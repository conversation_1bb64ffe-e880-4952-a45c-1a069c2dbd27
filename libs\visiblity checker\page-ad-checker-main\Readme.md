# Puppeteer-URL-Queue mit Redis, BullMQ und BullBoard

Ein skalierbares Node.js-System zur automatisierten Verarbeitung von URLs mit Puppeteer, gesteuert über eine Redis-Queue mit BullMQ. Visualisierung und Monitoring über BullBoard.

## 🚀 Features

- 🧠 **Asynchrone Verarbeitung** von URLs mit Puppeteer
- 💡 **BullMQ + Redis** als Messaging-System
- 📦 **Dockerized Microservices**

## 🛠 Projektstruktur

```bash
project/
├── producer/       # Fügt URLs in die Queue
├── worker/         # Holt Jobs aus der Queue und führt Puppeteer aus
├── docker-compose.yml
├── Makefile
└── README.md
