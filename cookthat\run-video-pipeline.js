import dotenv from "dotenv";
dotenv.config();

import { downloadTikTokVideo } from "./tiktok-download-video.js";
import { extractRecipeFromVideo } from "./gemini-extract-from-video.js";

async function run() {
  const url = process.argv[2];
  if (!url) {
    console.error("Usage: node cookthat/run-video-pipeline.js <tiktokUrl>");
    process.exit(1);
  }

  console.log("➡️  Downloading full video...");
  const { videoPath, description } = await downloadTikTokVideo(url);
  console.log("✅ Video saved:", videoPath);
  if (description) console.log("ℹ️  TikTok description:", description);

  console.log("➡️  Sending video to <PERSON> for recipe extraction...");
  const recipe = await extractRecipeFromVideo(videoPath, { description });
  console.log("✅ Recipe extracted:\n" + recipe);
}

run().catch((e) => {
  console.error("Pipeline failed:", e?.message || e);
  process.exit(1);
});

