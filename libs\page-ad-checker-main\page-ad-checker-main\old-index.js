// old implementations 


// Test change to verify volume mounting is working
console.log('Worker started with volume mounting test - FIXED ERROR 3');

const { Worker } = require('bullmq');
const IORedis = require('ioredis');
const puppeteer = require('puppeteer');

const connection = new IORedis({
  host: 'redis',
  port: 6379,
  maxRetriesPerRequest: null,
});

const calculateHeadingDistances = () => {
  // Selektiere alle relevanten Elemente in Erscheinungsreihenfolge
  const elements = Array.from(document.querySelectorAll("h1, h2, h3, h4, h5, h6, p"));

  // Extrahiere Position und Typinformationen
  const getElementDetails = el => {
    const rect = el.getBoundingClientRect();
    const scrollTop = window.scrollY || window.pageYOffset;
  
    return {
      tag: el.tagName,
      text: el.textContent.trim(),
      top: rect.top + scrollTop,
      bottom: rect.bottom + scrollTop,
      isHeading: el.tagName.startsWith('H')
    };
  };
  

  const elementDetails = elements.map(getElementDetails);

  // Sortiere Elemente nach vertikaler Position im Viewport
  elementDetails.sort((a, b) => a.top - b.top);

  const result = [];
  let currentHeading = null;
  let currentSectionElements = [];

  for (let i = 0; i < elementDetails.length; i++) {
    const el = elementDetails[i];

    if (el.isHeading) {
      if (currentHeading) {
        const sectionEnd = currentSectionElements.length > 0
          ? currentSectionElements[currentSectionElements.length - 1].bottom
          : currentHeading.bottom;

        const sectionHeight = sectionEnd - currentHeading.top;
        const distanceToNextHeading = el.top - currentHeading.bottom;

        result.push({
          heading: {
            tag: currentHeading.tag,
            text: currentHeading.text,
            position: currentHeading.top
          },
          elements: currentSectionElements,
          distanceToNextHeading,
          sectionHeight
        });
      }

      currentHeading = el;
      currentSectionElements = [];
    } else {
      currentSectionElements.push({
        tag: el.tag,
        text: el.text,
        position: el.top,
        bottom: el.bottom
      });
    }

    // Letztes Element: finalisiere die letzte Section
    const isLastElement = i === elementDetails.length - 1;
    if (isLastElement && currentHeading) {
      const sectionEnd = currentSectionElements.length > 0
        ? currentSectionElements[currentSectionElements.length - 1].bottom
        : currentHeading.bottom;

      const sectionHeight = sectionEnd - currentHeading.top;

      result.push({
        heading: {
          tag: currentHeading.tag,
          text: currentHeading.text,
          position: currentHeading.top
        },
        elements: currentSectionElements,
        distanceToNextHeading: null,
        sectionHeight
      });
    }
  }

  return result;
};

const checkAdVisibility = (adIds, requirements) => {
  const results = {};
  
  for (const adSlot of Object.keys(adIds)) {
    const adId = adIds[adSlot];
    const adContainer = document.getElementById(adId);
    if (!adContainer) {
      results[adId] = { found: false, message: `Ad container with ID ${adId} not found` };
      continue;
    }

    const adIframe = adContainer.querySelector('iframe[id^="adspiritflash"]');
    if (!adIframe) {
      results[adId] = {
        found: false,
        containerFound: true,
        message: 'Iframe with ID starting with adspiritflash not found within container'
      };
      continue;
    }

    const rect = adIframe.getBoundingClientRect();

    const isInViewport = (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );

    const style = window.getComputedStyle(adIframe);
    const isVisible = style.display !== 'none' &&
                      style.visibility !== 'hidden' &&
                      style.opacity !== '0';

    const hasValidDimensions = rect.width > 50 && rect.height > 40;
    
    // Check if dimensions match the minimum requirements for this ad slot
    const meetsMinDimensions = requirements[adSlot] ? 
      rect.width >= requirements[adSlot].minWidth && 
      rect.height >= requirements[adSlot].minHeight : 
      true;

    const isPointVisible = (x, y) => {
      const el = document.elementFromPoint(x, y);
      return el && (adIframe.contains(el) || adContainer.contains(el));
    };

    const checkPoints = [
      [rect.left + 1, rect.top + 1],                                   
      [rect.right - 1, rect.top + 1],                                 
      [rect.left + 1, rect.bottom - 1],                               
      [rect.right - 1, rect.bottom - 1],                              
      [rect.left + rect.width / 2, rect.top + rect.height / 2]        
    ];

    const visiblePoints = checkPoints.filter(([x, y]) => isPointVisible(x, y));
    const coverageRatio = visiblePoints.length / checkPoints.length;

    const isFullyVisible = coverageRatio < 0.1;

    results[adId] = {
      found: true,
      isInViewport,
      isVisible,
      hasValidDimensions,
      meetsMinDimensions,
      coverageRatio,
      isFullyVisible: isVisible && hasValidDimensions && isFullyVisible && meetsMinDimensions,
      isInViewportFullyVisible: isInViewport && isVisible && hasValidDimensions && isFullyVisible && meetsMinDimensions,
      dimensions: {
        width: rect.width,
        height: rect.height
      },
      position: {
        top: rect.top,
        left: rect.left,
        bottom: rect.bottom,
        right: rect.right
      }
    };
  }
  
  return results;
};

const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};

const checkSelectors = async (page) => {
  const qmnSelectors = await page.evaluate(() => {
    return window.qmn;
  });

  if(!qmnSelectors) return {
    desktopBillboardPlaceable: false,
    desktopBrochureadPlaceable: false,
    watchbetterPlaceable: false
  } 

  const {desktop_billboard_html_selector, desktop_brochuread_html_selector, watchbetter_html_selector} = qmnSelectors;

  const desktopBillboardPlaceable = await page.evaluate((selector) => {
    return document.querySelectorAll(selector).length > 0;
  }, desktop_billboard_html_selector);

  const desktopBrochureadPlaceable = await page.evaluate((selector) => {
    return document.querySelectorAll(selector).length > 0;
  }, desktop_brochuread_html_selector);

  const watchbetterPlaceable = await page.evaluate((selector) => {
    return document.querySelectorAll(selector).length > 0;
  }, 'body.home p:nth-of-type(4), body.post-template-default .instagram-media, h3:nth-of-type(7), h3:nth-of-type(6), h3:nth-of-type(2)');  

  if(!watchbetterPlaceable){
    console.log('❌ Watchbetter Placeable not found', watchbetter_html_selector);
  }

  return {
    desktopBillboardPlaceable,
    desktopBrochureadPlaceable,
    watchbetterPlaceable
  }
}


const adVisibilityChecks = {
  sitebar: {
    minWidth: 100,
    minHeight: 100,
  },
  hpa: {
    minWidth: 300,
    minHeight: 600,
  },
  intext: {
    minWidth: 800,
    minHeight: 250,
  },
  floor: {
    minWidth: 100,
    minHeight: 100,
  },
}


const worker = new Worker(
  'url-jobs',
  async (job) => {
    const url = job.data.url;
    console.log(`📥 Starte Analyse: ${url}`);

    const startTime = Date.now();

    const browser = await puppeteer.launch({
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();

    // Debug console logs from puppeteer
    // page.on('console', (msg) => {
    //   const type = msg.type();
    //   const text = msg.text();
    //   console.log(`[Client ${type}] ${text}`);
    // });

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });

      // Accept cookie consent if present
      const cookieConsentAccepted = await acceptCookieConsent(page);
      if (cookieConsentAccepted === 1) {
        console.log('Cookie consent accepted');
        await new Promise(resolve => setTimeout(resolve, 2000)); 
      } else {
        throw new Error('No cookie consent found '+cookieConsentAccepted);
      }

      const distances = await page.evaluate(calculateHeadingDistances);

      const qmnSelectors = await checkSelectors(page);

      // Scroll to the bottom of the page
      await page.evaluate(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth'
        });
      });
      // Wait a moment for any lazy-loaded content to appear
      await new Promise(resolve => setTimeout(resolve, 500));
      // Scroll to the bottom of the page again to ensure all content is loaded
      await page.evaluate(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth'
        });
      });
      
      // Wait a moment for any lazy-loaded content to appear
      await new Promise(resolve => setTimeout(resolve, 2000));

      const adResults = await page.evaluate(checkAdVisibility, {
        sitebar: 'qmn2400',
        hpa: 'qmn2397', 
        intext: 'qmn2396',
        floor: 'qmn2399'
      }, adVisibilityChecks);

      const endTime = Date.now();
      const durationMs = endTime - startTime;

      return {
        url,
        distances,
        adResults,
        qmnSelectors,
        cookieConsentAccepted,
        timestamp: new Date().toISOString(),
        processingTimeMs: durationMs
      }

    } catch (e) {
      console.error(`❌ Fehler bei ${url}:`, e.message);
    } finally {
      await browser.close();
    }
  },
  { connection }
);

worker.on('completed', (job) => {
  console.log(`🎉 Job abgeschlossen: ${job.id}`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job fehlgeschlagen: ${job.id} - ${err.message}`);
});
