.PHONY: build up down logs restart

build:
	docker compose build

up:
	docker compose up -d --build

down:
	docker compose down

logs:
	docker compose logs -f --tail=100

restart:
	docker compose down && docker compose up -d && docker compose logs -f --tail=100

open-bullboard:
	open http://localhost:3000

rw:
	docker compose stop worker && docker compose rm -f worker && docker compose up -d worker && docker compose logs -f --tail=100
