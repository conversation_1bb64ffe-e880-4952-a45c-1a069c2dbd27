// Test change to verify volume mounting is working

console.log("Worker started with volume mounting test - FIXED ERROR 3");

import {Worker} from "bullmq";
import IORedis from "ioredis";
import puppeteer from "puppeteer";
import {
  acceptCookieConsent,
  groupAdsByResponsive,
  checkSelectors,
  findMissingAds,
} from "./utils.js";



const connection = new IORedis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT, 10),
  maxRetriesPerRequest: null,
});

const viewports = {
  mobile: { width: 375, height: 812, deviceType: "mobile", name: "Mobile" },
  tablet: { width: 768, height: 1024, deviceType: "desktop", name: "Tablet" },
  desktop: { width: 1920, height: 1080, deviceType: "desktop", name: "Desktop HD" },
};

// This function runs the check for a single device configuration.
// It creates its own page, sets the viewport, and performs the ad check.
async function runCheckForDevice(browser, url, deviceName, config) {
    console.log(`\n--- Teste Gerät: ${config.name} (${config.width}x${config.height}) ---`);
    const page = await browser.newPage();
    try {
        await page.setViewport({ width: config.width, height: config.height });
        await page.goto(url, { waitUntil: "networkidle2" });

        const cookieConsentAccepted = await acceptCookieConsent(page);
        if (cookieConsentAccepted !== 1) {
            console.error(`[${config.name}] Cookie-Zustimmung fehlgeschlagen oder nicht gefunden: ${cookieConsentAccepted}`);
        } else {
            console.log(`[${config.name}] Cookie-Zustimmung akzeptiert, warte auf mögliche Overlays...`);
            await new Promise((resolve) => setTimeout(resolve, 2000));
        }

        const qmnSelectors = await checkSelectors(page);
        if (!qmnSelectors) {
            throw new Error(`[${config.name}] window.qmn.config-Objekt konnte nicht abgerufen werden.`);
        }
        const adsByResponsive = groupAdsByResponsive(qmnSelectors);

        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Überprüfe folgende Anzeigen für Responsive-Typ '${responsiveKey}':`, JSON.stringify(adsToCheck, null, 2));

        if (adsToCheck.length === 0) {
            console.log(`[${deviceName}] Keine Anzeigen für den Gerätetyp '${responsiveKey}' gefunden.`);
            return { responsiveKey, missingAdsForDevice: [], deviceName };
        }

        const missingAdsForDevice = await page.evaluate(findMissingAds, adsToCheck);

        if (missingAdsForDevice.length > 0) {
            console.log(`[${deviceName}] Fehlende Anzeigen:`, missingAdsForDevice);
        } else {
            console.log(`[${deviceName}] Alle ${adsToCheck.length} Anzeigen wurden erfolgreich gefunden.`);
        }

        return { responsiveKey, missingAdsForDevice, deviceName };
    } catch (e) {
        console.error(`❌ Fehler bei Gerät ${deviceName}:`, e.message);
        return {
            responsiveKey: config.deviceType,
            missingAdsForDevice: [],
            deviceName,
            error: e.message
        };
    } finally {
        await page.close();
    }
}


const worker = new Worker(
  "url-jobs",
  async (job) => {
    const url = job.data.url;
    console.log(`📥 Starte Analyse: ${url}`);

    const startTime = Date.now();

    const browser = await puppeteer.launch({
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      headless: "new",
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    try {
      // Run checks for all devices in parallel
      const devicePromises = Object.entries(viewports).map(([deviceName, config]) =>
        runCheckForDevice(browser, url, deviceName, config)
      );
      
      const deviceResults = await Promise.all(devicePromises);

      // Aggregate results from all parallel checks
      const missingAds = {
        desktop: new Map(),
        mobile: new Map(),
      };
      let allAdsSuccessfullyPlaced = true;
      const deviceErrors = [];

      for (const result of deviceResults) {
        if (result.error) {
            deviceErrors.push({ device: result.deviceName, error: result.error });
            allAdsSuccessfullyPlaced = false;
            continue;
        }
        if (result.missingAdsForDevice.length > 0) {
          allAdsSuccessfullyPlaced = false;
          result.missingAdsForDevice.forEach((ad) =>
            missingAds[result.responsiveKey].set(ad.id, ad)
          );
        }
      }
      
      const endTime = Date.now();
      const durationMs = endTime - startTime;

      let finalResult;

      if (allAdsSuccessfullyPlaced) {
        finalResult = {
          success: true,
          url,
          timestamp: new Date().toISOString(),
          processingTimeMs: durationMs,
        };
      } else {
        finalResult = {
          success: false,
          missingAds: {
            desktop: Array.from(missingAds.desktop.values()),
            mobile: Array.from(missingAds.mobile.values()),
          },
          errors: deviceErrors,
          url,
          timestamp: new Date().toISOString(),
          processingTimeMs: durationMs,
        };
      }

      console.log("final result 📈 :", JSON.stringify(finalResult, null, 2));

      return finalResult;
    } catch (e) {
      console.error(`❌ Fehler bei ${url}:`, e.message);
      return {
        url,
        error: e.message,
        timestamp: new Date().toISOString(),
      };
    } finally {
      await browser.close();
    }
  },
  { connection }
);

worker.on("completed", (job) => {
  console.log(`🎉 Job abgeschlossen: ${job.id}`);
});

worker.on("failed", (job, err) => {
  console.error(`❌ Job fehlgeschlagen: ${job.id} - ${err.message}`);
});
