import { GoogleGenerativeAI } from "@google/generative-ai";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";

// Try loading env from current dir and parent dir (helps when running from cookthat/)
dotenv.config();
if (!process.env.GOOGLE_API_KEY && !process.env.GEMINI_API_KEY && !process.env.GOOGLE_GENAI_API_KEY) {
  try { dotenv.config({ path: path.resolve(process.cwd(), "..", ".env") }); } catch {}
}

function mimeFromExt(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case ".mp4":
      return "video/mp4";
    case ".mov":
      return "video/quicktime";
    case ".webm":
      return "video/webm";
    default:
      return "application/octet-stream";
  }
}

function fileToGenerativePart(filePath) {
  const mimeType = mimeFromExt(filePath);
  const data = fs.readFileSync(filePath);
  const base64 = data.toString("base64");
  return {
    inlineData: {
      data: base64,
      mimeType,
    },
  };
}

const systemPrompt = `You are an expert culinary assistant. You will receive a cooking video (e.g., TikTok). Extract a clean, structured recipe . If there is insufficient info, make minimal reasonable assumptions.

Output requirements:
- Keep measurements and quantities if mentioned.
- Keep the order of steps as presented.
- Include time and servings only if clearly stated.
- Use concise, clear language.
`;

export async function extractRecipeFromVideo(videoFilePath, opts = {}) {
  const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
  if (!apiKey) throw new Error("Missing Google AI API key in env");

  const { description } = opts;
  const genAI = new GoogleGenerativeAI(apiKey);
  // Use a vision-capable Gemini model
  const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

  const videoPart = fileToGenerativePart(videoFilePath);
  const userPrompt = `Return only JSON matching the schema keys: z.object({
    title: z.string().min(1).max(200),
    servings: z.number().int().positive().nullable().optional(),
    total_time_minutes: z.number().int().positive().nullable().optional(),
    ingredients: z
      .array(
        z.object({
          quantity: z.string().optional(),
          unit: z.string().optional(),
          item: z.string().min(1),
          notes: z.string().optional(),
        })
      )
      .default([]),
    steps: z.array(z.string().min(1)).default([])
    notes: z.string().optional(),
  }). No prose.`;

  const parts = [{ text: systemPrompt }];
  if (description && description.trim()) {
    parts.push({ text: `Video description (may include ingredients/steps):\n${description}` });
  }
  parts.push(videoPart, { text: userPrompt });

  const result = await model.generateContent({
    contents: [
      { role: "user", parts },
    ],
  });

  const text = result.response.text();
  return text;
}

// Direct run helper
if (import.meta.url === `file://${process.argv[1]}`) {
  const filePath = process.argv[2];
  if (!filePath) {
    console.error("Usage: node cookthat/gemini-extract-from-video.js <videoFile>");
    process.exit(1);
  }
  extractRecipeFromVideo(filePath)
    .then((r) => console.log(r))
    .catch((e) => {
      console.error("Extraction failed:", e?.message || e);
      process.exit(1);
    });
}

