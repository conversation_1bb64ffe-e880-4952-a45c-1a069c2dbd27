<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lange Testseite für Sichtbarkeitsprüfung</title>
    <style>
        body { font-family: sans-serif; position: relative; }
        h1 { text-align: center; }
        #elements-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            position: relative; /* Wichtig für absolute Positionierung innen */
        }
        .item {
            height: 150px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            box-sizing: border-box;
        }
    </style>
</head>
<body style="background-color: blanchedalmond;" data-selector-to-check="#elements-container > .item">
    <h1>Lange Testseite für Sichtbarkeitsprüfung</h1>
    <div id="elements-container">
        <!-- Elemente werden per Skript generiert -->
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('elements-container');
            const totalItems = 100;

            for (let i = 0; i < totalItems; i++) {
                const div = document.createElement('div');
                div.id = `item-${i}`;
                div.className = 'item';
                
                let content = `Element ${i}`;
                let isExpectedVisible = true; // Standardmäßig sichtbar

                // Anwenden verschiedener Stile für Testzwecke
                if (i % 7 === 0) {
                    div.style.visibility = 'hidden'; // Versteckt
                    content += ' (visibility: hidden)';
                    isExpectedVisible = false;
                }
                if (i % 11 === 0) {
                    div.style.display = 'none'; // Nicht im Layout
                    content += ' (display: none)';
                    isExpectedVisible = false;
                }
                if (i % 5 === 0 && i % 11 !== 0) {
                    div.style.opacity = '0.1'; // Teilweise transparent, aber per CSS sichtbar
                    content += ' (opacity: 0.1)';
                }
                if (i === 20) {
                    div.style.height = '1200px'; // Ein sehr hohes Element
                    content += ' (sehr hoch)';
                }
                if (i === 30) {
                    // Ein Element, das von einem anderen überdeckt wird
                    div.style.position = 'relative';
                    div.style.zIndex = '1';
                    content += ' (überdeckt)';
                    isExpectedVisible = false; // Verdeckt, also nicht wirklich sichtbar

                    const overlay = document.createElement('div');
                    overlay.style.position = 'absolute';
                    overlay.style.top = '0';
                    overlay.style.left = '0';
                    overlay.style.width = '100%';
                    overlay.style.height = '100%';
                    overlay.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
                    overlay.textContent = 'OVERLAY';
                    overlay.style.color = 'white';
                    overlay.style.zIndex = '2';
                    overlay.style.display = 'flex';
                    overlay.style.alignItems = 'center';
                    overlay.style.justifyContent = 'center';
                    div.appendChild(overlay);
                }
                if (i === 40) {
                    div.style.position = 'absolute';
                    div.style.top = '5000px'; // Platziert es an einer bestimmten Stelle auf der Seite
                    div.style.left = '100px';
                    div.style.width = '300px';
                    div.style.backgroundColor = 'lightblue';
                    div.style.zIndex = '10';
                    content += ' (position: absolute)';
                    isExpectedVisible = true; // Sollte beim Scrollen sichtbar sein
                }
                if (i === 50) {
                    div.style.position = 'relative';
                    div.style.left = '-650px'; // Schiebt es 50px nach links, teilweise aus dem Container
                    div.style.width = '100%';
                    div.style.backgroundColor = 'lightgreen';
                    content += ' (teilweise außerhalb)';
                    isExpectedVisible = false; // Kann nie zu 100% sichtbar sein (threshold: 1.0)
                }
                
                div.textContent = content;
                // Die "Wahrheitsquelle" als data-Attribut setzen
                div.dataset.expectedVisibility = isExpectedVisible;
                container.appendChild(div);
            }
        });
    </script>
</body>
</html>
