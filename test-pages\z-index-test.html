<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Z-Index Sichtbarkeits-Test</title>
    <style>
        body {
            font-family: sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            position: relative;
            border: 2px dashed blue;
            padding: 20px;
            margin: 20px;
            height: 200px;
        }
        .item {
            position: absolute;
            width: 250px;
            height: 120px;
            border: 1px solid black;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            box-sizing: border-box;
            padding: 10px;
            text-align: center;
        }
        .info {
            margin: 20px;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body data-selector-to-check=".item">

   <!--  <div class="info">
        <h1>Z-Index und Stacking Context Testfälle</h1>
        <p>Diese Seite testet, ob die Sichtbarkeit von Elementen korrekt erkannt wird, wenn sie von anderen Elementen aufgrund von <code>z-index</code> und Stacking Contexts verdeckt werden.</p>
        <p><b>Hinweis:</b> Standard-APIs wie <code>checkVisibility</code> erkennen in der Regel <b>keine</b> Verdeckung durch andere Elemente. Diese Tests sollen diese Lücke aufzeigen.</p>
    </div>
 -->
    <!-- Testfall 1: Einfache Überlappung -->
    <div class="container">
        <div id="item-1-covered" class="item" style="top: 20px; left: 20px; background-color: lightcoral; z-index: 2;" data-expected-visibility="false">
            Item 1 (Verdeckt)<br>z-index: 1
        </div>
        <div id="item-2-cover" class="item" style="top: 20px; left: 20px; background-color: lightblue; z-index: 2;" data-expected-visibility="true">
            Item 2 (Verdeckend)<br>z-index: 2
        </div>
    </div>

    <!-- Testfall 2: Nested Stacking Context (umgestaltet) -->
    <div class="container">
        <!-- Dieser Wrapper erzeugt einen neuen Stacking Context mit z-index: 5 -->
        <div style="position: relative; z-index: 5; top: 0; left: 0; width: 100%; height: 100%;">
            <div id="item-3-nested-covered" class="item" style="top: 20px; left: 20px; background-color: lightgreen; z-index: 100;" data-expected-visibility="false">
                Item 3 (Verschachtelt & Verdeckt)<br>z-index: 100 in Context mit z-index: 5
            </div>
        </div>
        <!-- Dieses Element liegt im selben Stacking Context wie der Wrapper oben und sollte ihn überdecken, da sein z-index (6) höher ist. -->
        <div id="item-4-cover" class="item" style="top: 20px; left: 20px; background-color: orange; z-index: 6;" data-expected-visibility="true">
            Item 4 (Verdeckend)<br>z-index: 6
        </div>
    </div>

    <!-- Testfall 3: Negative z-index -->
    <div class="container" style="background-color: white; position: relative; z-index: 0;">
        <div id="item-5-negative-z" class="item" style="top: 20px; left: 20px; background-color: violet; z-index: -1;" data-expected-visibility="false">
            Item 5 (Negativer Z-Index)<br>z-index: -1
        </div>
        <p style="position: relative; z-index: auto; padding: 10px; font-size: 1.2em;">Dieser Text im Container sollte Item 5 überdecken, da es einen negativen z-index hat.</p>
    </div>

    <!-- Testfall 4: Gleicher z-index, DOM-Reihenfolge entscheidet -->
    <div class="container">
        <div id="item-6-dom-order-1" class="item" style="top: 20px; left: 20px; background-color: lightgoldenrodyellow; z-index: 3;" data-expected-visibility="false">
            Item 6 (DOM-Reihenfolge 1)<br>z-index: 3
        </div>
        <div id="item-7-dom-order-2" class="item" style="top: 20px; left: 20px; background-color: lightseagreen; z-index: 3;" data-expected-visibility="true">
            Item 7 (DOM-Reihenfolge 2)<br>z-index: 3
        </div>
    </div>

    <!-- Testfall 5: Unverdecktes Element -->
    <div class="container">
        <div id="item-8-visible" class="item" style="top: 20px; left: 20px; background-color: white;" data-expected-visibility="true">
            Item 8 (Klar sichtbar)
        </div>
    </div>

</body>
</html>
