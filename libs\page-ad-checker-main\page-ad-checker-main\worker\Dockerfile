FROM node:18

RUN apt-get update && \
    apt-get install -y \
      chromium \
      ca-certificates \
      fonts-liberation \
      libappindicator3-1 \
      libasound2 \
      libatk-bridge2.0-0 \
      libnspr4 \
      libnss3 \
      libxss1 \
      xdg-utils \
      --no-install-recommends && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

WORKDIR /app

# Copy package.json and install dependencies
COPY package.json .
RUN npm install

CMD ["node", "index.js"]
