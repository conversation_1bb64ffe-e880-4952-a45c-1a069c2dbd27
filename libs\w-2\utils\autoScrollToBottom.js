export const autoScrollToBottom = async (page) => {
    await page.evaluate(async () => {
        await new Promise(resolve => {
            let lastScrollHeight = 0;
            let scrollAttempts = 0;
            const maxScrollAttempts = 5; // Number of times to try scrolling without new content

            const scroll = () => {
                const currentScrollHeight = document.body.scrollHeight;
                window.scrollTo({ top: currentScrollHeight, behavior: 'smooth' }); // Added smooth behavior

                if (currentScrollHeight === lastScrollHeight) {
                    scrollAttempts++;
                } else {
                    scrollAttempts = 0; // Reset if new content loaded
                }

                lastScrollHeight = currentScrollHeight;

                if (scrollAttempts >= maxScrollAttempts) {
                    resolve(); // End of page reached
                } else {
                    setTimeout(scroll, 200); // Wait a bit and try again
                }
            };
            scroll();
        });
    });

    // make sure that all content on the webpage has been loaded 
    // await new Promise(resolve => setTimeout(resolve, 3000));
};
