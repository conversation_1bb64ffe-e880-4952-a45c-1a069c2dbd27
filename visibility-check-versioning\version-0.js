" V"


const puppeteer = require('puppeteer');
const path = require('path');

// --- KORRIGIERTE Logik für Methode 5: Statische Analyse + elementFromPoint (wird im Browser-Kontext ausgeführt) ---
const staticWithOcclusionCheckLogic = (selector) => {
    const elements = Array.from(document.querySelectorAll(selector));
    const results = [];
    const pageScrollHeight = document.body.scrollHeight;
    const pageScrollWidth = document.body.scrollWidth;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    
    for (const el of elements) {
        const rect = el.getBoundingClientRect();
        
        // 1. Grundlegende Render-Prüfung
        const isRendered = rect.width > 0 && rect.height > 0;

        // 2. Prüfung der Seitengrenzen
        const scrollTop = window.scrollY || window.pageYOffset;
        const scrollLeft = window.scrollX || window.pageXOffset;
        const isWithinPageBounds = isRendered &&
            (rect.top + scrollTop) >= 0 &&
            (rect.left + scrollLeft) >= 0 &&
            (rect.bottom + scrollTop) <= pageScrollHeight &&
            (rect.right + scrollLeft) <= pageScrollWidth;

        // 3. Robuste CSS-Sichtbarkeitsprüfung
        let isVisibleByCss = false;
        if (isRendered) {
            if (typeof el.checkVisibility === 'function') {
                isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
            } else {
                // Fallback für ältere Browser oder Kontexte ohne checkVisibility
                const style = window.getComputedStyle(el);
                isVisibleByCss = style && style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
            }
        }
        
        let isOccluded = false;
        // 4. Verdeckungsprüfung mit elementFromPoint, NUR wenn im aktuellen Viewport
        const isInViewport = rect.top < viewportHeight && rect.bottom > 0 && rect.left < viewportWidth && rect.right > 0;

        if (isVisibleByCss && isInViewport) {
            const samplePoints = [
                // 3x3-Raster für eine gründlichere Prüfung
                { x: rect.left + 1, y: rect.top + 1 },                             // Oben-links
                { x: rect.left + rect.width / 2, y: rect.top + 1 },                 // Oben-mitte
                { x: rect.right - 1, y: rect.top + 1 },                            // Oben-rechts
                { x: rect.left + 1, y: rect.top + rect.height / 2 },               // Mitte-links
                { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 }, // Mitte-mitte (Zentrum)
                { x: rect.right - 1, y: rect.top + rect.height / 2 },              // Mitte-rechts
                { x: rect.left + 1, y: rect.bottom - 1 },                          // Unten-links
                { x: rect.left + rect.width / 2, y: rect.bottom - 1 },              // Unten-mitte
                { x: rect.right - 1, y: rect.bottom - 1 }                           // Unten-rechts
            ];

            const visibilityResults = samplePoints.map(point => {
                if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                    const elementAtPoint = document.elementFromPoint(point.x, point.y);
                    return elementAtPoint === el;
                }
                return false;
            });

            // console.log(`Visibility Matrix for ${el.id}:`, visibilityResults);

            const visiblePoints = visibilityResults.filter(isVisible => isVisible).length;
            const visibilityPercentage = (visiblePoints / samplePoints.length) * 100;
            
            // console.log(`Visibility Percentage for ${el.id}: ${visibilityPercentage}%`);

            // Ein Element gilt als verdeckt, wenn nicht 100% der Prüfpunkte sichtbar sind.
            if (visibilityPercentage < 100) {
                isOccluded = true;
            }
        }

        // Endergebnis: Muss auf der Seite sein, per CSS sichtbar und nicht verdeckt.
        const isTrulyVisible = isWithinPageBounds && isVisibleByCss && !isOccluded;

        results.push({ id: el.id, isWithinPageBounds, isVisibleByCss, isOccluded, isTrulyVisible });
    }
    return results;
};


// --- Hilfsfunktion zur Genauigkeitsmessung ---
function calculateAndDisplayAccuracy(reportMap, groundTruthMap) {
    let correctCount = 0;
    let incorrectDetections = [];

    for (const [id, expectedVisibility] of groundTruthMap.entries()) {
        const report = reportMap.get(id);
        if (report) {
            if (report.wasEverTrulyVisible === expectedVisibility) {
                correctCount++;
            } else {
                incorrectDetections.push({
                    id,
                    expected: expectedVisibility,
                    got: report.wasEverTrulyVisible
                });
            }
        }
    }
    
    const totalCount = groundTruthMap.size;
    const accuracy = totalCount > 0 ? (correctCount / totalCount) * 100 : 0;
    console.log(`\nGenauigkeit: ${accuracy.toFixed(2)}% (${correctCount}/${totalCount} korrekt)`);

    if (incorrectDetections.length > 0) {
        console.log('  Fehlerhafte Erkennungen:');
        for (const item of incorrectDetections) {
            console.log(`    - ${item.id}: Erwartet=${item.expected}, Erhalten=${item.got}`);
        }
    }
}


// --- NEUER Test-Runner für statische Methode mit Occlusion Check ---
async function runStaticWithOcclusionCheckTest(page, testName, checkFunction) {
    console.log(`\n--- Starte Test: ${testName} ---`);
    console.time(testName);

    await page.reload({ waitUntil: 'networkidle0' });
    // FÜGE EINE KURZE VERZÖGERUNG HINZU, um Rendering-Probleme bei komplexen Stacking Contexts zu vermeiden.
    await new Promise(resolve => setTimeout(resolve, 100));

    const selector = await page.evaluate(() => document.body.dataset.selectorToCheck);
    
    const groundTruthData = await page.$$eval(selector, elements => 
        elements.map(el => [el.id, el.dataset.expectedVisibility === 'true'])
    );
    const groundTruthMap = new Map(groundTruthData);

    const batchResults = await page.evaluate(checkFunction, selector);

    const visibilityReport = new Map();
    for (const result of batchResults) {
        // Die Logikfunktion liefert jetzt alle Details.
        visibilityReport.set(result.id, {
            wasEverInViewport: result.isWithinPageBounds,
            isVisibleByCss: result.isVisibleByCss,
            isOccluded: result.isOccluded,
            wasEverTrulyVisible: result.isTrulyVisible
        });
    }

    console.log('Ergebnisse (für alle Elemente):');
    const sortedIds = [...visibilityReport.keys()].sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)[0], 10);
        const numB = parseInt(b.match(/\d+/)[0], 10);
        return numA - numB;
    });

    for (const id of sortedIds) {
        const report = visibilityReport.get(id);
        if (report) {
            console.log(`  Element: ${id.padEnd(20)} | Innerhalb Seite: ${String(report.wasEverInViewport).padEnd(5)} | CSS Sichtbar: ${String(report.isVisibleByCss).padEnd(5)} | Verdeckt: ${String(report.isOccluded).padEnd(5)} | Endergebnis: ${report.wasEverTrulyVisible}`);
        }
    }

    calculateAndDisplayAccuracy(visibilityReport, groundTruthMap);
    console.timeEnd(testName);
}

// --- Hauptausführung ---

(async () => {
    const browser = await puppeteer.launch({ headless: true });

    // --- Tests auf der "long-page.html" ---
    console.log('\n=========================================================');
    console.log('=== Starte Tests auf der "long-page.html"           ===');
    console.log('=========================================================');
    {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });
        const longTestPagePath = path.join(__dirname, 'test-pages', 'long-page.html');
        await page.goto(`file://${longTestPagePath}`, { waitUntil: 'networkidle0' });

        // NEUER Test 5: Statische Analyse mit Occlusion Check
        await runStaticWithOcclusionCheckTest(page, 'Statische Analyse + Occlusion Check', staticWithOcclusionCheckLogic);
        
        await page.close();
    }

    // --- NEUE TESTS auf der "z-index-test.html" ---
    console.log('\n=========================================================');
    console.log('=== Starte Tests auf der "z-index-test.html"        ===');
    console.log('=========================================================');
    {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });
        const zIndexTestPagePath = path.join(__dirname, 'test-pages', 'z-index-test.html');
        await page.goto(`file://${zIndexTestPagePath}`, { waitUntil: 'networkidle0' });
        
        // NEUER Test 5: Statische Analyse mit Occlusion Check
        await runStaticWithOcclusionCheckTest(page, 'Statische Analyse + Occlusion Check', staticWithOcclusionCheckLogic);

        await page.close();
    }

    await browser.close();
    
})();
