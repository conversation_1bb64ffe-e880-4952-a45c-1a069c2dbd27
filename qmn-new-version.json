{"config": {"displayMode": "production", "excludedPaths": [], "options": {"adLabel": {"enabled": true, "text": "Werbung", "style": {"color": "#333333", "fontSize": ".7rem", "textAlign": "center", "padding": "5px 5px 2px 5px", "textTransform": "uppercase", "fontWeight": "bold"}}}, "adSlots": [{"id": "5003", "type": "tracker", "width": "1", "height": "1", "responsive": "desktop"}, {"id": "5000", "type": "tracker", "width": "1", "height": "1", "responsive": "mobile"}, {"id": "5001", "type": "intext", "width": "800", "height": "250", "responsive": "desktop", "lazyload": true, "position": {"htmlSelectors": ".uk-grid-column-small, body.top-goldenratio .uk-grid-column-small, body.option-com_content.view-category article h2:nth-of-type(2), #module-1655, article h2:nth-of-type(2), article h3:nth-of-type(2)", "htmlPosition": "before"}, "css": {"marginBottom": "20px", "display": "flex", "justifyContent": "center", "alignItems": "center", "height": "275px", "flexDirection": "column"}}, {"id": "5004", "type": "floorad", "width": "300", "height": "600", "responsive": "desktop", "css": {}}, {"id": "5002", "type": "prospekt", "width": "300", "height": "600", "responsive": "desktop", "lazyload": true, "position": {"htmlSelectors": ".uk-grid.tm-grid-expand:nth-of-type(4), article h2:nth-of-type(10), .uk-card", "htmlPosition": "before"}, "css": {}}, {"id": "4997", "type": "hpa", "width": "300", "height": "600", "responsive": "mobile", "lazyload": true, "position": {"htmlSelectors": ".uk-margin-medium-top h2:nth-of-type(2), .uk-margin-medium-top h3:nth-of-type(2), .uk-container h2:nth-of-type(3), .uk-margin-medium-top h2, .uk-grid .uk-grid-margin, article h2:nth-of-type(6), h3:nth-of-type(2), .uk-margin-medium-top p:nth-of-type(10)", "htmlPosition": "before"}, "css": {}}, {"id": "4996", "type": "floorad", "width": "300", "height": "600", "responsive": "mobile", "position": {"htmlSelectors": "footer", "htmlPosition": "before"}, "css": {}}, {"id": "4999", "type": "hpa", "width": "300", "height": "600", "responsive": "mobile", "lazyload": true, "position": {"htmlSelectors": ".uk-grid .uk-grid-margin:nth-of-type(8), .uk-margin-medium-top h3:nth-of-type(4), .uk-flex.uk-flex-middle:nth-of-type(3), article h2:nth-of-type(3), h2:nth-of-type(4), .uk-margin-medium-top p:nth-of-type(18)", "htmlPosition": "before"}, "css": {}}, {"id": "4998", "type": "prospekt", "width": "300", "height": "600", "responsive": "mobile", "lazyload": true, "position": {"htmlSelectors": "#module-1665, .uk-margin-medium-top p:nth-of-type(40), article h2:nth-of-type(9), .tm-bottom, .uk-card", "htmlPosition": "before"}, "css": {}}, {"id": "5005", "type": "sitebar", "width": "300", "height": "600", "responsive": "desktop", "lazyload": false, "css": {}, "options": {"header_sticky_selector": "header", "footer_selector": "footer", "main_content_selector": "main", "is_left_sitebar": "0", "sitebar_max_width": "600", "is_sitebar_dynamic_placement": "0"}}], "watchbetter": {"players": [{"playerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playlist": "Tierchenwelt", "responsive": "desktop", "position": {"htmlSelectors": "body.option-com_content.view-article article h2:nth-of-type(3), body.option-com_content.view-article article h3:nth-of-type(3), .uk-grid.tm-grid-expand:nth-of-type(3) .uk-panel:nth-of-type(3), h2:nth-of-type(5), body.columns-2 h2:nth-of-type(4), body.columns-2 h3:nth-of-type(5), .tm-bottom .uk-container", "htmlPosition": "before"}, "css": {}}, {"playerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playlist": "Tierchenwelt", "responsive": "mobile", "position": {"htmlSelectors": ".uk-grid .uk-grid-margin:nth-of-type(15), .uk-margin-medium-top h2:nth-of-type(8), .uk-margin-medium-top h3:nth-of-type(8), h2:nth-of-type(5), .uk-margin-medium-top p:nth-of-type(28), .tm-bottom .uk-grid .uk-grid-margin:nth-of-type(4), #module-1712, .uk-card, footer", "htmlPosition": "before"}, "css": {}}]}}, "updateConfig": {}, "deviceType": "desktop", "header_sticky_selector": "", "footer_selector": "", "main_content_selector": "div.tm-page.uk-margin-auto:not(:has(#tm-sidebar)), #tm-main > div > div > div, div.tm-page.uk-margin-auto", "is_left_sitebar": "", "sitebar_max_width": "", "is_sitebar_dynamic_placement": "1", "adsLoaded": true, "scroll_to_top_selector": "", "sitebar_selector": "", "sitebar_right_offset": "", "sitebar_reposition": "#tm-sidebar div, div.tm-bottom.uk-section-default.uk-section.uk-section-small, footer", "display_below_selector": ""}