import Tik<PERSON> from "@tobyg74/tiktok-api-dl";
import fs from "fs";
import path from "path";
import axios from "axios";

/**
 * Download TikTok audio track (music) for a given TikTok URL.
 * Returns the absolute path to the saved audio file.
 *
 * Notes:
 * - Uses the first available music.playUrl provided by @tobyg74/tiktok-api-dl
 * - Saves to ./cookthat/downloads by default (created if missing)
 */
export async function downloadTikTokAudio(tiktokUrl, opts = {}) {
  if (!tiktokUrl) throw new Error("Missing TikTok URL");
  const outDir = opts.outputDir || path.resolve("cookthat", "downloads");
  await fs.promises.mkdir(outDir, { recursive: true });

  const result = await Tiktok.Downloader(tiktokUrl);
  if (result?.status !== "success" || !result?.result) {
    throw new Error("TikTok API DL did not return success");
  }

  console.log(JSON.stringify(result, null, 2));


  // Extract description/caption if available
  const description = result?.result?.desc ?? "";

  const audioUrls = result?.result?.music?.playUrl || [];
  if (!Array.isArray(audioUrls) || audioUrls.length === 0) {
    throw new Error("No audio URL found for this TikTok");
  }

  const audioUrl = audioUrls[0];
  const fileName = `tiktok_audio_${Date.now()}.mp3`;
  const filePath = path.join(outDir, fileName);

  const response = await axios.get(audioUrl, { responseType: "stream" });
  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);
    writer.on("finish", resolve);
    writer.on("error", reject);
  });

  return { filePath, description };
}

// If run directly: small manual test helper
if (import.meta.url === `file://${process.argv[1]}`) {
  const url = process.argv[2];
  if (!url) {
    console.error("Usage: node cookthat/tiktok-download.js <tiktokUrl>");
    process.exit(1);
  }
  downloadTikTokAudio(url)
    .then(({ filePath, description }) => {
      console.log("Audio saved:", filePath);
      console.log("Description:", description);
    })
    .catch((e) => {
      console.error("Download failed:", e?.message || e);
      process.exit(1);
    });
}

