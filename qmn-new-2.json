{"config": {"displayMode": "production", "excludedPaths": [], "adSlots": [{"id": "4629", "type": "tracker", "width": "1", "height": "1", "responsive": "desktop"}, {"id": "4630", "type": "floorad", "width": "300", "height": "600", "responsive": "desktop", "css": {}}, {"id": "4636", "type": "tracker", "width": "1", "height": "1", "responsive": "mobile"}, {"id": "4635", "type": "hpa", "width": "300", "height": "600", "responsive": "mobile", "lazyload": false, "position": {"htmlSelectors": "section.pagecontent > section.gridSection > section.three-block:nth-of-type(3), article > section[itemprop=\"articlebody\"], .recipeDetailContainer", "htmlPosition": "before"}, "css": {}}, {"id": "4632", "type": "floorad", "width": "300", "height": "600", "responsive": "mobile", "css": {}}, {"id": "4627", "type": "intext", "width": "800", "height": "250", "responsive": "desktop", "lazyload": true, "position": {"htmlSelectors": ".metainfo2, .ym-grid.gridSection, h2:nth-of-type(3), .premiumnetbox", "htmlPosition": "before"}, "css": {}}], "watchbetter": {"players": [{"playerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playlist": "Rezepte", "responsive": "desktop", "position": {"htmlPosition": "after"}, "css": {}}, {"playerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playlist": "Rezepte", "responsive": "mobile", "position": {"htmlPosition": "before"}, "css": {}}]}}, "updateConfig": {}, "deviceType": "desktop"}