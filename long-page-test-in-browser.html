<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lange Testseite für Sichtbarkeitsprüfung</title>
    <style>
        body { font-family: sans-serif; position: relative; }
        h1 { text-align: center; }
        #elements-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            position: relative; /* Wichtig für absolute Positionierung innen */
        }
        .item {
            height: 150px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            box-sizing: border-box;
        }
    </style>
</head>
<body style="background-color: blanchedalmond;" data-selector-to-check="#elements-container > .item">
    <h1>Lange Testseite für Sichtbarkeitsprüfung</h1>
    <div id="elements-container">
        <!-- Elemente werden per Skript generiert -->
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.getElementById('elements-container');
            const totalItems = 100;

            for (let i = 0; i < totalItems; i++) {
                const div = document.createElement('div');
                div.id = `item-${i}`;
                div.className = 'item';
                
                let content = `Element ${i}`;
                let isExpectedVisible = true; // Standardmäßig sichtbar

                // Anwenden verschiedener Stile für Testzwecke
                if (i % 7 === 0) {
                    div.style.visibility = 'hidden'; // Versteckt
                    content += ' (visibility: hidden)';
                    isExpectedVisible = false;
                }
                if (i % 11 === 0) {
                    div.style.display = 'none'; // Nicht im Layout
                    content += ' (display: none)';
                    isExpectedVisible = false;
                }
                if (i % 5 === 0 && i % 11 !== 0) {
                    div.style.opacity = '0.1'; // Teilweise transparent, aber per CSS sichtbar
                    content += ' (opacity: 0.1)';
                }
                if (i === 20) {
                    div.style.height = '1200px'; // Ein sehr hohes Element
                    content += ' (sehr hoch)';
                }
                if (i === 30) {
                    // Ein Element, das von einem anderen überdeckt wird
                    div.style.position = 'relative';
                    div.style.zIndex = '1';
                    content += ' (überdeckt)';
                    isExpectedVisible = false; // Verdeckt, also nicht wirklich sichtbar

                    const overlay = document.createElement('div');
                    overlay.style.position = 'absolute';
                    overlay.style.top = '0';
                    overlay.style.left = '0';
                    overlay.style.width = '100%';
                    overlay.style.height = '100%';
                    overlay.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
                    overlay.textContent = 'OVERLAY';
                    overlay.style.color = 'white';
                    overlay.style.zIndex = '2';
                    overlay.style.display = 'flex';
                    overlay.style.alignItems = 'center';
                    overlay.style.justifyContent = 'center';
                    div.appendChild(overlay);
                }
                if (i === 40) {
                    div.style.position = 'absolute';
                    div.style.top = '5000px'; // Platziert es an einer bestimmten Stelle auf der Seite
                    div.style.left = '100px';
                    div.style.width = '300px';
                    div.style.backgroundColor = 'lightblue';
                    div.style.zIndex = '10';
                    content += ' (position: absolute)';
                    isExpectedVisible = true; // Sollte beim Scrollen sichtbar sein
                }
                if (i === 50) {
                    div.style.position = 'relative';
                    div.style.left = '-650px'; // Schiebt es 50px nach links, teilweise aus dem Container
                    div.style.width = '100%';
                    div.style.backgroundColor = 'lightgreen';
                    content += ' (teilweise außerhalb)';
                    isExpectedVisible = false; // Kann nie zu 100% sichtbar sein (threshold: 1.0)
                }
                
                div.textContent = content;
                // Die "Wahrheitsquelle" als data-Attribut setzen
                div.dataset.expectedVisibility = isExpectedVisible;
                container.appendChild(div);
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Geben Sie dem Browser einen Moment Zeit, um das Rendering abzuschließen.
            setTimeout(() => {
                console.log(`--- Starte Test für: ${document.title} ---`);
                console.log("Hinweis: Da dieser Test nicht scrollt, werden nur die Elemente im initialen Viewport auf Verdeckung geprüft.");

                const staticWithOcclusionCheckLogic = (doc) => {
                    const selector = doc.body.dataset.selectorToCheck;
                    const elements = Array.from(doc.querySelectorAll(selector));
                    const results = [];
                    const pageScrollHeight = doc.body.scrollHeight;
                    const pageScrollWidth = doc.body.scrollWidth;
                    const viewportHeight = doc.defaultView.innerHeight;
                    const viewportWidth = doc.defaultView.innerWidth;

                    for (const el of elements) {
                        const rect = el.getBoundingClientRect();
                        
                        const isRendered = rect.width > 0 && rect.height > 0;

                        const scrollTop = doc.defaultView.scrollY || doc.defaultView.pageYOffset;
                        const scrollLeft = doc.defaultView.scrollX || doc.defaultView.pageXOffset;
                        const isWithinPageBounds = isRendered &&
                            (rect.top + scrollTop) >= 0 &&
                            (rect.left + scrollLeft) >= 0 &&
                            (rect.bottom + scrollTop) <= pageScrollHeight &&
                            (rect.right + scrollLeft) <= pageScrollWidth;

                        let isVisibleByCss = false;
                        if (isRendered) {
                            if (typeof el.checkVisibility === 'function') {
                                isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                            } else {
                                const style = doc.defaultView.getComputedStyle(el);
                                isVisibleByCss = style && style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
                            }
                        }
                        
                        let isOccluded = false;
                        const isInViewport = rect.top < viewportHeight && rect.bottom > 0 && rect.left < viewportWidth && rect.right > 0;

                        if (isVisibleByCss && isInViewport) {
                            const samplePoints = [
                                // 3x3-Raster für eine gründlichere Prüfung
                                { x: rect.left + 1, y: rect.top + 1 },                             // Oben-links
                                { x: rect.left + rect.width / 2, y: rect.top + 1 },                 // Oben-mitte
                                { x: rect.right - 1, y: rect.top + 1 },                            // Oben-rechts
                                { x: rect.left + 1, y: rect.top + rect.height / 2 },               // Mitte-links
                                { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 }, // Mitte-mitte (Zentrum)
                                { x: rect.right - 1, y: rect.top + rect.height / 2 },              // Mitte-rechts
                                { x: rect.left + 1, y: rect.bottom - 1 },                          // Unten-links
                                { x: rect.left + rect.width / 2, y: rect.bottom - 1 },              // Unten-mitte
                                { x: rect.right - 1, y: rect.bottom - 1 }                           // Unten-rechts
                            ];

                            let isVisibleAtAnyPoint = false;
                            for (const point of samplePoints) {
                                if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                                    const elementAtPoint = doc.elementFromPoint(point.x, point.y);
                                    if (elementAtPoint === el) {
                                        isVisibleAtAnyPoint = true;
                                        break;
                                    }
                                }
                            }
                            if (!isVisibleAtAnyPoint) {
                                isOccluded = true;
                            }
                        }

                        const isTrulyVisible = isWithinPageBounds && isVisibleByCss && !isOccluded;

                        results.push({ 
                            id: el.id, 
                            expected: el.dataset.expectedVisibility === 'true',
                            isTrulyVisible: isTrulyVisible 
                        });
                    }
                    return results;
                };

                const results = staticWithOcclusionCheckLogic(document);
                
                let correctCount = 0;
                const tableData = [];

                results.forEach(result => {
                    const isCorrect = result.isTrulyVisible === result.expected;
                    if (isCorrect) {
                        correctCount++;
                    }
                    tableData.push({
                        'Element ID': result.id,
                        'Erwartet': result.expected,
                        'Erhalten': result.isTrulyVisible,
                        'Status': isCorrect ? 'PASS' : 'FAIL'
                    });
                });

                console.log('Ergebnisse:');
                console.table(tableData);

                const accuracy = results.length > 0 ? (correctCount / results.length) * 100 : 0;
                console.log(`\nGenauigkeit: ${accuracy.toFixed(2)}% (${correctCount}/${results.length} korrekt)`);
                console.log('--- Test abgeschlossen ---');

            }, 200);
        });
    </script>
</body>
</html>
