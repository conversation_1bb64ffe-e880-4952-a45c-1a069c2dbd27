async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`[${deviceName}] Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        const adDomId = `qmn${ad.id}`;
        const adSelector = `#${adDomId}`;
        const elementHandle = await page.$(adSelector);

        if (!elementHandle) {
            // Dieser Fall wird hauptsächlich durch den Placement-Check abgefangen, dient aber als robuster Fallback.
            const details = { isTrulyVisible: false, reason: "Element not found in DOM during visibility check" };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   - ❌ ${adDomId}: Nicht gefunden.`);
            continue;
        }

        // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
        await elementHandle.evaluate(el => el.scrollIntoView({ block: 'center', inline: 'center' }));
        // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
        await new Promise(resolve => setTimeout(resolve, 1000));

        // DEBUG: Überprüfen, ob das Element nach dem Scrollen im Viewport ist.
        const isElementInViewportForDebug = await elementHandle.evaluate(isElementInViewportLogic);
        console.log(`[${deviceName}]   - DEBUG: Element ${adDomId} im Viewport nach Scroll: ${isElementInViewportForDebug}`);

        // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0]; 

        if (!result || !result.isTrulyVisible) {
            const details = result || { isTrulyVisible: false };
            notVisibleAds.push({ id:ad.id, type: ad.type , visibilityDetails: details });
            const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
            console.log(`[${deviceName}]   - ❌ ${adDomId}: Nicht sichtbar. Grund: ${reason}`);
        } else {
            console.log(`[${deviceName}]   - ✅ ${adDomId}: Sichtbar.`);
        }
        
        await elementHandle.dispose(); // Bereinige das Handle, um Speicherlecks zu vermeiden.
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
}

// --- Logik für Debugging: Viewport-Check (wird im Browser-Kontext ausgeführt) ---
const isElementInViewportLogic = (el) => {
    if (!el) return false;
    const rect = el.getBoundingClientRect();
    const vHeight = window.innerHeight || document.documentElement.clientHeight;
    const vWidth = window.innerWidth || document.documentElement.clientWidth;
    
    // Prüft, ob irgendein Teil des Elements im Viewport sichtbar ist.
    const vertInView = rect.top < vHeight && rect.bottom > 0;
    const horInView = rect.left < vWidth && rect.right > 0;
    
    return vertInView && horInView;
};