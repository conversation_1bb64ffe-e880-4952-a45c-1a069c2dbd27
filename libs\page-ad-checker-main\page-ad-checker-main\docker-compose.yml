name: pupquer
services:
  redis:
    image: redis:6
    ports:
      - "6379:6379"

  producer:
    build: ./producer
    depends_on:
      redis:
        condition: service_started
  
  worker:
    build: ./worker
    depends_on:
      redis:
        condition: service_started
    volumes:
      - ./worker/index.js:/app/index.js
      - ./worker/package.json:/app/package.json
    deploy:
      replicas: 3
  bullboard:
    image: deadly0/bull-board:latest
    platform: linux/amd64
    ports:
      - "3000:3000"
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      QUEUES: url-jobs
      QUEUE_TYPE: bullmq
      UI_PATH: /
    depends_on:
      redis:
        condition: service_started
