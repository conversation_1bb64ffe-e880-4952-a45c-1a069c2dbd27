import dotenv from "dotenv";
dotenv.config();

import { downloadTikTokAudio } from "./tiktok-download.js";
import { extractRecipeFromAudio } from "./gemini-extract-from-audio.js";

async function run() {
  const url = process.argv[2];
  if (!url) {
    console.error("Usage: node cookthat/run-pipeline.js <tiktokUrl>");
    process.exit(1);
  }

  console.log("➡️  Downloading audio...");
  const { filePath: audioPath, description } = await downloadTikTokAudio(url);
  console.log("✅ Audio saved:", audioPath);
  if (description) console.log("ℹ️  TikTok description:", description);

  console.log("➡️  Sending audio to <PERSON> for recipe extraction...");
  const recipe = await extractRecipeFromAudio(audioPath, { description });
  console.log("✅ Recipe extracted:\n" + recipe);
}

run().catch((e) => {
  console.error("Pipeline failed:", e?.message || e);
  process.exit(1);
});

