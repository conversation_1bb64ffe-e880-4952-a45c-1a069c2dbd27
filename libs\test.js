const testObj = {
  "displayMode": "production",
  "excludedPaths": [],
  "options": {
    "adLabel": {
      "enabled": true,
      "text": "Werbung",
      "style": {
        "color": "#333333",
        "fontSize": ".7rem",
        "textAlign": "center",
        "padding": "5px 5px 2px 5px",
        "textTransform": "uppercase",
        "fontWeight": "bold"
      }
    }
  },
  "adSlots": [
    {
      "id": "5003",
      "type": "tracker",
      "width": "1",
      "height": "1",
      "responsive": "desktop"
    },
    {
      "id": "5000",
      "type": "tracker",
      "width": "1",
      "height": "1",
      "responsive": "mobile"
    },
    {
      "id": "5001",
      "type": "intext",
      "width": "800",
      "height": "250",
      "responsive": "desktop",
      "lazyload": true,
      "position": {
        "htmlSelectors": ".uk-grid-column-small, body.top-goldenratio .uk-grid-column-small, body.option-com_content.view-category article h2:nth-of-type(2), #module-1655, article h2:nth-of-type(2), article h3:nth-of-type(2)",
        "htmlPosition": "before"
      },
      "css": {
        "marginBottom": "20px",
        "display": "flex",
        "justifyContent": "center",
        "alignItems": "center",
        "height": "275px",
        "flexDirection": "column"
      }
    },
    {
      "id": "5004",
      "type": "floorad",
      "width": "300",
      "height": "600",
      "responsive": "desktop",
      "css": {}
    },
    {
      "id": "5002",
      "type": "prospekt",
      "width": "300",
      "height": "600",
      "responsive": "desktop",
      "lazyload": true,
      "position": {
        "htmlSelectors": ".uk-grid.tm-grid-expand:nth-of-type(4), article h2:nth-of-type(10), .uk-card",
        "htmlPosition": "before"
      },
      "css": {}
    },
    {
      "id": "4997",
      "type": "hpa",
      "width": "300",
      "height": "600",
      "responsive": "mobile",
      "lazyload": true,
      "position": {
        "htmlSelectors": ".uk-margin-medium-top h2:nth-of-type(2), .uk-margin-medium-top h3:nth-of-type(2), .uk-container h2:nth-of-type(3), .uk-margin-medium-top h2, .uk-grid .uk-grid-margin, article h2:nth-of-type(6), h3:nth-of-type(2), .uk-margin-medium-top p:nth-of-type(10)",
        "htmlPosition": "before"
      },
      "css": {}
    },
    {
      "id": "4996",
      "type": "floorad",
      "width": "300",
      "height": "600",
      "responsive": "mobile",
      "position": {
        "htmlSelectors": "footer",
        "htmlPosition": "before"
      },
      "css": {}
    },
    {
      "id": "4999",
      "type": "hpa",
      "width": "300",
      "height": "600",
      "responsive": "mobile",
      "lazyload": true,
      "position": {
        "htmlSelectors": ".uk-grid .uk-grid-margin:nth-of-type(8), .uk-margin-medium-top h3:nth-of-type(4), .uk-flex.uk-flex-middle:nth-of-type(3), article h2:nth-of-type(3), h2:nth-of-type(4), .uk-margin-medium-top p:nth-of-type(18)",
        "htmlPosition": "before"
      },
      "css": {}
    },
    {
      "id": "4998",
      "type": "prospekt",
      "width": "300",
      "height": "600",
      "responsive": "mobile",
      "lazyload": true,
      "position": {
        "htmlSelectors": "#module-1665, .uk-margin-medium-top p:nth-of-type(40), article h2:nth-of-type(9), .tm-bottom, .uk-card",
        "htmlPosition": "before"
      },
      "css": {}
    },
    {
      "id": "5005",
      "type": "sitebar",
      "width": "300",
      "height": "600",
      "responsive": "desktop",
      "lazyload": false,
      "css": {},
      "options": {
        "header_sticky_selector": "header",
        "footer_selector": "footer",
        "main_content_selector": "main",
        "is_left_sitebar": "0",
        "sitebar_max_width": "600",
        "is_sitebar_dynamic_placement": "0"
      }
    }
  ],
  "watchbetter": {
    "players": [
      {
        "playerType": "embedPartner",
        "playlist": "Tierchenwelt",
        "responsive": "desktop",
        "position": {
          "htmlSelectors": "body.option-com_content.view-article article h2:nth-of-type(3), body.option-com_content.view-article article h3:nth-of-type(3), .uk-grid.tm-grid-expand:nth-of-type(3) .uk-panel:nth-of-type(3), h2:nth-of-type(5), body.columns-2 h2:nth-of-type(4), body.columns-2 h3:nth-of-type(5), .tm-bottom .uk-container",
          "htmlPosition": "before"
        },
        "css": {}
      },
      {
        "playerType": "embedPartner",
        "playlist": "Tierchenwelt",
        "responsive": "mobile",
        "position": {
          "htmlSelectors": ".uk-grid .uk-grid-margin:nth-of-type(15), .uk-margin-medium-top h2:nth-of-type(8), .uk-margin-medium-top h3:nth-of-type(8), h2:nth-of-type(5), .uk-margin-medium-top p:nth-of-type(28), .tm-bottom .uk-grid .uk-grid-margin:nth-of-type(4), #module-1712, .uk-card, footer",
          "htmlPosition": "before"
        },
        "css": {}
      }
    ]
  }
}

const groupAdIdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN-Objekt oder qmn.adSlots ist nicht im erwarteten Format.');
    return {};
  }

  return qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    // Anzeigen vom Typ 'tracker' und Slots ohne ID/Responsive ignorieren
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Sicherstellen, dass das Array für den Gerätetyp existiert
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Anzeigen-ID zur Liste hinzufügen
    acc[responsive].push(id);

    return acc;
  }, {});
};

const main = () => {

  const result = groupAdIdsByResponsive(testObj)
  console.log(result)

}

main()


async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`[${deviceName}] Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        // Construct the selector for the ad container based on ad.id
        // Example: #qmn12345
        const adContainerSelector = `#qmn${ad.id}`;
        console.log(`  [${deviceName}] Prüfe Ad Container: ${adContainerSelector}`);

        let iframeHandle = null;
        let iframeId = null;

        try {
            // Step 1: Wait for the ad container to be present on the page
            const containerHandle = await page.waitForSelector(adContainerSelector, { timeout: 10000 }); // Increased timeout for container
            
            if (!containerHandle) {
                const details = { isTrulyVisible: false, reason: "Ad container element not found (timeout)." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ${adContainerSelector}: Ad-Container nicht gefunden oder Timeout.`);
                continue;
            }

            // Step 2: Look for an iframe *inside* that specific ad container
            // We expect the iframe's ID to start with "adspiritflash" based on previous examples.
            iframeHandle = await containerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 10000 }); // Wait for iframe inside container

            if (!iframeHandle) {
                const details = { isTrulyVisible: false, reason: "Iframe element not found within specific ad container (timeout)." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ${adContainerSelector} iframe: Nicht gefunden (Timeout oder Strukturabweichung).`);
                await containerHandle.dispose(); // Clean up container handle
                continue;
            }

            // Step 3: Get the ID of the found iframe
            iframeId = await iframeHandle.evaluate(iframe => iframe.id);

            if (!iframeId) {
                const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ${adContainerSelector} iframe: Gefunden, aber es hat keine ID.`);
                await iframeHandle.dispose();
                await containerHandle.dispose();
                continue;
            }

            console.log(`  [${deviceName}] Gefundenes Iframe-ID für ${adContainerSelector}: ${iframeId}`);

            const adSelectorForVisibilityCheck = `#${iframeId}`;

            // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
            await iframeHandle.evaluate(el => el.scrollIntoView({ block: 'center', inline: 'center' }));
            // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
            await new Promise(resolve => setTimeout(resolve, 1000));

            // DEBUG: Überprüfen, ob das Element nach dem Scrollen im Viewport ist.
            const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
            console.log(`[${deviceName}]   - DEBUG: Element ${adSelectorForVisibilityCheck} im Viewport nach Scroll: ${isElementInViewportForDebug}`);

            // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
            // Note: checkVisibilityLogic takes a selector, which is now the iframeId.
            const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelectorForVisibilityCheck);
            const result = visibilityResults[0]; // Assuming checkVisibilityLogic returns an array with one result for the given selector

            if (!result || !result.isTrulyVisible) {
                const details = result || { isTrulyVisible: false };
                notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
                const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
                console.log(`[${deviceName}]   - ❌ ${adSelectorForVisibilityCheck}: Nicht sichtbar. Grund: ${reason}`);
            } else {
                console.log(`[${deviceName}]   - ✅ ${adSelectorForVisibilityCheck}: Sichtbar.`);
            }

        } catch (error) {
            // Catch specific timeouts or other errors during iframe finding
            const details = { isTrulyVisible: false, reason: `Fehler beim Suchen des Iframes: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   - ERROR beim Prüfen von ${adContainerSelector}: ${error.message}`);
        } finally {
            if (iframeHandle) {
                await iframeHandle.dispose(); // Clean up the iframe handle
            }
            // containerHandle would be disposed if it was assigned and caught an error before iframeHandle.
            // If it was successfully used, it will be implicitly cleaned up when page closes or when its scope ends.
            // For explicit cleanup after successful use:
            // if (containerHandle) await containerHandle.dispose();
        }
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
}