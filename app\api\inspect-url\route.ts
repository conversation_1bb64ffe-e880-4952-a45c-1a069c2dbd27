import { NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};

export async function POST(request: Request) {
  const body = await request.json();
  const { url } = body;

  if (!url) {
    return NextResponse.json({ error: 'URL ist erforderlich' }, { status: 400 });
  }

  let browser;
  try {
    browser = await puppeteer.launch({
        headless: "new",
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });
    // Warte, bis das Netzwerk für eine Weile inaktiv ist, um sicherzustellen, dass die meisten Skripte geladen wurden.
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 25000 });

    /* const cookieConsentAccepted = await acceptCookieConsent(page);
      if (cookieConsentAccepted === 1) {
        console.log('Cookie consent accepted');
        await new Promise(resolve => setTimeout(resolve, 2000)); 
      } else {
        throw new Error('No cookie consent found '+cookieConsentAccepted);
      } */

    // Extrahiert das qmn-Objekt von der Seite
    const qmnData = await page.evaluate(() => {
      // Diese Funktion wird im Kontext der besuchten Seite ausgeführt.
      // 'qmn' wird als globale Variable im 'window'-Objekt erwartet.
      if (typeof (window as any).qmn !== 'undefined') {
        return (window as any).qmn;
      }
      return null; // Gibt null zurück, wenn qmn nicht gefunden wird.
    });

    if (qmnData === null) {
      return NextResponse.json({ error: 'Das QMN-Objekt (window.qmn) wurde auf der angegebenen URL nicht gefunden.' }, { status: 404 });
    }

    return NextResponse.json(qmnData);
    
  } catch (error: any) {
    console.error(`Fehler beim Analysieren von ${url}:`, error);
    let errorMessage = `Fehler beim Analysieren der URL: ${error.message}`;
    if (error.name === 'TimeoutError') {
        errorMessage = `Timeout: Die Seite ${url} hat nicht innerhalb von 25 Sekunden geantwortet.`;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}
