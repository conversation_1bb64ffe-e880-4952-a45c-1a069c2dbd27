import { GoogleGenerativeAI } from "@google/generative-ai";
import { z } from "zod";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";

// Try loading env from current dir and parent dir (helps when running from cookthat/)
dotenv.config();
if (!process.env.GOOGLE_API_KEY && !process.env.GEMINI_API_KEY && !process.env.GOOGLE_GENAI_API_KEY) {
  try { dotenv.config({ path: path.resolve(process.cwd(), "..", ".env") }); } catch {}
}

// Define the target recipe schema
export const RecipeSchema = z.object({
  title: z.string().min(1).max(200),
  servings: z.number().int().positive().nullable().optional(),
  total_time_minutes: z.number().int().positive().nullable().optional(),
  ingredients: z
    .array(
      z.object({
        quantity: z.string().optional(),
        unit: z.string().optional(),
        item: z.string().min(1),
        notes: z.string().optional(),
      })
    )
    .default([]),
  steps: z.array(z.string().min(1)).default([]),
  notes: z.string().optional(),
});

function mimeFromExt(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case ".mp3":
      return "audio/mpeg";
    case ".wav":
      return "audio/wav";
    case ".m4a":
      return "audio/mp4";
    case ".ogg":
      return "audio/ogg";
    default:
      return "application/octet-stream";
  }
}

function fileToGenerativePart(filePath) {
  const mimeType = mimeFromExt(filePath);
  const data = fs.readFileSync(filePath);
  const base64 = data.toString("base64");
  return {
    inlineData: {
      data: base64,
      mimeType,
    },
  };
}

const systemPrompt = `You are an expert culinary assistant. You will receive an audio track from a cooking video (e.g., TikTok). Extract a clean, structured recipe from the audio narration only. If there is insufficient info, make minimal reasonable assumptions but mark unknowns as null.

Output requirements:
- Keep measurements and quantities if mentioned.
- Keep the order of steps as presented.
- Include time and servings only if clearly stated.
- Use concise, clear language.
`;

export async function extractRecipeFromAudio(audioFilePath, opts = {}) {
  const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
  if (!apiKey) {
    const foundRelated = Object.keys(process.env).filter((k) => k.toLowerCase().includes("google") || k.toLowerCase().includes("gemini"));
    throw new Error(
      "Missing Google AI API key. Set one of: GOOGLE_API_KEY, GEMINI_API_KEY, or GOOGLE_GENAI_API_KEY (in .env at project root). Found related envs: " +
      (foundRelated.length ? foundRelated.join(", ") : "none")
    );
  }

  const { description } = opts;

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash-lite" });

  // Compose parts: system instruction + audio + user guidance for JSON
  const audioPart = fileToGenerativePart(audioFilePath);
  const userPrompt = `Return only JSON matching the schema keys: {title, servings, total_time_minutes, ingredients, steps, notes}. No prose.`;

  const parts = [{ text: systemPrompt }, audioPart, { text: userPrompt }];
  if (description && description.trim().length > 0) {
    parts.splice(1, 0, { text: `Video description (may include ingredients/steps) to guide you:\n${description}` });
  }

  const result = await model.generateContent({
    contents: [
      { role: "user", parts },
    ],
  });

  const text = result.response.text();

  // Attempt to locate JSON in the text
  let jsonText = text.trim();
  // Remove markdown fences if present
  if (jsonText.startsWith("```")) {
    jsonText = jsonText.replace(/^```[a-zA-Z]*\n?/, "").replace(/```\s*$/, "");
  }

  let parsed;
/*   try {
    parsed = JSON.parse(jsonText);
  } catch (e) {
    // Try to salvage by finding first { ... } block
    const start = jsonText.indexOf("{");
    const end = jsonText.lastIndexOf("}");
    if (start >= 0 && end > start) {
      parsed = JSON.parse(jsonText.slice(start, end + 1));
    } else {
      throw new Error("Gemini did not return JSON: " + jsonText.slice(0, 300));
    }
  }
 */
  // Validate and coerce to schema
  /* const recipe = RecipeSchema.parse(parsed); */
  return text;
}

// small manual test helper
if (import.meta.url === `file://${process.argv[1]}`) {
  const filePath = process.argv[2];
  if (!filePath) {
    console.error("Usage: node cookthat/gemini-extract-from-audio.js <audioFile>");
    process.exit(1);
  }
  extractRecipeFromAudio(filePath)
    .then((r) => {
      console.log(JSON.stringify(r, null, 2));
    })
    .catch((e) => {
      console.error("Extraction failed:", e?.message || e);
      process.exit(1);
    });
}

