const { Worker } = require('bullmq');
const IORedis = require('ioredis');
const puppeteer = require('puppeteer');

const connection = new IORedis({
  host: 'redis',
  port: 6379,
  maxRetriesPerRequest: null,
});

async function getElementId(elementHandle) {
    if (elementHandle) {
        return await elementHandle.evaluate(el => el.id);
    }
    return null;
}



const autoScrollToBottom = async (page) => {
    await page.evaluate(async () => {
        await new Promise(resolve => {
            let lastScrollHeight = 0;
            let scrollAttempts = 0;
            const maxScrollAttempts = 5; // Number of times to try scrolling without new content

            const scroll = () => {
                const currentScrollHeight = document.body.scrollHeight;
                window.scrollTo({ top: currentScrollHeight, behavior: 'smooth' }); // Added smooth behavior

                if (currentScrollHeight === lastScrollHeight) {
                    scrollAttempts++;
                } else {
                    scrollAttempts = 0; // Reset if new content loaded
                }

                lastScrollHeight = currentScrollHeight;

                if (scrollAttempts >= maxScrollAttempts) {
                    resolve(); // End of page reached
                } else {
                    setTimeout(scroll, 200); // Wait a bit and try again
                }
            };
            scroll();
        });
    });

    // make sure that all content on the webpage has been loaded 
    // await new Promise(resolve => setTimeout(resolve, 3000));
};


const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};

// this  function is used for debugging purposes 
const isElementInViewportLogic = (el) => {
    if (!el) return false;
    const rect = el.getBoundingClientRect();
    const vHeight = window.innerHeight || document.documentElement.clientHeight;
    const vWidth = window.innerWidth || document.documentElement.clientWidth;
    
    // Checks whether any part of the element is visible in the viewport.
    const vertInView = rect.top < vHeight && rect.bottom > 0;
    const horInView = rect.left < vWidth && rect.right > 0;
    
    return vertInView && horInView;
};

const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN object or qmn.adSlots is not in the expected format.');
    return {};
  }

  const acc = qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    // Ignore ads of type 'tracker' and slots without ID/responsive.
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Make sure the array for the device type exists.
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Add display object to list.
    acc[responsive].push({ id, type });

    return acc;
  }, {});

  if (qmn.watchbetter) {
    acc.mobile.push({ id: 'watchbetter-embed', type: 'watchbetter' });
    acc.desktop.push({ id: 'watchbetter-embed', type: 'watchbetter' });   
  }

  return acc
};

const checkSelectors = async (page) => {
  const qmnData = await page.evaluate(() => {
    return window.qmn;
  });

  /* 
  DEBUG Console.log
  console.log( 'QMN Object:' ,JSON.stringify(qmnData, null, 2)) 
  */

  if(!qmnData || !qmnData.config) {
    console.log('⚠️ window.qmn object or window.qmn.config not found on the page.');
    return null;
  }

  return qmnData.config
}

const viewports = {
  mobile: { width: 412, height: 940, deviceType: 'mobile', name: 'Mobile' },
  tablet: { width: 1024, height: 1366, deviceType: 'desktop', name: 'Tablet' },
  desktop: { width: 1920, height: 1080, deviceType: 'desktop', name: 'Desktop HD' },
};

// --- Logic for Layer 1: Placement Check (executed in the browser context) ---
const checkPlacementLogic = (ads) => {
    const missing = [];
    for (const ad of ads) {
        let adContainer;
        if (ad.type === 'watchbetter') {
            adContainer = document.getElementById(ad.id);
        } else {
            adContainer = document.getElementById(`qmn${ad.id}`);
        }
        if (!adContainer) {
            missing.push(ad);
        }
    }
    return missing;
};

// --- Logic for Layer 2: Positioning Check (executed in the browser context) ---
const checkPositioningLogic = (ads) => {
    const unpositionable = [];
    for (const ad of ads) {
        if (ad.position && ad.position.htmlSelectors) {
            const selector = ad.position.htmlSelectors;
            const isPlaceable = document.querySelectorAll(selector).length > 0;
            if (!isPlaceable) {
                unpositionable.push({
                    ...ad,
                    result: false
                });
            }
        }
    }
    return unpositionable;
};


// --- Logic for Layer 3: Visibility Check (executed in the browser context) ---
const checkVisibilityLogic = (selector) => {
    const el = document.querySelector(selector);
    if (!el) {
        return [{
            id: selector,
            isWithinPageBounds: false,
            isVisibleByCss: false,
            isOccluded: false,
            isPartiallyOutOfViewport: true,
            visibilityPercentage: 0,
            occludingElements: [],
            reason: 'Element not found'
        }];
    }

    const pageScrollHeight = document.body.scrollHeight;
    const pageScrollWidth = document.body.scrollWidth;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    const qmnContainer = el.closest('div[id^="qmn"], div[id="watchbetter-embed"]');
    const parentOfQmnContainer = qmnContainer ? qmnContainer.parentElement : null;

    const rect = el.getBoundingClientRect();
    
    const isRendered = rect.width > 0 && rect.height > 0;

    const scrollTop = window.scrollY || window.pageYOffset;
    const scrollLeft = window.scrollX || window.pageXOffset;
    const isWithinPageBounds = isRendered &&
        (rect.top + scrollTop) >= 0 &&
        (rect.left + scrollLeft) >= 0 &&
        (rect.bottom + scrollTop) <= pageScrollHeight &&
        (rect.right + scrollLeft) <= pageScrollWidth;

    let isVisibleByCss = false;
    if (isRendered) {
        if (typeof el.checkVisibility === 'function') {
            isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
        }
    }
    
    let visiblePoints = 0;
    const occludingElementsRaw = [];
    let pointsOutsideViewport = 0;

    const samplePoints = [
        { x: rect.left + 1, y: rect.top + 1 },
        { x: rect.left + rect.width / 2, y: rect.top + 1 },
        { x: rect.right - 1, y: rect.top + 1 },
        { x: rect.left + 1, y: rect.top + rect.height / 2 },
        { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 },
        { x: rect.right - 1, y: rect.top + rect.height / 2 },
        { x: rect.left + 1, y: rect.bottom - 1 },
        { x: rect.left + rect.width / 2, y: rect.bottom - 1 },
        { x: rect.right - 1, y: rect.bottom - 1 }
    ];

    if (isVisibleByCss && isWithinPageBounds) {
        samplePoints.forEach(point => {
            if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                const elementAtPoint = document.elementFromPoint(point.x, point.y);
                
                if (!elementAtPoint) {
                    return; // Not visible at this point
                }

                if (el.contains(elementAtPoint) || (qmnContainer && elementAtPoint === qmnContainer) || (parentOfQmnContainer && elementAtPoint === parentOfQmnContainer)) {
                    visiblePoints++;
                } else {
                    // Occluded
                    occludingElementsRaw.push({
                        tag: elementAtPoint.tagName,
                        id: elementAtPoint.id || null,
                        className: elementAtPoint.className || null
                    });
                }
            } else {
                pointsOutsideViewport++;
            }
        });
    }

    const visibilityPercentage = Math.round((visiblePoints / samplePoints.length) * 100);
    
    // Deduplicate occluding elements
    const occludingElements = [];
    if (occludingElementsRaw.length > 0) {
        const seenOccluders = new Set();
        for (const o of occludingElementsRaw) {
            const key = `${o.tag}${o.id ? '#' + o.id : ''}${o.className ? '.' + String(o.className).split(' ').join('.') : ''}`;
            if (!seenOccluders.has(key)) {
                seenOccluders.add(key);
                occludingElements.push(o);
            }
        }
    }

    const isOccluded = occludingElements.length > 0;
    const isPartiallyOutOfViewport = pointsOutsideViewport > 0;

    const finalResult = {
        id: el.id,
        isWithinPageBounds,
        isVisibleByCss,
        isOccluded,
        isPartiallyOutOfViewport,
        visibilityPercentage,
        occludingElements
    };

    return [finalResult];
};

async function runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unplacedAds = await page.evaluate(checkPlacementLogic, adsToCheck);
    if (unplacedAds.length > 0) {
        if (!issues.placement) issues.placement = { desktop: [], tablet: [],mobile: [] };
        issues.placement[responsiveKey] = unplacedAds;
        console.log(`[${deviceName}] ERROR [Placement]: ${unplacedAds.length} Display containers not found in the DOM:`, unplacedAds.map(ad => ad.id));
    } else {
        console.log(`[${deviceName}] SUCCESS [Placement]: All ${adsToCheck.length} display containers found.`);
    }
    return unplacedAds.length;
}

async function runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unpositionableAds = await page.evaluate(checkPositioningLogic, adsToCheck);
    if (unpositionableAds.length > 0) {
        if (!issues.positioning) issues.positioning = { desktop: [], tablet: [],mobile: [] };
        issues.positioning[responsiveKey] = unpositionableAds;
        console.log(`[${deviceName}] ERROR [Positioning]: ${unpositionableAds.length} Ads not positionable (target selector not found):`, unpositionableAds.map(ad => ({id: ad.id, selector: ad.position.htmlSelectors})));
    } else {
        console.log(`[${deviceName}] SUCCESS [Positioning]: All ${adsToCheck.length} ads are positionable.`);
    }
    return unpositionableAds.length;
}

async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`### ➡️ [${deviceName}] SUCCESS [Visibility]: No ads to check for this layer.`);
        return 0; // there is nothing to check, so we return 0 issues.
    }

    const notVisibleAds = [];
    console.log(`### ➡️ [${deviceName}] Starting individual visibility check for ${adsToCheck.length} ads...`);

    for (const ad of adsToCheck) {
        let iframeHandle = null;

        // parentContainerHandle saves the parent container of the iframe. It is saved because it is part of the exclusion list.
        // the exclusion list is a list of elements that are not considered as possible occluder when checking if the ad is occluded 
        let parentContainerHandle = null;
        let parentContainerId = null;
        let insContainerId = null;

        try {
            if (ad.type === 'watchbetter') {
                // whatchbetter has a different structure. 
                // --- Specific logic for watchbetter ---
                console.log(`### ➡️  [${deviceName}] ➡️ Checking ad ID: ${ad.id} (watchbetter). Searching for container: #${ad.id}`);
                const watchbetterContainerHandle = await page.$(`#${ad.id}`);
                if (watchbetterContainerHandle) {
                    parentContainerId = await getElementId(watchbetterContainerHandle);
                    iframeHandle = await watchbetterContainerHandle.$('iframe');
                    if (iframeHandle) {
                        parentContainerHandle = watchbetterContainerHandle;
                        console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside #${parentContainerId} for watchbetter ad.`);
                    }
                }
                
            } else {
                // --- Previous logic for QMN ads ---
                
                // we search for the div with the id "qmn_[digit]" e.g qmn64643

                const qmnDivSelector = `div[id^="qmn"][id*="${ad.id}"]`;
                console.log(`### ➡️  [${deviceName}] ➡️ Checking ad ID: ${ad.id}. Searching for div: ${qmnDivSelector}`);

                const qmnDivHandles = await page.$$(qmnDivSelector); // return an array
                

                if (qmnDivHandles.length === 0) {
                    console.log(`### ➡️  [${deviceName}] ⚠️ WARN: No div element matching "${qmnDivSelector}" found for ad.id ${ad.id}.`);
                    // Fallback to the 'asmobj_' logic if the qmn div is not found.
                    const asmobjContainerSelector = `div[id*="asmobj_${ad.id}"]`;
                    const asmobjContainerHandle = await page.$(asmobjContainerSelector);
                    if (asmobjContainerHandle) {
                        parentContainerId = await getElementId(asmobjContainerHandle);
                        iframeHandle = await asmobjContainerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 5000 });
                        if (iframeHandle) {
                            parentContainerHandle = asmobjContainerHandle;
                            console.log(`### ➡️ [${deviceName}] ✅ Found iframe via 'asmobj_' fallback for ad.id ${ad.id}. Parent: ${parentContainerId}`);
                        }
                    }

                    if (!iframeHandle) {
                        const details = { isTrulyVisible: false, reason: `No container div (${qmnDivSelector} or fallback) found for ad.id.` };
                        notVisibleAds.push({ ...ad, visibilityDetails: details });
                        console.log(`### ➡️ [${deviceName}]   -   Ad ID ${ad.id}: Container div not found.`);
                        continue;
                    }

                } else {
                    // Iterate through found QMN divs and search for all ins 
                    for (const qmnDiv of qmnDivHandles) {
                        parentContainerId = await getElementId(qmnDiv);
                        console.log(`### ➡️ [${deviceName}]   - Found QMN div: #${parentContainerId}. Searching for ins...`);

                        const insHandles = await qmnDiv.$$('ins');

                        if (insHandles.length === 0) {
                            console.log(`### ➡️ [${deviceName}]     - No 'ins' element found within div #${parentContainerId}.`);
                            const divOuterHTML = await qmnDiv.evaluate(node => node.outerHTML);
                            console.log(`### ➡️ [${deviceName}]     - Full HTML of div #${parentContainerId} (no ins found):`);
                            console.log(divOuterHTML);
                            continue;
                        }

                        for (const ins of insHandles) {
                            insContainerId = await getElementId(ins);
                            console.log(`### ➡️ [${deviceName}]     - Found ins: #${insContainerId}. Searching for iframe...`);
                            iframeHandle = await ins.$('iframe');

                            if (iframeHandle) {
                                parentContainerHandle = qmnDiv;
                                console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside div #${parentContainerId} -> ins #${insContainerId} for ad.id ${ad.id}.`);
                                break;
                            }
                        }

                        if (iframeHandle) {
                            break;
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`### ➡️ [${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`);
            const details = { isTrulyVisible: false, reason: `Error during iframe discovery: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            continue;
        }

        // --- Common logic after the iframe search ---
        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found after hierarchical search." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe not found after all attempts.`);
            if (parentContainerHandle) await parentContainerHandle.dispose();
            continue;
        }

        let adSelector;
        if (ad.type === 'watchbetter') {
            // For watchbetter, the iframe has no ID. We build a selector based on its parent.
            // The parentContainerId was already found in the try-block.
            adSelector = `#${parentContainerId} > iframe`;
            console.log(`### ➡️ [${deviceName}] -  Using selector for watchbetter ad: ${adSelector}`);
        } else {
            // For all other ads, we expect an ID on the iframe.
            const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
            console.log(`### ➡️ [${deviceName}] -  Iframe ID found for Ad ID ${ad.id}: ${iframeId}`);

            if (!iframeId) {
                const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe found, but it has no ID.`);
                if (parentContainerHandle) await parentContainerHandle.dispose();
                await iframeHandle.dispose();
                continue;
            }
            adSelector = `#${iframeId}`;
        }

        // --- Ad-Type-Specific Visibility Logic ---
        console.log(`### ➡️ [${deviceName}]   - Applying visibility logic for ad type: ${ad.type}`);

        let floorAdButtonHandle = null; // Handle for the floorad button

        switch (ad.type) {
            case 'floorad':
                console.log(`### ➡️ [${deviceName}]  Specific logic for 'floorad' is applied.`);
                const floorAdButtonSelector = 'div[id^="mflbuttons-"]';
                floorAdButtonHandle = await page.$(floorAdButtonSelector);

                if (floorAdButtonHandle) {
                    console.log(`### ➡️ [${deviceName}]  Floorad button found. Click to open the ad.`);
                    await floorAdButtonHandle.click();
                    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for animation
                } else {
                    console.log(`### ➡️ [${deviceName}]  WARN: Floorad button (${floorAdButtonSelector}) not found.`);
                }
                break;

            case 'sitebar':
                console.log(`### ➡️ [${deviceName}]  Specific logic for 'sitebar' is applied.`);
                if (!parentContainerHandle) {
                    console.log(`### ➡️ [${deviceName}]  WARN: Parent container handle for sitebar ad ${ad.id} not found. Scroll logic skipped.`);
                    break;
                }

                let isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                   
                    return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });

                });
                
                console.log(`### ➡️ [${deviceName}]  Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`);

                if (isContainerVisibleByCss) {
                    console.log(`### ➡️ [${deviceName}]  Sitebar container for ad ${ad.id} is already visible via CSS.`);
                } else {
                    console.log(`### ➡️ [${deviceName}]  Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`);
                    
                    const maxScrollAttempts = 50; // Maximum number of scroll attempts
                    let attempts = 0;

                    const scrollPosition = await page.evaluate(() => ({
                        scrollTop: window.scrollY,
                        scrollHeight: document.body.scrollHeight,
                        clientHeight: document.documentElement.clientHeight
                    }));
                    
                    const isCloseToBottom = (scrollPosition.scrollTop + scrollPosition.clientHeight) >= (scrollPosition.scrollHeight - 100);
                    const scrollDirection = isCloseToBottom ? 1 : -1; // -1 for up, 1 for down
                    const scrollAmount = 120; // Pixels per attempt

                    console.log(`### ${isCloseToBottom}`)

                    console.log(`### ➡️ [${deviceName}]   - Scroll ${scrollDirection === 1 ? 'DOWN' : 'UP'}, to find the sitebar container.`);

                    while (!isContainerVisibleByCss && attempts < maxScrollAttempts) {
                        await page.evaluate((y) => { window.scrollBy(0, y); }, scrollDirection * scrollAmount);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                            if (typeof el.checkVisibility !== 'function') return false;
                            return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                        });
                        attempts++;

                        const newScrollTop = await page.evaluate(() => window.scrollY);
                        if ((scrollDirection === -1 && newScrollTop === 0) || 
                            (scrollDirection === 1 && (await page.evaluate(() => (window.innerHeight + window.scrollY) >= document.body.scrollHeight)))) {
                            console.log(`### ➡️ [${deviceName}]   - page ${scrollDirection === 1 ? 'end' : 'start'} Reached. Stop the scroll search.`);
                            break;
                        }
                    }

                    if (isContainerVisibleByCss) {
                        console.log(`### ➡️ [${deviceName}]   - Sitebar container is now visible after ${attempts} scroll attempts via CSS.`);
                    } else {
                        console.log(`### ➡️ [${deviceName}]   - WARN: Could not make the sitebar container visible after ${attempts} attempts via CSS.`);
                    }
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;

            case 'intext':
            case 'prospekt':
            case 'hpa':
            default: // Standard logic for known and unknown types
                console.log(`### ➡️ [${deviceName}]   - Standard logic (scrollIntoView) is applied.`);
                // Scroll the element into the center of the viewport, to ensure it is checkable.
                await iframeHandle.evaluate(el => {
                    const rect = el.getBoundingClientRect();
                    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                    // Calculate the target scroll position to place the element 200px above the center
                    const targetY = rect.top + window.scrollY - (viewportHeight / 2) + (rect.height / 2) ;
                    window.scrollTo({ top: targetY });
                });
                // A short wait can help stabilize the page before scrolling.
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;
        }

        /* DEBUG: Check if the element is in the viewport after the action.
        const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
        console.log(`### ➡️ [${deviceName}]   - DEBUG: Element ${adSelector} in viewport after action: ${isElementInViewportForDebug}`);
        */

        // Evaluate the visibility of this single, now centered element.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0];

        if (!result || result.visibilityPercentage < 100) {
            let reason = 'Others'; // Default reason
            
            if (result.reason === 'Element not found') {
                reason = 'Element not found';
            } else if (!result.isVisibleByCss) {
                reason = 'Hidden by CSS';
            } else if (result.isOccluded) {
                reason = 'Covered';
            } else if (result.isPartiallyOutOfViewport) {
                reason = 'Not in viewport';
            } else if (!result.isWithinPageBounds) {
                reason = 'Not within page bounds';
            }

            const details = {
                visibilityPercentage: result.visibilityPercentage,
                reason: reason,
                isVisibleByCss: result.isVisibleByCss,
                occludingElements: result.occludingElements
            };

            notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]   - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`);
        } else {
            console.log(`### ➡️ [${deviceName}]   - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`);
        }

        // Close the Floorad ad if it was opened
        if (floorAdButtonHandle) {
            console.log(`### ➡️ [${deviceName}]  Close Floorad ad by clicking again.`);
            try {
                await floorAdButtonHandle.click();
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for animation
            } catch (e) {
                console.log(`### ➡️ [${deviceName}]  WARN: Floorad button could not be clicked again (possibly already closed): ${e.message}`);
            } finally {
                await floorAdButtonHandle.dispose();
            }
        }

        if (parentContainerHandle) {
            await parentContainerHandle.dispose();
        }
        await iframeHandle.dispose(); // Clean up the iframe handle
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`);
    } else {
        console.log(`[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`);
    }
    return notVisibleAds.length;
}

async function runChecksForDevice(browser, url, deviceName, config, activeLayers, consentState) {
    const page = await browser.newPage();
    page.on('console', msg => console.log(`[${config.name} BROWSER LOG]: ${msg.text()}`));

    try {
        console.log(`\n--- Test device: ${config.name} (${config.width}x${config.height}) ---`);
        await page.setViewport({ width: config.width, height: config.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        if (!consentState.handled) {
            console.log(`[${config.name}] First device check: Trying to accept cookie consent...`);
            const consentResult = await acceptCookieConsent(page);
            if (consentResult === 1) {
                console.log(`[${config.name}] Cookie consent accepted successfully.`);
                consentState.handled = true;
            } else {
                console.warn(`[${config.name}] Cookie consent could not be accepted (Code: ${consentResult}). Test is continuing.`);
            }
        }
        
        console.log(`[${config.name}] Wait for TCF API confirmation... ⏳ `);
        try {
            await page.evaluate(() => {
                return new Promise((resolve, reject) => {
                    // Retry mechanism to wait for the TCF API to be ready and successful
                    const checkTcfApi = (retries = 20) => { // ~10 seconds timeout (20 * 500ms)
                        if (typeof window.__tcfapi === 'function') {
                            window.__tcfapi('getTCData', 2, (tcData, success) => {
                                if (success) {
                                    console.log('TCF API success:', tcData);
                                    resolve();
                                } else if (retries > 0) {
                                    setTimeout(() => checkTcfApi(retries - 1), 500);
                                } else {
                                    reject(new Error('TCF API did not return success within the timeout.'));
                                }
                            });
                        } else if (retries > 0) {
                            // If the API function is not yet available, wait and retry
                            setTimeout(() => checkTcfApi(retries - 1), 500);
                        } else {
                            reject(new Error('__tcfapi function not found on page within the timeout. ❌ '));
                        }
                    };
                    checkTcfApi();
                });
            });
            console.log(`[${config.name}] TCF API confirmation received successfully. ✅ `);
        } catch (e) {
            console.warn(`[${config.name}] Error waiting for TCF API: ${e.message} ❌ `);
        }

        // A short wait can help stabilize the page before scrolling.
        await new Promise(resolve => setTimeout(resolve, 1000));

        // lazy load to ensure all content has been loaded before checking the ads.
        await autoScrollToBottom(page);

        // we extract the ads from the QMN object 
        const qmnConfig = await checkSelectors(page);
        if (!qmnConfig) {
            throw new Error(`[${config.name}] Could not retrieve window.qmn.config object.`);
        }

        // we group the ads by responsive type (desktop/mobile)
        const adsByResponsive = groupAdsByResponsive(qmnConfig);
        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`);

        // we run the checks for each layer (placement, positioning, visibility) and count the issues.
        const issues = {};
        let issueCount = 0;

        if (adsToCheck.length > 0) {
            if (activeLayers.includes('placement')) {
                issueCount += await runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('positioning')) {
                issueCount += await runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('visibility')) {
                issueCount += await runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
        } else {
            console.log(`[${deviceName}] No ads to check for device type '${responsiveKey}'.`);
        }

        return { deviceName, responsiveKey, issues, issueCount, error: null };

    } catch (e) {
        console.error(`❌ Error with device ${deviceName}:`, e.message);
        return { deviceName, error: e.message, issues: {}, issueCount: 1 };
    } finally {
        await page.close();
    }
}


const worker = new Worker(
  'url-jobs',
  async (job) => {
    const { url, layers } = job.data;
    const activeLayers = layers || ['placement', 'positioning', 'visibility'];

    console.log(`📥 Start analysis: ${url} with layers [${activeLayers.join(', ')}]`);

    const startTime = Date.now();

    const browser = await puppeteer.launch({
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      headless: 'new', // Use 'new' for headless mode
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      const consentState = { handled: false }; // Flag to track if consent has been handled.

      const deviceResults = [];
      // Run checks sequentially to handle consent state correctly
      for (const [deviceName, config] of Object.entries(viewports)) {
          const result = await runChecksForDevice(browser, url, deviceName, config, activeLayers, consentState);
          deviceResults.push(result);
      }

      const finalIssues = {};
      let totalIssues = 0;
      const deviceErrors = [];

      for (const result of deviceResults) {
          if (result.error) {
              deviceErrors.push({ device: result.deviceName, error: result.error });
          }
          totalIssues += result.issueCount;

          for (const layer of ['placement', 'positioning', 'visibility']) {
              if (result.issues[layer]) {
                  if (!finalIssues[layer]) {
                      // Initialize with all possible keys
                      finalIssues[layer] = { desktop: [], tablet: [], mobile: [] };
                  }
                  const responsiveKey = result.responsiveKey;
                  if (result.issues[layer][responsiveKey]) {
                      // Ensure the key exists before pushing
                      if (!finalIssues[layer][responsiveKey]) {
                          finalIssues[layer][responsiveKey] = [];
                      }
                      finalIssues[layer][responsiveKey].push(...result.issues[layer][responsiveKey]);
                  }
              }
          }
      }

      const endTime = Date.now();
      const durationMs = endTime - startTime;
      
      let finalResult;

      if (totalIssues === 0 && deviceErrors.length === 0) {
          finalResult = {
              success: true,
              url,
              checkedLayers: activeLayers,
              timestamp: new Date().toISOString(),
              processingTimeMs: durationMs
          };
      } else {
          finalResult = {
              success: false,
              totalIssues: totalIssues, // Added from test logic
              url,
              checkedLayers: activeLayers,
              issues: finalIssues,
              errors: deviceErrors,
              timestamp: new Date().toISOString(),
              processingTimeMs: durationMs
          };
      }

      console.log("final result 📈 :" ,JSON.stringify(finalResult, null, 2))
    
      return finalResult

    } catch (e) {
      console.error(`❌ Critical error during job execution for ${url}:`, e.message);
      return {
          url,
          error: e.message,
          timestamp: new Date().toISOString(),
      }
    } finally {
      await browser.close();
    }
  },
  { connection }
  
);

worker.on('completed', (job, result) => {
  console.log(`🎉 Task completed: ${job.id}`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job failed: ${job.id} - ${err.message}`);
});
