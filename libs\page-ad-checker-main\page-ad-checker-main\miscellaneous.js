const calculateHeadingDistances = () => {
  // Selektiere alle relevanten Elemente in Erscheinungsreihenfolge
  const elements = Array.from(document.querySelectorAll("h1, h2, h3, h4, h5, h6, p"));

  // Extrahiere Position und Typinformationen
  const getElementDetails = el => {
    const rect = el.getBoundingClientRect();
    const scrollTop = window.scrollY || window.pageYOffset;
  
    return {
      tag: el.tagName,
      text: el.textContent.trim(),
      top: rect.top + scrollTop,
      bottom: rect.bottom + scrollTop,
      isHeading: el.tagName.startsWith('H')
    };
  };
  

  const elementDetails = elements.map(getElementDetails);

  // Sortiere Elemente nach vertikaler Position im Viewport
  elementDetails.sort((a, b) => a.top - b.top);

  const result = [];
  let currentHeading = null;
  let currentSectionElements = [];

  for (let i = 0; i < elementDetails.length; i++) {
    const el = elementDetails[i];

    if (el.isHeading) {
      if (currentHeading) {
        const sectionEnd = currentSectionElements.length > 0
          ? currentSectionElements[currentSectionElements.length - 1].bottom
          : currentHeading.bottom;

        const sectionHeight = sectionEnd - currentHeading.top;
        const distanceToNextHeading = el.top - currentHeading.bottom;

        result.push({
          heading: {
            tag: currentHeading.tag,
            text: currentHeading.text,
            position: currentHeading.top
          },
          elements: currentSectionElements,
          distanceToNextHeading,
          sectionHeight
        });
      }

      currentHeading = el;
      currentSectionElements = [];
    } else {
      currentSectionElements.push({
        tag: el.tag,
        text: el.text,
        position: el.top,
        bottom: el.bottom
      });
    }

    // Letztes Element: finalisiere die letzte Section
    const isLastElement = i === elementDetails.length - 1;
    if (isLastElement && currentHeading) {
      const sectionEnd = currentSectionElements.length > 0
        ? currentSectionElements[currentSectionElements.length - 1].bottom
        : currentHeading.bottom;

      const sectionHeight = sectionEnd - currentHeading.top;

      result.push({
        heading: {
          tag: currentHeading.tag,
          text: currentHeading.text,
          position: currentHeading.top
        },
        elements: currentSectionElements,
        distanceToNextHeading: null,
        sectionHeight
      });
    }
  }

  return result;
};

async function runChecksForDevice(browser, url, deviceName, config, activeLayers, consentState) {
    const page = await browser.newPage();
    page.on('console', msg => console.log(`[${config.name} BROWSER LOG]: ${msg.text()}`));

    try {
        console.log(`\n--- Teste Gerät: ${config.name} (${config.width}x${config.height}) ---`);
        await page.setViewport({ width: config.width, height: config.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        if (!consentState.handled) {
            console.log(`[${config.name}] Erster Geräte-Check: Versuche Cookie-Zustimmung zu akzeptieren...`);
            const consentResult = await acceptCookieConsent(page);
            if (consentResult === 1) {
                console.log(`[${config.name}] Cookie-Zustimmung erfolgreich akzeptiert.`);
                consentState.handled = true;
            } else {
                console.warn(`[${config.name}] Cookie-Zustimmung konnte nicht akzeptiert werden (Code: ${consentResult}). Test wird fortgesetzt.`);
            }
        }
        
        console.log(`[${config.name}] Warte auf TCF API-Bestätigung...`);
        try {
            await page.evaluate(() => {
                return new Promise((resolve, reject) => {
                    // Retry mechanism to wait for the TCF API to be ready and successful
                    const checkTcfApi = (retries = 20) => { // ~10 seconds timeout (20 * 500ms)
                        if (typeof window.__tcfapi === 'function') {
                            window.__tcfapi('getTCData', 2, (tcData, success) => {
                                if (success) {
                                    console.log('TCF API success:', tcData);
                                    resolve();
                                } else if (retries > 0) {
                                    setTimeout(() => checkTcfApi(retries - 1), 500);
                                } else {
                                    reject(new Error('TCF API did not return success within the timeout.'));
                                }
                            });
                        } else if (retries > 0) {
                            // If the API function is not yet available, wait and retry
                            setTimeout(() => checkTcfApi(retries - 1), 500);
                        } else {
                            reject(new Error('__tcfapi function not found on page within the timeout.'));
                        }
                    };
                    checkTcfApi();
                });
            });
            console.log(`[${config.name}] TCF API-Bestätigung erfolgreich erhalten.`);
        } catch (e) {
            console.warn(`[${config.name}] Fehler beim Warten auf TCF API: ${e.message}`);
        }

        // A short wait can help stabilize the page before scrolling.
        await new Promise(resolve => setTimeout(resolve, 1000));

        await autoScrollToBottom(page);

        const qmnConfig = await checkSelectors(page);
        if (!qmnConfig) {
            throw new Error(`[${config.name}] window.qmn.config-Objekt konnte nicht abgerufen werden.`);
        }

        const adsByResponsive = groupAdsByResponsive(qmnConfig);
        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Überprüfe ${adsToCheck.length} Anzeigen für Responsive-Typ '${responsiveKey}'.`);

        const issues = {};
        let issueCount = 0;

        if (adsToCheck.length > 0) {
            if (activeLayers.includes('placement')) {
                issueCount += await runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('positioning')) {
                issueCount += await runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('visibility')) {
                issueCount += await runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
        } else {
            console.log(`[${deviceName}] Keine Anzeigen für den Gerätetyp '${responsiveKey}' zu prüfen.`);
        }

        return { deviceName, responsiveKey, issues, issueCount, error: null };

    } catch (e) {
        console.error(`❌ Fehler bei Gerät ${deviceName}:`, e.message);
        return { deviceName, error: e.message, issues: {}, issueCount: 1 };
    } finally {
        /* await page.close(); */
    }
}