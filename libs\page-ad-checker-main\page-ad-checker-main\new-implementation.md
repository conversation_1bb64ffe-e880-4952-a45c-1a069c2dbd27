 for (const deviceName in viewports) {
        const config = viewports[deviceName];
        // Create a promise for each device check without awaiting it immediately
        const deviceCheckPromise = runChecksForDevice(
            browser,
            testJobData.url,
            deviceName,
            config,
            testJobData.layers
        ).then(deviceIssues => {
            // Once a device check is complete, store its issues
            if (Object.keys(deviceIssues).length > 0) {
                allIssues[deviceName] = deviceIssues;
            }
        }).catch(error => {
            // Handle any errors from individual device checks
            console.error(`Error checking device ${deviceName}:`, error);
            allIssues[deviceName] = { generalError: error.message };
        });

        allDevicePromises.push(deviceCheckPromise);
    }

    // Wait for all device check promises to resolve
    console.log("\n--- Starting all device checks concurrently... ---");
    await Promise.all(allDevicePromises);
    console.log("\n--- All device checks completed. ---");

    await browser.close();
    console.log("\nOverall Issues across all devices:", JSON.stringify(allIssues, null, 2));
}