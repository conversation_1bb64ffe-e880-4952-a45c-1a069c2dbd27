import ytdl from "@distube/ytdl-core";
import fs from "fs";
import { join } from "path";

const videoUrl = "https://youtube.com/shorts/da8XUUlHSUc?si=UoaQlj1sGiyqEm5z"; // Example YouTube video URL
const baseFileName = `youtube_download_${Date.now()}`;
const outputDir = "./"; // Save in the current directory

async function downloadVideoAndAudio(url) {
  try {
    const info = await ytdl.getInfo(url);
    console.log("Video Info:", JSON.stringify(info.videoDetails.title, null, 2));

    // Download video (without audio)
    const videoFormat = ytdl.chooseFormat(info.formats, { quality: 'highestvideo', filter: 'videoonly' });
    if (videoFormat) {
      const videoFilePath = join(outputDir, `${baseFileName}_video.mp4`);
      console.log(`Downloading video to: ${videoFilePath}`);
      ytdl(url, { format: videoFormat })
        .pipe(fs.createWriteStream(videoFilePath))
        .on('finish', () => console.log(`Video downloaded successfully to ${videoFilePath}`))
        .on('error', (err) => console.error('Error downloading video:', err));
    } else {
      console.log("No video format found.");
    }

    // Download audio
    const audioFormat = ytdl.chooseFormat(info.formats, { quality: 'highestaudio', filter: 'audioonly' });
    if (audioFormat) {
      const audioFilePath = join(outputDir, `${baseFileName}_audio.mp3`); // Assuming MP3 for audio
      console.log(`Downloading audio to: ${audioFilePath}`);
      ytdl(url, { format: audioFormat })
        .pipe(fs.createWriteStream(audioFilePath))
        .on('finish', () => console.log(`Audio downloaded successfully to ${audioFilePath}`))
        .on('error', (err) => console.error('Error downloading audio:', err));
    } else {
      console.log("No audio format found.");
    }

    // Download captions
    const captions = info.player_response.captions;
    if (captions && captions.playerCaptionsTracklistRenderer && captions.playerCaptionsTracklistRenderer.captionTracks) {
      const captionTracks = captions.playerCaptionsTracklistRenderer.captionTracks;
      const englishCaption = captionTracks.find(track => track.languageCode === 'en'); // Prioritize English captions

      if (englishCaption) {
        const captionUrl = englishCaption.baseUrl;
        const captionFilePath = join(outputDir, `${baseFileName}_captions.vtt`); // VTT format is common

        console.log(`Downloading captions from: ${captionUrl}`);
        const response = await fetch(captionUrl);
        const captionText = await response.text();
        fs.writeFileSync(captionFilePath, captionText);
        console.log(`Captions downloaded successfully to ${captionFilePath}`);
      } else {
        console.log("No English captions found. Available languages:", captionTracks.map(track => track.languageCode).join(', '));
      }
    } else {
      console.log("No captions found for this video.");
    }

  } catch (error) {
    console.error("Error during ytdl-core operation:", error);
  }
}

downloadVideoAndAudio(videoUrl);
