// --- Logic for Layer 3: Visibility Check (executed in the browser context) ---
export const checkVisibilityLogic = (selector) => {
    const el = document.querySelector(selector);
    if (!el) {
        return [{
            id: selector,
            isWithinPageBounds: false,
            isVisibleByCss: false,
            isOccluded: false,
            isPartiallyOutOfViewport: true,
            visibilityPercentage: 0,
            occludingElements: [],
            reason: 'Element not found'
        }];
    }

    const pageScrollHeight = document.body.scrollHeight;
    const pageScrollWidth = document.body.scrollWidth;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    const qmnContainer = el.closest('div[id^="qmn"], div[id="watchbetter-embed"]');
    const parentOfQmnContainer = qmnContainer ? qmnContainer.parentElement : null;

    const rect = el.getBoundingClientRect();
    
    const isRendered = rect.width > 0 && rect.height > 0;

    const scrollTop = window.scrollY || window.pageYOffset;
    const scrollLeft = window.scrollX || window.pageXOffset;
    const isWithinPageBounds = isRendered &&
        (rect.top + scrollTop) >= 0 &&
        (rect.left + scrollLeft) >= 0 &&
        (rect.bottom + scrollTop) <= pageScrollHeight &&
        (rect.right + scrollLeft) <= pageScrollWidth;

    let isVisibleByCss = false;
    if (isRendered) {
        if (typeof el.checkVisibility === 'function') {
            isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
        }
    }
    
    let visiblePoints = 0;
    const occludingElementsRaw = [];
    let pointsOutsideViewport = 0;

    const samplePoints = [
        { x: rect.left + 1, y: rect.top + 1 },
        { x: rect.left + rect.width / 2, y: rect.top + 1 },
        { x: rect.right - 1, y: rect.top + 1 },
        { x: rect.left + 1, y: rect.top + rect.height / 2 },
        { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 },
        { x: rect.right - 1, y: rect.top + rect.height / 2 },
        { x: rect.left + 1, y: rect.bottom - 1 },
        { x: rect.left + rect.width / 2, y: rect.bottom - 1 },
        { x: rect.right - 1, y: rect.bottom - 1 }
    ];

    if (isVisibleByCss && isWithinPageBounds) {
        samplePoints.forEach(point => {
            if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                const elementAtPoint = document.elementFromPoint(point.x, point.y);
                
                if (!elementAtPoint) {
                    return; // Not visible at this point
                }

                if (el.contains(elementAtPoint) || (qmnContainer && elementAtPoint === qmnContainer) || (parentOfQmnContainer && elementAtPoint === parentOfQmnContainer)) {
                    visiblePoints++;
                } else {
                    // Occluded
                    occludingElementsRaw.push({
                        tag: elementAtPoint.tagName,
                        id: elementAtPoint.id || null,
                        className: elementAtPoint.className || null
                    });
                }
            } else {
                pointsOutsideViewport++;
            }
        });
    }

    const visibilityPercentage = Math.round((visiblePoints / samplePoints.length) * 100);
    
    // Deduplicate occluding elements
    const occludingElements = [];
    if (occludingElementsRaw.length > 0) {
        const seenOccluders = new Set();
        for (const o of occludingElementsRaw) {
            const key = `${o.tag}${o.id ? '#' + o.id : ''}${o.className ? '.' + String(o.className).split(' ').join('.') : ''}`;
            if (!seenOccluders.has(key)) {
                seenOccluders.add(key);
                occludingElements.push(o);
            }
        }
    }

    const isOccluded = occludingElements.length > 0;
    const isPartiallyOutOfViewport = pointsOutsideViewport > 0;

    const finalResult = {
        id: el.id,
        isWithinPageBounds,
        isVisibleByCss,
        isOccluded,
        isPartiallyOutOfViewport,
        visibilityPercentage,
        occludingElements
    };

    return [finalResult];
};
