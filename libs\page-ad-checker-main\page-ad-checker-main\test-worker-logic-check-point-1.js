const puppeteer = require('puppeteer');

/* console.log(document.getElementById("qmn5005")) */
/* console.log(window.qmn) */
/* console.log(document.querySelectorAll(".<EMAIL>-first-column")) */
// Hardcoded test data to simulate job.data from the queue
// #mflbuttons-adspiritflash9085201

const testJobData = {
  url: "https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige.html",
  layers: ["placement", "positioning", "visibility"]
};

async function getElementId(elementHandle) {
    if (elementHandle) {
        return await elementHandle.evaluate(el => el.id);
    }
    return null;
}



const autoScrollToBottom = async (page) => {
    await page.evaluate(async () => {
        await new Promise(resolve => {
            let lastScrollHeight = 0;
            let scrollAttempts = 0;
            const maxScrollAttempts = 5; // Number of times to try scrolling without new content

            const scroll = () => {
                const currentScrollHeight = document.body.scrollHeight;
                window.scrollTo({ top: currentScrollHeight, behavior: 'smooth' }); // Added smooth behavior

                if (currentScrollHeight === lastScrollHeight) {
                    scrollAttempts++;
                } else {
                    scrollAttempts = 0; // Reset if new content loaded
                }

                lastScrollHeight = currentScrollHeight;

                if (scrollAttempts >= maxScrollAttempts) {
                    resolve(); // End of page reached
                } else {
                    setTimeout(scroll, 200); // Wait a bit and try again
                }
            };
            scroll();
        });
    });
};


const calculateHeadingDistances = () => {
  // Selektiere alle relevanten Elemente in Erscheinungsreihenfolge
  const elements = Array.from(document.querySelectorAll("h1, h2, h3, h4, h5, h6, p"));

  // Extrahiere Position und Typinformationen
  const getElementDetails = el => {
    const rect = el.getBoundingClientRect();
    const scrollTop = window.scrollY || window.pageYOffset;
  
    return {
      tag: el.tagName,
      text: el.textContent.trim(),
      top: rect.top + scrollTop,
      bottom: rect.bottom + scrollTop,
      isHeading: el.tagName.startsWith('H')
    };
  };
  

  const elementDetails = elements.map(getElementDetails);

  // Sortiere Elemente nach vertikaler Position im Viewport
  elementDetails.sort((a, b) => a.top - b.top);

  const result = [];
  let currentHeading = null;
  let currentSectionElements = [];

  for (let i = 0; i < elementDetails.length; i++) {
    const el = elementDetails[i];

    if (el.isHeading) {
      if (currentHeading) {
        const sectionEnd = currentSectionElements.length > 0
          ? currentSectionElements[currentSectionElements.length - 1].bottom
          : currentHeading.bottom;

        const sectionHeight = sectionEnd - currentHeading.top;
        const distanceToNextHeading = el.top - currentHeading.bottom;

        result.push({
          heading: {
            tag: currentHeading.tag,
            text: currentHeading.text,
            position: currentHeading.top
          },
          elements: currentSectionElements,
          distanceToNextHeading,
          sectionHeight
        });
      }

      currentHeading = el;
      currentSectionElements = [];
    } else {
      currentSectionElements.push({
        tag: el.tag,
        text: el.text,
        position: el.top,
        bottom: el.bottom
      });
    }

    // Letztes Element: finalisiere die letzte Section
    const isLastElement = i === elementDetails.length - 1;
    if (isLastElement && currentHeading) {
      const sectionEnd = currentSectionElements.length > 0
        ? currentSectionElements[currentSectionElements.length - 1].bottom
        : currentHeading.bottom;

      const sectionHeight = sectionEnd - currentHeading.top;

      result.push({
        heading: {
          tag: currentHeading.tag,
          text: currentHeading.text,
          position: currentHeading.top
        },
        elements: currentSectionElements,
        distanceToNextHeading: null,
        sectionHeight
      });
    }
  }

  return result;
};

const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};

const isElementInViewportLogic = (el) => {
    if (!el) return false;
    const rect = el.getBoundingClientRect();
    const vHeight = window.innerHeight || document.documentElement.clientHeight;
    const vWidth = window.innerWidth || document.documentElement.clientWidth;
    
    // Prüft, ob irgendein Teil des Elements im Viewport sichtbar ist.
    const vertInView = rect.top < vHeight && rect.bottom > 0;
    const horInView = rect.left < vWidth && rect.right > 0;
    
    return vertInView && horInView;
};




const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN-Objekt oder qmn.adSlots ist nicht im erwarteten Format.');
    return {};
  }

  return qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type, position } = adSlot;

    // Anzeigen vom Typ 'tracker' und Slots ohne ID/Responsive ignorieren
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Sicherstellen, dass das Array für den Gerätetyp existiert
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Anzeigen-Objekt zur Liste hinzufügen, inklusive Position
    acc[responsive].push({ id, type, position });

    return acc;
  }, {});
};

const checkSelectors = async (page) => {
  const qmnData = await page.evaluate(() => {
    return window.qmn;
  });

  /* console.log( 'QMN Object:' ,JSON.stringify(qmnData, null, 2)) */

  if(!qmnData || !qmnData.config) {
    console.log('⚠️ window.qmn-Objekt oder window.qmn.config nicht auf der Seite gefunden.');
    return null;
  }

  return qmnData.config
}

const viewports = {
  mobile: { width: 375, height: 812, deviceType: 'mobile', name: 'Mobile' },
  tablet: { width: 1024, height: 1366, deviceType: 'desktop', name: 'Tablet' },
  desktop: { width: 1920, height: 1080, deviceType: 'desktop', name: 'Desktop HD' },
};

// --- Logik für Layer 1: Placement Check (wird im Browser-Kontext ausgeführt) ---
const checkPlacementLogic = (ads) => {
    const missing = [];
    for (const ad of ads) {
        const adContainer = document.getElementById(`qmn${ad.id}`);
        if (!adContainer) {
            missing.push(ad);
        }
    }
    return missing;
};

// --- Logik für Layer 2: Positioning Check (wird im Browser-Kontext ausgeführt) ---
const checkPositioningLogic = (ads) => {
    const unpositionable = [];
    for (const ad of ads) {
        if (ad.position && ad.position.htmlSelectors) {
            const selector = ad.position.htmlSelectors;
            const isPlaceable = document.querySelectorAll(selector).length > 0;
            if (!isPlaceable) {
                unpositionable.push({
                    ...ad,
                    result: false
                });
            }
        }
    }
    return unpositionable;
};

// --- Logik für Layer 3: Visibility Check (wird im Browser-Kontext ausgeführt) ---
const checkVisibilityLogic = (selector) => {
    const elements = Array.from(document.querySelectorAll(selector));
    const results = [];
    const pageScrollHeight = document.body.scrollHeight;
    const pageScrollWidth = document.body.scrollWidth;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    /* console.log(  `element 🌳`, JSON.stringify(elements, null, 2)) */

    for (const el of elements) {

        const rect = el.getBoundingClientRect();
        
        const isRendered = rect.width > 0 && rect.height > 0;

        const scrollTop = window.scrollY || window.pageYOffset;
        const scrollLeft = window.scrollX || window.pageXOffset;
        const isWithinPageBounds = isRendered &&
            (rect.top + scrollTop) >= 0 &&
            (rect.left + scrollLeft) >= 0 &&
            (rect.bottom + scrollTop) <= pageScrollHeight &&
            (rect.right + scrollLeft) <= pageScrollWidth;

      
        console.log("result is within pagebound 🎦",isWithinPageBounds )

        let isVisibleByCss = false;
        if (isRendered) {
            if (typeof el.checkVisibility === 'function') {
                isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });

                console.log(`visibility Check Result 🌳: ${isVisibleByCss}`)
            }
        }
        
        let isOccluded = false;
        if (isVisibleByCss) {
            const samplePoints = [
                { x: rect.left + 1, y: rect.top + 1 },
                { x: rect.left + rect.width / 2, y: rect.top + 1 },
                { x: rect.right - 1, y: rect.top + 1 },
                { x: rect.left + 1, y: rect.top + rect.height / 2 },
                { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 },
                { x: rect.right - 1, y: rect.top + rect.height / 2 },
                { x: rect.left + 1, y: rect.bottom - 1 },
                { x: rect.left + rect.width / 2, y: rect.bottom - 1 },
                { x: rect.right - 1, y: rect.bottom - 1 }
            ];

            console.log( `#### 🈯 ####
              
              
              
              

              
              #### 🈯 ####
              `)

  
            const visibilityResults = samplePoints.map(point => {
                if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                    const elementAtPoint = document.elementFromPoint(point.x, point.y);
                    
                    // Log the found element for debugging purposes
                    if (elementAtPoint) {
                        const tag = elementAtPoint.tagName;
                        const id = elementAtPoint.id;
                        const classes = elementAtPoint.className;

                        /* console.log(
                          ` ##### 🈯 ####`,

                          elementAtPoint.outerHTML,
                          
                          ` ##### 🈯 ####`
                        ) */

                        console.log(`[Visibility Check] Point (${point.x.toFixed(0)}, ${point.y.toFixed(0)}): 
                        Found element: ${tag} ${id ? '#'+id : ''}
                        ${classes ? '.'+classes.split(' ').join('.') : ''}}
                        `);


                    } else {

                        console.log(`[Visibility Check] Point (${point.x.toFixed(0)}, ${point.y.toFixed(0)}): Found no element.`);
                    }

                    // Das Element am Prüfpunkt muss entweder das Zielelement selbst oder ein Kindelement davon sein.
                    return elementAtPoint === el || el.contains(elementAtPoint);
                }


              console.log(" is not within viewport  ❌" )

                return false;
            });

            const visiblePoints = visibilityResults.filter(isVisible => isVisible).length;
            const visibilityPercentage = (visiblePoints / samplePoints.length) * 100;
            
            if (visibilityPercentage < 100) {
                isOccluded = true;
            }
        }

        const isTrulyVisible = isWithinPageBounds && isVisibleByCss && !isOccluded;
        results.push({ id: el.id, isWithinPageBounds, isVisibleByCss, isOccluded, isTrulyVisible });
    }
    return results;
};

async function runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unplacedAds = await page.evaluate(checkPlacementLogic, adsToCheck);
    if (unplacedAds.length > 0) {
        if (!issues.placement) issues.placement = { desktop: [], mobile: [] };
        issues.placement[responsiveKey] = unplacedAds;
        console.log(`[${deviceName}] FEHLER [Placement]: ${unplacedAds.length} Anzeigen-Container nicht im DOM gefunden:`, unplacedAds.map(ad => ad.id));
    } else {
        console.log(`[${deviceName}] ERFOLG [Placement]: Alle ${adsToCheck.length} Anzeigen-Container gefunden.`);
    }
    return unplacedAds.length;
}

async function runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unpositionableAds = await page.evaluate(checkPositioningLogic, adsToCheck);
    if (unpositionableAds.length > 0) {
        if (!issues.positioning) issues.positioning = { desktop: [], mobile: [] };
        issues.positioning[responsiveKey] = unpositionableAds;
        console.log(`[${deviceName}] FEHLER [Positioning]: ${unpositionableAds.length} Anzeigen nicht positionierbar (Ziel-Selektor nicht gefunden):`, unpositionableAds.map(ad => ({id: ad.id, selector: ad.position.htmlSelectors})));
    } else {
        console.log(`[${deviceName}] ERFOLG [Positioning]: Alle ${adsToCheck.length} Anzeigen sind positionierbar.`);
    }
    return unpositionableAds.length;
}

async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`### ➡️ [${deviceName}] ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`### ➡️ [${deviceName}] Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        // --- NEW Iframe Discovery Logic based on your specification ---

        // 1. Define the selector for the parent div (e.g., #qmn123 where ad.id is 123)
        const qmnDivSelector = `div[id^="qmn"][id*="${ad.id}"]`;
        console.log(`### ➡️  [${deviceName}] ➡️ Prüfe Ad ID: ${ad.id}. Suche nach div: ${qmnDivSelector}`);

        let iframeHandle = null;
        let parentContainerHandle = null; // This will hold the element handle
        let parentContainerId = null; // To store the ID of the div container
        let insContainerId = null;    // To store the ID of the ins container

        try {
            // Find all potential QMN divs on the page that match the ad.id
            const qmnDivHandles = await page.$$(qmnDivSelector);

            if (qmnDivHandles.length === 0) {
                console.log(`### ➡️  [${deviceName}] ⚠️ WARN: No div element matching "${qmnDivSelector}" found for ad.id ${ad.id}.`);
                // Fallback to the 'asmobj_' logic if the qmn div is not found,
                // as that was observed in tierchenwelt.de
                const asmobjContainerSelector = `div[id*="asmobj_${ad.id}"]`;
                const asmobjContainerHandle = await page.$(asmobjContainerSelector);
                if (asmobjContainerHandle) {
                    parentContainerId = await getElementId(asmobjContainerHandle);
                    iframeHandle = await asmobjContainerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 5000 });
                    if (iframeHandle) {
                        parentContainerHandle = asmobjContainerHandle;
                        console.log(`### ➡️ [${deviceName}] ✅ Found iframe via 'asmobj_' fallback for ad.id ${ad.id}. Parent: ${parentContainerId}`);
                    }
                }

                if (!iframeHandle) { // If still no iframe after fallback
                     const details = { isTrulyVisible: false, reason: `No container div (${qmnDivSelector} or ${asmobjContainerSelector}) found for ad.id.` };
                     notVisibleAds.push({ ...ad, visibilityDetails: details });
                     console.log(`### ➡️ [${deviceName}]   -   Ad ID ${ad.id}: Container div not found.`);
                     continue; // Move to the next ad
                }
            } else {
                // Iterate through found QMN divs
                for (const qmnDiv of qmnDivHandles) {
                    parentContainerId = await getElementId(qmnDiv);
                    console.log(`### ➡️ [${deviceName}]   - Found QMN div: #${parentContainerId}. Searching for ins...`);

                    // 2. Within this div, find all 'ins' elements
                    const insHandles = await qmnDiv.$$('ins');

                    if (insHandles.length === 0) {
                        console.log(`### ➡️ [${deviceName}]     - No 'ins' element found within div #${parentContainerId}.`);
                        // === NEW LOGIC HERE: Log the outerHTML of the div if no ins is found ===
                        const divOuterHTML = await qmnDiv.evaluate(node => node.outerHTML);
                        console.log(`### ➡️ [${deviceName}]     - Full HTML of div #${parentContainerId} (no ins found):`);
                        console.log(divOuterHTML);
                        // =========================================================================
                        continue; // Try next qmnDiv if exists
                    }

                    // 3. Within each 'ins', search for the first 'iframe'
                    for (const ins of insHandles) {
                        insContainerId = await getElementId(ins); // Get ID of the INS element
                        console.log(`### ➡️ [${deviceName}]     - Found ins: #${insContainerId}. Searching for iframe...`);

                        // Find the first iframe within this specific 'ins' element
                        iframeHandle = await ins.$('iframe'); // Using $ to get the first one

                        if (iframeHandle) {
                            parentContainerHandle = qmnDiv;
                            console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside div #${parentContainerId} -> ins #${insContainerId} for ad.id ${ad.id}.`);
                            break; // Found the iframe, stop searching within this ad
                        }
                    }

                    if (iframeHandle) {
                        break; // Found iframe for this ad, exit qmnDiv loop
                    }
                }
            }

        } catch (error) {
            console.error(`### ➡️ [${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`);
            const details = { isTrulyVisible: false, reason: `Error during iframe discovery: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            continue; // Move to the next ad
        }

        // --- End NEW Iframe Discovery Logic ---

        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found after hierarchical search." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe not found after all attempts.`);
            if (parentContainerHandle) await parentContainerHandle.dispose();
            continue; // Move to the next ad
        }

        const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
        console.log(`### ➡️ [${deviceName}] -  Iframe ID found for Ad ID ${ad.id}: ${iframeId}`);

        if (!iframeId) {
            const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe found, but it has no ID.`);
            if (parentContainerHandle) await parentContainerHandle.dispose();
            await iframeHandle.dispose();
            continue; // Move to the next ad
        }

        const adSelector = `#${iframeId}`;

        // --- Ad-Type-Specific Visibility Logic ---
        console.log(`### ➡️ [${deviceName}]   - Applying visibility logic for ad type: ${ad.type}`);

        let floorAdButtonHandle = null; // Handle for the floorad button

        switch (ad.type) {
            case 'floorad':
                console.log(`### ➡️ [${deviceName}]  - Spezifische Logik für 'floorad' wird angewendet.`);
                const floorAdButtonSelector = 'div[id^="mflbuttons-"]';
                floorAdButtonHandle = await page.$(floorAdButtonSelector);

                if (floorAdButtonHandle) {
                    console.log(`### ➡️ [${deviceName}]  - Floorad-Button gefunden. Klicke, um Anzeige zu öffnen.`);
                    await floorAdButtonHandle.click();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for animation
                } else {
                    console.log(`### ➡️ [${deviceName}]  - WARN: Floorad-Button (${floorAdButtonSelector}) nicht gefunden.`);
                }
                break;

            case 'sitebar':
                console.log(`### ➡️ [${deviceName}]  - Spezifische Logik für 'sitebar' wird angewendet.`);
                if (!parentContainerHandle) {
                    console.log(`### ➡️ [${deviceName}]  - WARN: Parent container handle für sitebar ad ${ad.id} nicht gefunden. Scroll-Logik wird übersprungen.`);
                    break;
                }

                let isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                   
                    /* if (typeof el.checkVisibility !== 'function') {
                        return false; // Sollte in modernen Chromium-Versionen vorhanden sein
                    } */
                    return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });

                });
                
                console.log(`### ➡️ [${deviceName}]  - Initiale CSS-Sichtbarkeitsprüfung für Sitebar-Container Ad ${ad.id}: ${isContainerVisibleByCss}`);

                if (isContainerVisibleByCss) {
                    console.log(`### ➡️ [${deviceName}]  - Sitebar-Container für Ad ${ad.id} ist bereits via CSS sichtbar.`);
                } else {
                    console.log(`### ➡️ [${deviceName}]  - Sitebar-Container für Ad ${ad.id} ist via CSS nicht sichtbar. Versuche, ihn durch Scrollen sichtbar zu machen.`);
                    
                    const maxScrollAttempts = 50; // Maximale Anzahl von Scroll-Versuchen
                    let attempts = 0;

                    const scrollPosition = await page.evaluate(() => ({
                        scrollTop: window.scrollY,
                        scrollHeight: document.body.scrollHeight,
                        clientHeight: document.documentElement.clientHeight
                    }));
                    
                    const isCloseToBottom = (scrollPosition.scrollTop + scrollPosition.clientHeight) >= (scrollPosition.scrollHeight - 100);
                    const scrollDirection = isCloseToBottom ? 1 : -1; // -1 für hoch, 1 für runter
                    const scrollAmount = 100; // Pixel pro Versuch

                    console.log(`### ${isCloseToBottom}`)

                    console.log(`### ➡️ [${deviceName}]   - Scrolle ${scrollDirection === 1 ? 'NACH UNTEN' : 'NACH OBEN'}, um den Sitebar-Container zu finden.`);

                    while (!isContainerVisibleByCss && attempts < maxScrollAttempts) {
                        await page.evaluate((y) => { window.scrollBy(0, y); }, scrollDirection * scrollAmount);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                            if (typeof el.checkVisibility !== 'function') return false;
                            return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                        });
                        attempts++;

                        const newScrollTop = await page.evaluate(() => window.scrollY);
                        if ((scrollDirection === -1 && newScrollTop === 0) || 
                            (scrollDirection === 1 && (await page.evaluate(() => (window.innerHeight + window.scrollY) >= document.body.scrollHeight)))) {
                            console.log(`### ➡️ [${deviceName}]   - Seiten${scrollDirection === 1 ? 'ende' : 'anfang'} erreicht. Stoppe die Scroll-Suche.`);
                            break;
                        }
                    }

                    if (isContainerVisibleByCss) {
                        console.log(`### ➡️ [${deviceName}]   - Sitebar-Container ist nach ${attempts} Scroll-Versuchen jetzt via CSS sichtbar.`);
                    } else {
                        console.log(`### ➡️ [${deviceName}]   - WARN: Konnte den Sitebar-Container nach ${attempts} Versuchen nicht via CSS sichtbar machen.`);
                    }
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;

            case 'intext':
            case 'prospekt':
            case 'hpa':
            default: // Standardlogik für bekannte und unbekannte Typen
                console.log(`### ➡️ [${deviceName}]   - Standard-Logik (scrollIntoView) wird angewendet.`);
                // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
                await iframeHandle.evaluate(el => {
                    const rect = el.getBoundingClientRect();
                    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                    // Calculate the target scroll position to place the element 200px above the center
                    const targetY = rect.top + window.scrollY - (viewportHeight / 2) + (rect.height / 2) + 50;
                    window.scrollTo({ top: targetY });
                });
                // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;
        }

        // DEBUG: Überprüfen, ob das Element nach der Aktion im Viewport ist.
        const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
        console.log(`### ➡️ [${deviceName}]   - DEBUG: Element ${adSelector} im Viewport nach Aktion: ${isElementInViewportForDebug}`);

        // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0];

        if (!result || !result.isTrulyVisible) {
            const details = result || { isTrulyVisible: false };
            notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
            const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
            console.log(`### ➡️ [${deviceName}]   - ❌ ${adSelector}: Nicht sichtbar. Grund: ${reason}`);
        } else {
            console.log(`### ➡️ [${deviceName}]   - ✅ ${adSelector}: Sichtbar.`);
        }

        // Schließe die Floorad-Anzeige, falls sie geöffnet wurde
        if (floorAdButtonHandle) {
            console.log(`### ➡️ [${deviceName}]  - Schließe Floorad-Anzeige durch erneuten Klick.`);
            try {
                await floorAdButtonHandle.click();
                await new Promise(resolve => setTimeout(resolve, 2000)); // Warte auf die Schließanimation
            } catch (e) {
                console.log(`### ➡️ [${deviceName}]  - WARN: Floorad-Button konnte nicht erneut geklickt werden (möglicherweise bereits verschwunden): ${e.message}`);
            } finally {
                await floorAdButtonHandle.dispose();
            }
        }

        if (parentContainerHandle) {
            await parentContainerHandle.dispose();
        }
        await iframeHandle.dispose(); // Bereinige das Handle, um Speicherlecks zu vermeiden.
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
}

async function runChecksForDevice(browser, url, deviceName, config, activeLayers) {
    const page = await browser.newPage();
    page.on('console', msg => console.log(`[${config.name} BROWSER LOG]: ${msg.text()}`));

    try {
        console.log(`\n--- Teste Gerät: ${config.name} (${config.width}x${config.height}) ---`);
        await page.setViewport({ width: config.width, height: config.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        const cookieConsentAccepted = await acceptCookieConsent(page);
        if (cookieConsentAccepted !== 1) {
            // Wir werfen hier keinen harten Fehler mehr, sondern loggen ihn und fahren fort.
            console.error(`[${config.name}] Cookie-Zustimmung fehlgeschlagen oder nicht gefunden:❌ ${cookieConsentAccepted}`);
        } else {
            console.log(`[${config.name}] Cookie-Zustimmung akzeptiert ✅, warte auf mögliche Overlays...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        await autoScrollToBottom(page);

        const qmnConfig = await checkSelectors(page);
        if (!qmnConfig) {
            throw new Error(`[${config.name}] window.qmn.config-Objekt konnte nicht abgerufen werden.`);
        }

        const adsByResponsive = groupAdsByResponsive(qmnConfig);
        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Überprüfe ${adsToCheck.length} Anzeigen für Responsive-Typ '${responsiveKey}'.`);

        const issues = {};
        let issueCount = 0;

        if (adsToCheck.length > 0) {
            if (activeLayers.includes('placement')) {
                issueCount += await runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('positioning')) {
                issueCount += await runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('visibility')) {
                issueCount += await runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
        } else {
            console.log(`[${deviceName}] Keine Anzeigen für den Gerätetyp '${responsiveKey}' zu prüfen.`);
        }

        return { deviceName, responsiveKey, issues, issueCount, error: null };

    } catch (e) {
        console.error(`❌ Fehler bei Gerät ${deviceName}:`, e.message);
        return { deviceName, error: e.message, issues: {}, issueCount: 1 };
    } finally {
        await page.close();
    }
}


async function runTest() {
  const { url, layers } = testJobData;
  const activeLayers = layers || ['placement', 'positioning', 'visibility'];

  console.log(`📥 Starte Analyse: ${url} mit Layern [${activeLayers.join(', ')}]`);

  const startTime = Date.now();

  const browser = await puppeteer.launch({
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  try {
    const devicePromises = Object.entries(viewports).map(([deviceName, config]) =>
        runChecksForDevice(browser, url, deviceName, config, activeLayers)
    );

    const deviceResults = await Promise.all(devicePromises);

    const finalIssues = {};
    let totalIssues = 0;
    const deviceErrors = [];

    for (const result of deviceResults) {
        if (result.error) {
            deviceErrors.push({ device: result.deviceName, error: result.error });
        }
        totalIssues += result.issueCount;

        for (const layer of ['placement', 'positioning', 'visibility']) {
            if (result.issues[layer]) {
                if (!finalIssues[layer]) {
                    finalIssues[layer] = { desktop: [], mobile: [] };
                }
                const responsiveKey = result.responsiveKey;
                if (result.issues[layer][responsiveKey]) {
                    finalIssues[layer][responsiveKey].push(...result.issues[layer][responsiveKey]);
                }
            }
        }
    }

    const endTime = Date.now();
    const durationMs = endTime - startTime;
    
    let finalResult;

    if (totalIssues === 0) {
        finalResult = {
            success: true,
            url,
            checkedLayers: activeLayers,
            timestamp: new Date().toISOString(),
            processingTimeMs: durationMs
        };
    } else {
        finalResult = {
            success: false,
            url,
            checkedLayers: activeLayers,
            issues: finalIssues,
            errors: deviceErrors,
            timestamp: new Date().toISOString(),
            processingTimeMs: durationMs
        };
    }

    console.log("final result 📈 :" ,JSON.stringify(finalResult, null, 2))
  
    return finalResult

  } catch (e) {
    console.error(`❌ Kritischer Fehler bei der Testausführung für ${url}:`, e.message);
    return {
        url,
        error: e.message,
        timestamp: new Date().toISOString(),
    }
  } finally {
    /* await browser.close(); */
  }
}

runTest();
