<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Browser-basierter Sichtbarkeits-Test</title>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f4f4f4; }
        h1, h2 { border-bottom: 2px solid #333; padding-bottom: 5px; }
        #results-container { display: flex; flex-wrap: wrap; gap: 20px; }
        .results-section { flex: 1; min-width: 45%; border: 1px solid #ccc; padding: 15px; background-color: #fff; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        iframe { display: none; } /* Das iframe wird nur zum <PERSON> benötigt, nicht zur Anzeige */
    </style>
</head>
<body>
    <h1>Browser-basierter Sichtbarkeits-Test</h1>
    <p>Diese Seite führt die "Statische Analyse + Occlusion Check"-Methode direkt im Browser aus, indem sie die Testseiten in ein unsichtbares iframe lädt.</p>
    
    <div id="results-container">
        <div class="results-section">
            <h2>Ergebnisse für: z-index-test.html</h2>
            <div id="z-index-summary">Wird geladen...</div>
            <table id="z-index-results">
                <thead>
                    <tr>
                        <th>Element ID</th>
                        <th>Erwartet</th>
                        <th>Erhalten</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div class="results-section">
            <h2>Ergebnisse für: long-page.html</h2>
            <p><strong>Hinweis:</strong> Da dieser Test nicht scrollt, werden nur die Elemente im initialen Viewport auf Verdeckung geprüft. Andere werden nur auf CSS-Sichtbarkeit geprüft.</p>
            <div id="long-page-summary">Wartet...</div>
            <table id="long-page-results">
                <thead>
                    <tr>
                        <th>Element ID</th>
                        <th>Erwartet</th>
                        <th>Erhalten</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>

    <iframe id="test-frame"></iframe>

    <script>
        // Dies ist die Kernlogik, angepasst, um im Browser zu laufen und auf ein bestimmtes Dokument zu operieren.
        const staticWithOcclusionCheckLogic = (doc) => {
            const selector = doc.body.dataset.selectorToCheck;
            const elements = Array.from(doc.querySelectorAll(selector));
            const results = [];
            const pageScrollHeight = doc.body.scrollHeight;
            const pageScrollWidth = doc.body.scrollWidth;
            const viewportHeight = doc.defaultView.innerHeight;
            const viewportWidth = doc.defaultView.innerWidth;

            for (const el of elements) {
                const rect = el.getBoundingClientRect();
                
                const isRendered = rect.width > 0 && rect.height > 0;

                const scrollTop = doc.defaultView.scrollY || doc.defaultView.pageYOffset;
                const scrollLeft = doc.defaultView.scrollX || doc.defaultView.pageXOffset;
                const isWithinPageBounds = isRendered &&
                    (rect.top + scrollTop) >= 0 &&
                    (rect.left + scrollLeft) >= 0 &&
                    (rect.bottom + scrollTop) <= pageScrollHeight &&
                    (rect.right + scrollLeft) <= pageScrollWidth;

                let isVisibleByCss = false;
                if (isRendered) {
                    if (typeof el.checkVisibility === 'function') {
                        isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                    } else {
                        const style = doc.defaultView.getComputedStyle(el);
                        isVisibleByCss = style && style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
                    }
                }
                
                let isOccluded = false;
                const isInViewport = rect.top < viewportHeight && rect.bottom > 0 && rect.left < viewportWidth && rect.right > 0;

                if (isVisibleByCss && isInViewport) {
                    const samplePoints = [
                        // 3x3-Raster für eine gründlichere Prüfung
                        { x: rect.left + 1, y: rect.top + 1 },                             // Oben-links
                        { x: rect.left + rect.width / 2, y: rect.top + 1 },                 // Oben-mitte
                        { x: rect.right - 1, y: rect.top + 1 },                            // Oben-rechts
                        { x: rect.left + 1, y: rect.top + rect.height / 2 },               // Mitte-links
                        { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 }, // Mitte-mitte (Zentrum)
                        { x: rect.right - 1, y: rect.top + rect.height / 2 },              // Mitte-rechts
                        { x: rect.left + 1, y: rect.bottom - 1 },                          // Unten-links
                        { x: rect.left + rect.width / 2, y: rect.bottom - 1 },              // Unten-mitte
                        { x: rect.right - 1, y: rect.bottom - 1 }                           // Unten-rechts
                    ];

                    let isVisibleAtAnyPoint = false;
                    for (const point of samplePoints) {
                        if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                            const elementAtPoint = doc.elementFromPoint(point.x, point.y);
                            if (elementAtPoint === el) {
                                isVisibleAtAnyPoint = true;
                                break;
                            }
                        }
                    }
                    if (!isVisibleAtAnyPoint) {
                        isOccluded = true;
                    }
                }

                const isTrulyVisible = isWithinPageBounds && isVisibleByCss && !isOccluded;

                results.push({ 
                    id: el.id, 
                    expected: el.dataset.expectedVisibility === 'true',
                    isTrulyVisible: isTrulyVisible 
                });
            }
            return results;
        };

        // Test-Runner Logik
        document.addEventListener('DOMContentLoaded', () => {
            const testQueue = [
                { url: 'test-pages/z-index-test.html', tableId: 'z-index-results', summaryId: 'z-index-summary' },
                { url: 'test-pages/long-page.html', tableId: 'long-page-results', summaryId: 'long-page-summary' }
            ];

            let currentTestIndex = -1;
            const iframe = document.getElementById('test-frame');

            function runNextTest() {
                currentTestIndex++;
                if (currentTestIndex < testQueue.length) {
                    const test = testQueue[currentTestIndex];
                    console.log(`Starte Test für: ${test.url}`);
                    iframe.src = test.url;
                } else {
                    console.log('Alle Tests abgeschlossen.');
                }
            }

            iframe.onload = () => {
                if (currentTestIndex >= testQueue.length) return;

                const test = testQueue[currentTestIndex];
                const resultsTableBody = document.querySelector(`#${test.tableId} tbody`);
                const summaryDiv = document.getElementById(test.summaryId);

                // Geben Sie dem Browser einen Moment Zeit, um das Rendering abzuschließen.
                setTimeout(() => {
                    const testDoc = iframe.contentDocument;
                    if (!testDoc || !testDoc.body) {
                        console.error(`Konnte Dokument für ${test.url} nicht laden.`);
                        summaryDiv.innerHTML = `<strong class="fail">Fehler beim Laden der Testseite.</strong>`;
                        runNextTest(); // Versuche den nächsten Test
                        return;
                    }

                    const results = staticWithOcclusionCheckLogic(testDoc);
                    
                    let correctCount = 0;
                    resultsTableBody.innerHTML = ''; // Vorherige Ergebnisse löschen

                    results.forEach(result => {
                        const isCorrect = result.isTrulyVisible === result.expected;
                        if (isCorrect) {
                            correctCount++;
                        }

                        const row = resultsTableBody.insertRow();
                        row.className = isCorrect ? 'pass' : 'fail';
                        row.innerHTML = `
                            <td>${result.id}</td>
                            <td>${result.expected}</td>
                            <td>${result.isTrulyVisible}</td>
                            <td>${isCorrect ? 'PASS' : 'FAIL'}</td>
                        `;
                    });

                    const accuracy = results.length > 0 ? (correctCount / results.length) * 100 : 0;
                    summaryDiv.innerHTML = `<strong>Genauigkeit: ${accuracy.toFixed(2)}%</strong> (${correctCount}/${results.length} korrekt)`;
                    
                    // Führe den nächsten Test in der Warteschlange aus
                    runNextTest();

                }, 200); // Eine kleine Verzögerung zur Stabilisierung des Renderings.
            };

            // Starte den ersten Test
            runNextTest();
        });
    </script>
</body>
</html>
