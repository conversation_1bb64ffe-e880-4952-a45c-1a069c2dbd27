<html lang="de-de" dir="ltr">
    
  <div id="in-page-channel-node-id" data-channel-name="in_page_channel_hkcZE0">
    <script
      type="text/javascript"
      async=""
      src="https://www.google-analytics.com/analytics.js"
    ></script>
    <script
      type="text/javascript"
      async=""
      src="https://www.googletagmanager.com/gtag/js?id=G-PCEXYKRWWT&amp;cx=c&amp;gtm=45He56u2v9106682676za200&amp;tag_exp=101509157~103116026~103200004~103233427~103351869~103351871~104573694~104684208~104684211~104718208~104839054~104839056~104908321~104908323"
    ></script>
    <script
      async=""
      src="https://www.googletagmanager.com/gtm.js?id=GTM-5T5K3Z2"
    ></script>
    <script data-cmp-ab="2" src="https://cp.tierchenwelt.de/now.js"></script>
  </div>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="icon" href="/images/stories/favicon.png" sizes="any" />
    <link rel="apple-touch-icon" href="/images/stories/apple_touch_icon.png" />
    <meta charset="utf-8" />
    <meta
      name="robots"
      content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"
    />
    <meta
      name="description"
      content="Suchst du eine praktische Liste mit Göttern und Königen, um deinem Haustier einen besonders ehrenvollen Tiernamen zu geben? Hier bist du richtig!"
    />
    <meta name="generator" content="Joomla! - Open Source Content Management" />
    <title>Tiernamen: Götter / Könige</title>
    <link
      href="/media/system/css/joomla-fontawesome.min.css?4.5.21"
      rel="stylesheet"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
      data-jtaldef-processed="2.0.11"
    />
    <link
      href="/templates/yootheme/css/theme.12.css?1751372419"
      rel="stylesheet"
      data-jtaldef-processed="2.0.11"
    />
    <link
      href="/media/vendor/joomla-custom-elements/css/joomla-alert.min.css?0.4.1"
      rel="stylesheet"
      data-jtaldef-processed="2.0.11"
    />
    <script src="/templates/yootheme/vendor/assets/uikit/dist/js/uikit.min.js?4.5.21"></script>
    <script src="/templates/yootheme/vendor/assets/uikit/dist/js/uikit-icons.min.js?4.5.21"></script>
    <script src="/templates/yootheme/js/theme.js?4.5.21"></script>
    <script type="application/json" class="joomla-script-options loaded">
      {
        "joomla.jtext": {
          "ERROR": "Fehler",
          "MESSAGE": "Nachricht",
          "NOTICE": "Hinweis",
          "WARNING": "Warnung",
          "JCLOSE": "Schließen",
          "JOK": "OK",
          "JOPEN": "Öffnen"
        },
        "system.paths": {
          "root": "",
          "rootFull": "https:\/\/tierchenwelt.de\/",
          "base": "",
          "baseFull": "https:\/\/tierchenwelt.de\/"
        },
        "csrf.token": "2d92fa1b61eac5ec0a0cffb6d99ede22"
      }
    </script>
    <script src="/media/system/js/core.min.js?2cb912"></script>
    <script
      src="/media/system/js/messages.min.js?9a4811"
      type="module"
    ></script>
    <script>
      window.yootheme ||= {};
      var $theme = (yootheme.theme = {
        i18n: {
          close: { label: "Schlie\u00dfen" },
          totop: { label: "Zur\u00fcck nach oben" },
          marker: { label: "\u00d6ffnen" },
          navbarToggleIcon: { label: "Men\u00fc \u00f6ffnen" },
          paginationPrevious: { label: "Vorherige Seite" },
          paginationNext: { label: "N\u00e4chste Seite" },
          searchIcon: {
            toggle: "Suche \u00f6ffnen",
            submit: "Suche ausf\u00fchren",
          },
          slider: {
            next: "N\u00e4chste Folie",
            previous: "Vorherige Folie",
            slideX: "Folie %s",
            slideLabel: "%s von %s",
          },
          slideshow: {
            next: "N\u00e4chste Folie",
            previous: "Vorherige Folie",
            slideX: "Folie %s",
            slideLabel: "%s von %s",
          },
          lightboxPanel: {
            next: "N\u00e4chste Folie",
            previous: "Vorherige Folie",
            slideLabel: "%s von %s",
            close: "Schlie\u00dfen",
          },
        },
      });
    </script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@type": "WebPage",
              "@id": "index.php?Itemid=376",
              "name": "Startseite"
            }
          },
          {
            "@type": "ListItem",
            "position": 2,
            "item": {
              "@type": "WebPage",
              "@id": "index.php?option=com_content&view=article&id=3990&Itemid=254",
              "name": "Tiernamen"
            }
          }
        ],
        "@id": "https://tierchenwelt.de/#/schema/BreadcrumbList/0"
      }
    </script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@type": "WebPage",
              "@id": "index.php?Itemid=376",
              "name": "Startseite"
            }
          },
          {
            "@type": "ListItem",
            "position": 2,
            "item": {
              "@type": "WebPage",
              "@id": "index.php?option=com_content&view=article&id=3990&Itemid=254",
              "name": "Tiernamen"
            }
          },
          {
            "@type": "ListItem",
            "position": 3,
            "item": {
              "@type": "WebPage",
              "@id": "index.php?option=com_content&view=category&layout=blog&id=253&Itemid=265",
              "name": "Tiernamen: Götter / Könige"
            }
          }
        ],
        "@id": "https://www.tierchenwelt.de/#/schema/BreadcrumbList/0"
      }
    </script>
    <script>
      if (!window.location.toString().includes("tierchenwelt.schule")) {
        if (!("gdprAppliesGlobally" in window)) {
          window.gdprAppliesGlobally = true;
        }
        if (!("cmp_id" in window) || window.cmp_id < 1) {
          window.cmp_id = 0;
        }
        if (!("cmp_cdid" in window)) {
          window.cmp_cdid = "da4bc10401ac2";
        }
        if (!("cmp_params" in window)) {
          window.cmp_params = "";
        }
        if (!("cmp_host" in window)) {
          window.cmp_host = "b.delivery.consentmanager.net";
        }
        if (!("cmp_cdn" in window)) {
          window.cmp_cdn = "cdn.consentmanager.net";
        }
        if (!("cmp_proto" in window)) {
          window.cmp_proto = "https:";
        }
        if (!("cmp_codesrc" in window)) {
          window.cmp_codesrc = "1";
        }
        window.cmp_getsupportedLangs = function () {
          var b = [
            "DE",
            "EN",
            "FR",
            "IT",
            "NO",
            "DA",
            "FI",
            "ES",
            "PT",
            "RO",
            "BG",
            "ET",
            "EL",
            "GA",
            "HR",
            "LV",
            "LT",
            "MT",
            "NL",
            "PL",
            "SV",
            "SK",
            "SL",
            "CS",
            "HU",
            "RU",
            "SR",
            "ZH",
            "TR",
            "UK",
            "AR",
            "BS",
          ];
          if ("cmp_customlanguages" in window) {
            for (var a = 0; a < window.cmp_customlanguages.length; a++) {
              b.push(window.cmp_customlanguages[a].l.toUpperCase());
            }
          }
          return b;
        };
        window.cmp_getRTLLangs = function () {
          var a = ["AR"];
          if ("cmp_customlanguages" in window) {
            for (var b = 0; b < window.cmp_customlanguages.length; b++) {
              if (
                "r" in window.cmp_customlanguages[b] &&
                window.cmp_customlanguages[b].r
              ) {
                a.push(window.cmp_customlanguages[b].l);
              }
            }
          }
          return a;
        };
        window.cmp_getlang = function (j) {
          if (typeof j != "boolean") {
            j = true;
          }
          if (
            j &&
            typeof cmp_getlang.usedlang == "string" &&
            cmp_getlang.usedlang !== ""
          ) {
            return cmp_getlang.usedlang;
          }
          var g = window.cmp_getsupportedLangs();
          var c = [];
          var f = location.hash;
          var e = location.search;
          var a = "languages" in navigator ? navigator.languages : [];
          if (f.indexOf("cmplang=") != -1) {
            c.push(f.substr(f.indexOf("cmplang=") + 8, 2).toUpperCase());
          } else {
            if (e.indexOf("cmplang=") != -1) {
              c.push(e.substr(e.indexOf("cmplang=") + 8, 2).toUpperCase());
            } else {
              if ("cmp_setlang" in window && window.cmp_setlang != "") {
                c.push(window.cmp_setlang.toUpperCase());
              } else {
                if (a.length > 0) {
                  for (var d = 0; d < a.length; d++) {
                    c.push(a[d]);
                  }
                }
              }
            }
          }
          if ("language" in navigator) {
            c.push(navigator.language);
          }
          if ("userLanguage" in navigator) {
            c.push(navigator.userLanguage);
          }
          var h = "";
          for (var d = 0; d < c.length; d++) {
            var b = c[d].toUpperCase();
            if (g.indexOf(b) != -1) {
              h = b;
              break;
            }
            if (b.indexOf("-") != -1) {
              b = b.substr(0, 2);
            }
            if (g.indexOf(b) != -1) {
              h = b;
              break;
            }
          }
          if (
            h == "" &&
            typeof cmp_getlang.defaultlang == "string" &&
            cmp_getlang.defaultlang !== ""
          ) {
            return cmp_getlang.defaultlang;
          } else {
            if (h == "") {
              h = "EN";
            }
          }
          h = h.toUpperCase();
          return h;
        };
        (function () {
          var z = document;
          var A = z.getElementsByTagName;
          var j = window;
          var t = "";
          var b = "_en";
          if ("cmp_getlang" in j) {
            t = j.cmp_getlang().toLowerCase();
            if ("cmp_customlanguages" in j) {
              var g = j.cmp_customlanguages;
              for (var v = 0; v < g.length; v++) {
                if (g[v].l.toLowerCase() == t) {
                  t = "en";
                  break;
                }
              }
            }
            b = "_" + t;
          }
          function B(e, E) {
            e += "=";
            var d = "";
            var m = e.length;
            var i = location;
            var F = i.hash;
            var w = i.search;
            var s = F.indexOf(e);
            var D = w.indexOf(e);
            if (s != -1) {
              d = F.substring(s + m, 9999);
            } else {
              if (D != -1) {
                d = w.substring(D + m, 9999);
              } else {
                return E;
              }
            }
            var C = d.indexOf("&");
            if (C != -1) {
              d = d.substring(0, C);
            }
            return d;
          }
          var o = "cmp_proto" in j ? j.cmp_proto : "https:";
          if (o != "http:" && o != "https:") {
            o = "https:";
          }
          var h = "cmp_ref" in j ? j.cmp_ref : location.href;
          if (h.length > 300) {
            h = h.substring(0, 300);
          }
          var n = z.createElement("script");
          n.setAttribute("data-cmp-ab", "1");
          var c = B("cmpdesign", "cmp_design" in j ? j.cmp_design : "");
          var f = B(
            "cmpregulationkey",
            "cmp_regulationkey" in j ? j.cmp_regulationkey : ""
          );
          var x = B("cmpgppkey", "cmp_gppkey" in j ? j.cmp_gppkey : "");
          var q = B("cmpatt", "cmp_att" in j ? j.cmp_att : "");
          n.src =
            o +
            "//" +
            j.cmp_host +
            "/delivery/cmp.php?" +
            ("cmp_id" in j && j.cmp_id > 0 ? "id=" + j.cmp_id : "") +
            ("cmp_cdid" in j ? "&cdid=" + j.cmp_cdid : "") +
            "&h=" +
            encodeURIComponent(h) +
            (c != "" ? "&cmpdesign=" + encodeURIComponent(c) : "") +
            (f != "" ? "&cmpregulationkey=" + encodeURIComponent(f) : "") +
            (x != "" ? "&cmpgppkey=" + encodeURIComponent(x) : "") +
            (q != "" ? "&cmpatt=" + encodeURIComponent(q) : "") +
            ("cmp_params" in j ? "&" + j.cmp_params : "") +
            (z.cookie.length > 0 ? "&__cmpfcc=1" : "") +
            "&l=" +
            t.toLowerCase() +
            "&o=" +
            new Date().getTime();
          n.type = "text/javascript";
          n.async = true;
          if (z.currentScript && z.currentScript.parentElement) {
            z.currentScript.parentElement.appendChild(n);
          } else {
            if (z.body) {
              z.body.appendChild(n);
            } else {
              var r = ["body", "div", "span", "script", "head"];
              for (var v = 0; v < r.length; v++) {
                var y = A(r[v]);
                if (y.length > 0) {
                  y[0].appendChild(n);
                  break;
                }
              }
            }
          }
          var p = "js";
          var u =
            B(
              "cmpdebugunminimized",
              "cmpdebugunminimized" in j ? j.cmpdebugunminimized : 0
            ) > 0
              ? ""
              : ".min";
          var a = B(
            "cmpdebugcoverage",
            "cmp_debugcoverage" in j ? j.cmp_debugcoverage : ""
          );
          if (a == "1") {
            p = "instrumented";
            u = "";
          }
          var k = B(
            "cmpdebugtest",
            "cmp_debugtest" in j ? j.cmp_debugtest : ""
          );
          if (k == "1") {
            p = "jstests";
            u = "";
          }
          var n = z.createElement("script");
          n.src =
            o + "//" + j.cmp_cdn + "/delivery/" + p + "/cmp" + b + u + ".js";
          n.type = "text/javascript";
          n.setAttribute("data-cmp-ab", "1");
          n.async = true;
          if (z.currentScript && z.currentScript.parentElement) {
            z.currentScript.parentElement.appendChild(n);
          } else {
            if (z.body) {
              z.body.appendChild(n);
            } else {
              var y = A("body");
              if (y.length == 0) {
                y = A("div");
              }
              if (y.length == 0) {
                y = A("span");
              }
              if (y.length == 0) {
                y = A("ins");
              }
              if (y.length == 0) {
                y = A("script");
              }
              if (y.length == 0) {
                y = A("head");
              }
              if (y.length > 0) {
                y[0].appendChild(n);
              }
            }
          }
        })();
        window.cmp_addFrame = function (b) {
          if (!window.frames[b]) {
            if (document.body) {
              var a = document.createElement("iframe");
              a.style.cssText = "display:none";
              if (
                "cmp_cdn" in window &&
                "cmp_ultrablocking" in window &&
                window.cmp_ultrablocking > 0
              ) {
                a.src = "//" + window.cmp_cdn + "/delivery/empty.html";
              }
              a.name = b;
              a.setAttribute("title", "Intentionally hidden, please ignore");
              a.setAttribute("role", "none");
              a.setAttribute("tabindex", "-1");
              document.body.appendChild(a);
            } else {
              window.setTimeout(window.cmp_addFrame, 10, b);
            }
          }
        };
        window.cmp_rc = function (c, b) {
          var j = document.cookie;
          var g = "";
          var e = 0;
          var f = false;
          while (j != "" && e < 100) {
            e++;
            while (j.substr(0, 1) == " ") {
              j = j.substr(1, j.length);
            }
            var h = j.substring(0, j.indexOf("="));
            if (j.indexOf(";") != -1) {
              var k = j.substring(j.indexOf("=") + 1, j.indexOf(";"));
            } else {
              var k = j.substr(j.indexOf("=") + 1, j.length);
            }
            if (c == h) {
              g = k;
              f = true;
            }
            var d = j.indexOf(";") + 1;
            if (d == 0) {
              d = j.length;
            }
            j = j.substring(d, j.length);
          }
          if (!f && typeof b == "string") {
            g = b;
          }
          return g;
        };
        window.cmp_stub = function () {
          var a = arguments;
          __cmp.a = __cmp.a || [];
          if (!a.length) {
            return __cmp.a;
          } else {
            if (a[0] === "ping") {
              if (a[1] === 2) {
                a[2](
                  {
                    gdprApplies: gdprAppliesGlobally,
                    cmpLoaded: false,
                    cmpStatus: "stub",
                    displayStatus: "hidden",
                    apiVersion: "2.2",
                    cmpId: 31,
                  },
                  true
                );
              } else {
                a[2](false, true);
              }
            } else {
              if (a[0] === "getUSPData") {
                a[2](
                  {
                    version: 1,
                    uspString: window.cmp_rc("__cmpccpausps", "1---"),
                  },
                  true
                );
              } else {
                if (a[0] === "getTCData") {
                  __cmp.a.push([].slice.apply(a));
                } else {
                  if (
                    a[0] === "addEventListener" ||
                    a[0] === "removeEventListener"
                  ) {
                    __cmp.a.push([].slice.apply(a));
                  } else {
                    if (a.length == 4 && a[3] === false) {
                      a[2]({}, false);
                    } else {
                      __cmp.a.push([].slice.apply(a));
                    }
                  }
                }
              }
            }
          }
        };
        window.cmp_gpp_ping = function () {
          return {
            gppVersion: "1.1",
            cmpStatus: "stub",
            cmpDisplayStatus: "hidden",
            signalStatus: "not ready",
            supportedAPIs: [
              "5:tcfcav1",
              "7:usnat",
              "8:usca",
              "9:usva",
              "10:usco",
              "11:usut",
              "12:usct",
            ],
            cmpId: 31,
            sectionList: [],
            applicableSections: [0],
            gppString: "",
            parsedSections: {},
          };
        };
        window.cmp_gppstub = function () {
          var c = arguments;
          __gpp.q = __gpp.q || [];
          if (!c.length) {
            return __gpp.q;
          }
          var h = c[0];
          var g = c.length > 1 ? c[1] : null;
          var f = c.length > 2 ? c[2] : null;
          var a = null;
          var j = false;
          if (h === "ping") {
            a = window.cmp_gpp_ping();
            j = true;
          } else {
            if (h === "addEventListener") {
              __gpp.e = __gpp.e || [];
              if (!("lastId" in __gpp)) {
                __gpp.lastId = 0;
              }
              __gpp.lastId++;
              var d = __gpp.lastId;
              __gpp.e.push({ id: d, callback: g });
              a = {
                eventName: "listenerRegistered",
                listenerId: d,
                data: true,
                pingData: window.cmp_gpp_ping(),
              };
              j = true;
            } else {
              if (h === "removeEventListener") {
                __gpp.e = __gpp.e || [];
                a = false;
                for (var e = 0; e < __gpp.e.length; e++) {
                  if (__gpp.e[e].id == f) {
                    __gpp.e[e].splice(e, 1);
                    a = true;
                    break;
                  }
                }
                j = true;
              } else {
                __gpp.q.push([].slice.apply(c));
              }
            }
          }
          if (a !== null && typeof g === "function") {
            g(a, j);
          }
        };
        window.cmp_msghandler = function (d) {
          var a = typeof d.data === "string";
          try {
            var c = a ? JSON.parse(d.data) : d.data;
          } catch (f) {
            var c = null;
          }
          if (typeof c === "object" && c !== null && "__cmpCall" in c) {
            var b = c.__cmpCall;
            window.__cmp(b.command, b.parameter, function (h, g) {
              var e = {
                __cmpReturn: { returnValue: h, success: g, callId: b.callId },
              };
              d.source.postMessage(a ? JSON.stringify(e) : e, "*");
            });
          }
          if (typeof c === "object" && c !== null && "__uspapiCall" in c) {
            var b = c.__uspapiCall;
            window.__uspapi(b.command, b.version, function (h, g) {
              var e = {
                __uspapiReturn: {
                  returnValue: h,
                  success: g,
                  callId: b.callId,
                },
              };
              d.source.postMessage(a ? JSON.stringify(e) : e, "*");
            });
          }
          if (typeof c === "object" && c !== null && "__tcfapiCall" in c) {
            var b = c.__tcfapiCall;
            window.__tcfapi(
              b.command,
              b.version,
              function (h, g) {
                var e = {
                  __tcfapiReturn: {
                    returnValue: h,
                    success: g,
                    callId: b.callId,
                  },
                };
                d.source.postMessage(a ? JSON.stringify(e) : e, "*");
              },
              b.parameter
            );
          }
          if (typeof c === "object" && c !== null && "__gppCall" in c) {
            var b = c.__gppCall;
            window.__gpp(
              b.command,
              function (h, g) {
                var e = {
                  __gppReturn: { returnValue: h, success: g, callId: b.callId },
                };
                d.source.postMessage(a ? JSON.stringify(e) : e, "*");
              },
              "parameter" in b ? b.parameter : null,
              "version" in b ? b.version : 1
            );
          }
        };
        window.cmp_setStub = function (a) {
          if (
            !(a in window) ||
            (typeof window[a] !== "function" &&
              typeof window[a] !== "object" &&
              (typeof window[a] === "undefined" || window[a] !== null))
          ) {
            window[a] = window.cmp_stub;
            window[a].msgHandler = window.cmp_msghandler;
            window.addEventListener("message", window.cmp_msghandler, false);
          }
        };
        window.cmp_setGppStub = function (a) {
          if (
            !(a in window) ||
            (typeof window[a] !== "function" &&
              typeof window[a] !== "object" &&
              (typeof window[a] === "undefined" || window[a] !== null))
          ) {
            window[a] = window.cmp_gppstub;
            window[a].msgHandler = window.cmp_msghandler;
            window.addEventListener("message", window.cmp_msghandler, false);
          }
        };
        window.cmp_addFrame("__cmpLocator");
        if (!("cmp_disableusp" in window) || !window.cmp_disableusp) {
          window.cmp_addFrame("__uspapiLocator");
        }
        if (!("cmp_disabletcf" in window) || !window.cmp_disabletcf) {
          window.cmp_addFrame("__tcfapiLocator");
        }
        if (!("cmp_disablegpp" in window) || !window.cmp_disablegpp) {
          window.cmp_addFrame("__gppLocator");
        }
        window.cmp_setStub("__cmp");
        if (!("cmp_disabletcf" in window) || !window.cmp_disabletcf) {
          window.cmp_setStub("__tcfapi");
        }
        if (!("cmp_disableusp" in window) || !window.cmp_disableusp) {
          window.cmp_setStub("__uspapi");
        }
        if (!("cmp_disablegpp" in window) || !window.cmp_disablegpp) {
          window.cmp_setGppStub("__gpp");
        }
      }
    </script>
    <script
      data-cmp-ab="1"
      src="https://b.delivery.consentmanager.net/delivery/cmp.php?&amp;cdid=da4bc10401ac2&amp;h=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;&amp;__cmpfcc=1&amp;l=fr&amp;o=1751436281740"
      type="text/javascript"
      async=""
    ></script>
    <script
      src="https://cdn.consentmanager.net/delivery/js/cmp_fr.min.js"
      type="text/javascript"
      data-cmp-ab="1"
      async=""
    ></script>

    <script
      class="cmplazyload"
      data-cmp-newid="8780259"
      data-cmp-ab="1"
      type="text/javascript"
      async=""
      src="https://cdn.qualitymedianetwork.de/delivery/tierchenwelt.de.js"
      data-cmp-done="1"
      data-cmp-activated="1"
    ></script>
    <script
      data-cmp-vendor="s1541"
      data-cmp-src="https://cdn.qualitymedianetwork.de/delivery/tierchenwelt.de.js"
      async=""
      data-cmp-done="1"
      data-cmp-ab="1"
      data-cmp-orgid="8780259"
      class="cmporgscript"
      data-cmp-activated="1"
    ></script>
    <style>
      @keyframes slide-in-one-tap {
        from {
          transform: translateY(80px);
        }
        to {
          transform: translateY(0px);
        }
      }

      .trust-hide-gracefully {
        opacity: 0;
      }

      .trust-wallet-one-tap .hidden {
        display: none;
      }

      .trust-wallet-one-tap .semibold {
        font-weight: 500;
      }

      .trust-wallet-one-tap .binance-plex {
        font-family: "Binance";
      }

      .trust-wallet-one-tap .rounded-full {
        border-radius: 50%;
      }

      .trust-wallet-one-tap .flex {
        display: flex;
      }

      .trust-wallet-one-tap .flex-col {
        flex-direction: column;
      }

      .trust-wallet-one-tap .items-center {
        align-items: center;
      }

      .trust-wallet-one-tap .space-between {
        justify-content: space-between;
      }

      .trust-wallet-one-tap .justify-center {
        justify-content: center;
      }

      .trust-wallet-one-tap .w-full {
        width: 100%;
      }

      .trust-wallet-one-tap .box {
        transition: all 0.5s cubic-bezier(0, 0, 0, 1.43);
        animation: slide-in-one-tap 0.5s cubic-bezier(0, 0, 0, 1.43);
        width: 384px;
        border-radius: 15px;
        background: #fff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
        position: fixed;
        right: 30px;
        bottom: 30px;
        z-index: 1020;
      }

      .trust-wallet-one-tap .header {
        gap: 15px;
        border-bottom: 1px solid #e6e6e6;
        padding: 10px 18px;
      }

      .trust-wallet-one-tap .header .left-items {
        gap: 15px;
      }

      .trust-wallet-one-tap .header .title {
        color: #1e2329;
        font-size: 18px;
        font-weight: 600;
        line-height: 28px;
      }

      .trust-wallet-one-tap .header .subtitle {
        color: #474d57;
        font-size: 14px;
        line-height: 20px;
      }

      .trust-wallet-one-tap .header .close {
        color: #1e2329;
        cursor: pointer;
      }

      .trust-wallet-one-tap .body {
        padding: 9px 18px;
        gap: 10px;
      }

      .trust-wallet-one-tap .body .right-items {
        gap: 10px;
        width: 100%;
      }

      .trust-wallet-one-tap .body .right-items .wallet-title {
        color: #1e2329;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;
      }

      .trust-wallet-one-tap .body .right-items .wallet-subtitle {
        color: #474d57;
        font-size: 14px;
        line-height: 20px;
      }

      .trust-wallet-one-tap .connect-indicator {
        gap: 15px;
        padding: 8px 0;
      }

      .trust-wallet-one-tap .connect-indicator .flow-icon {
        color: #474d57;
      }

      .trust-wallet-one-tap .loading-color {
        color: #fff;
      }

      .trust-wallet-one-tap .button {
        border-radius: 50px;
        outline: 2px solid transparent;
        outline-offset: 2px;
        background-color: rgb(5, 0, 255);
        border-color: rgb(229, 231, 235);
        cursor: pointer;
        text-align: center;
        height: 45px;
      }

      .trust-wallet-one-tap .button .button-text {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;
      }

      .trust-wallet-one-tap .footer {
        margin: 20px 30px;
      }

      .trust-wallet-one-tap .check-icon {
        color: #fff;
      }

      @font-face {
        font-family: "Binance";
        src: url(chrome-extension://egjidjbpglichdcondbcbdnbeeppgdph/fonts/BinancePlex-Regular.otf)
          format("opentype");
        font-weight: 400;
        font-style: normal;
      }

      @font-face {
        font-family: "Binance";
        src: url(chrome-extension://egjidjbpglichdcondbcbdnbeeppgdph/fonts/BinancePlex-Medium.otf)
          format("opentype");
        font-weight: 500;
        font-style: normal;
      }

      @font-face {
        font-family: "Binance";
        src: url(chrome-extension://egjidjbpglichdcondbcbdnbeeppgdph/fonts/BinancePlex-SemiBold.otf)
          format("opentype");
        font-weight: 600;
        font-style: normal;
      }

      /*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL2FwcC9zcmMvb25lVGFwL3N0eWxlLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFO0lBQ0UsMkJBQTJCO0VBQzdCO0VBQ0E7SUFDRSwwQkFBMEI7RUFDNUI7QUFDRjs7QUFFQTtFQUNFLFVBQVU7QUFDWjs7QUFHRTtJQUNFLGFBQWE7RUFDZjs7QUFFQTtJQUNFLGdCQUFnQjtFQUNsQjs7QUFFQTtJQUNFLHNCQUFzQjtFQUN4Qjs7QUFFQTtJQUNFLGtCQUFrQjtFQUNwQjs7QUFFQTtJQUNFLGFBQWE7RUFDZjs7QUFFQTtJQUNFLHNCQUFzQjtFQUN4Qjs7QUFFQTtJQUNFLG1CQUFtQjtFQUNyQjs7QUFFQTtJQUNFLDhCQUE4QjtFQUNoQzs7QUFFQTtJQUNFLHVCQUF1QjtFQUN6Qjs7QUFFQTtJQUNFLFdBQVc7RUFDYjs7QUFFQTtJQUNFLGdEQUFnRDtJQUNoRCw0REFBNEQ7SUFDNUQsWUFBWTtJQUNaLG1CQUFtQjtJQUNuQixnQkFBZ0I7SUFDaEIsK0NBQStDO0lBQy9DLGVBQWU7SUFDZixXQUFXO0lBQ1gsWUFBWTtJQUNaLGFBQWE7RUFDZjs7QUFFQTtJQUNFLFNBQVM7SUFDVCxnQ0FBZ0M7SUFDaEMsa0JBQWtCO0VBdUJwQjs7QUFyQkU7TUFDRSxTQUFTO0lBQ1g7O0FBRUE7TUFDRSxjQUFjO01BQ2QsZUFBZTtNQUNmLGdCQUFnQjtNQUNoQixpQkFBaUI7SUFDbkI7O0FBRUE7TUFDRSxjQUFjO01BQ2QsZUFBZTtNQUNmLGlCQUFpQjtJQUNuQjs7QUFFQTtNQUNFLGNBQWM7TUFDZCxlQUFlO0lBQ2pCOztBQUdGO0lBQ0UsaUJBQWlCO0lBQ2pCLFNBQVM7RUFtQlg7O0FBakJFO01BQ0UsU0FBUztNQUNULFdBQVc7SUFjYjs7QUFaRTtRQUNFLGNBQWM7UUFDZCxlQUFlO1FBQ2YsZ0JBQWdCO1FBQ2hCLGlCQUFpQjtNQUNuQjs7QUFFQTtRQUNFLGNBQWM7UUFDZCxlQUFlO1FBQ2YsaUJBQWlCO01BQ25COztBQUlKO0lBQ0UsU0FBUztJQUNULGNBQWM7RUFLaEI7O0FBSEU7TUFDRSxjQUFjO0lBQ2hCOztBQUdGO0lBQ0UsV0FBVztFQUNiOztBQUVBO0lBQ0UsbUJBQW1CO0lBQ25CLDhCQUE4QjtJQUM5QixtQkFBbUI7SUFDbkIsZ0NBQWdDO0lBQ2hDLGdDQUFnQztJQUNoQyxlQUFlO0lBQ2Ysa0JBQWtCO0lBQ2xCLFlBQVk7RUFRZDs7QUFORTtNQUNFLFdBQVc7TUFDWCxlQUFlO01BQ2YsZ0JBQWdCO01BQ2hCLGlCQUFpQjtJQUNuQjs7QUFHRjtJQUNFLGlCQUFpQjtFQUNuQjs7QUFFQTtJQUNFLFdBQVc7RUFDYjs7QUFHRjtFQUNFLHNCQUFzQjtFQUN0QiwrREFBMEU7RUFDMUUsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLHNCQUFzQjtFQUN0QiwrREFBeUU7RUFDekUsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLHNCQUFzQjtFQUN0QiwrREFBMkU7RUFDM0UsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtBQUNwQiIsInNvdXJjZXNDb250ZW50IjpbIkBrZXlmcmFtZXMgc2xpZGUtaW4tb25lLXRhcCB7XG4gIGZyb20ge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSg4MHB4KTtcbiAgfVxuICB0byB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDBweCk7XG4gIH1cbn1cblxuLnRydXN0LWhpZGUtZ3JhY2VmdWxseSB7XG4gIG9wYWNpdHk6IDA7XG59XG5cbi50cnVzdC13YWxsZXQtb25lLXRhcCB7XG4gIC5oaWRkZW4ge1xuICAgIGRpc3BsYXk6IG5vbmU7XG4gIH1cblxuICAuc2VtaWJvbGQge1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIH1cblxuICAuYmluYW5jZS1wbGV4IHtcbiAgICBmb250LWZhbWlseTogJ0JpbmFuY2UnO1xuICB9XG5cbiAgLnJvdW5kZWQtZnVsbCB7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB9XG5cbiAgLmZsZXgge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gIH1cblxuICAuZmxleC1jb2wge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIH1cblxuICAuaXRlbXMtY2VudGVyIHtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICB9XG5cbiAgLnNwYWNlLWJldHdlZW4ge1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgfVxuXG4gIC5qdXN0aWZ5LWNlbnRlciB7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIH1cblxuICAudy1mdWxsIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgfVxuXG4gIC5ib3gge1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjVzIGN1YmljLWJlemllcigwLCAwLCAwLCAxLjQzKTtcbiAgICBhbmltYXRpb246IHNsaWRlLWluLW9uZS10YXAgMC41cyBjdWJpYy1iZXppZXIoMCwgMCwgMCwgMS40Myk7XG4gICAgd2lkdGg6IDM4NHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gICAgYmFja2dyb3VuZDogI2ZmZjtcbiAgICBib3gtc2hhZG93OiAwcHggMnB4IDRweCAwcHggcmdiYSgwLCAwLCAwLCAwLjI1KTtcbiAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgcmlnaHQ6IDMwcHg7XG4gICAgYm90dG9tOiAzMHB4O1xuICAgIHotaW5kZXg6IDEwMjA7XG4gIH1cblxuICAuaGVhZGVyIHtcbiAgICBnYXA6IDE1cHg7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNmU2ZTY7XG4gICAgcGFkZGluZzogMTBweCAxOHB4O1xuXG4gICAgLmxlZnQtaXRlbXMge1xuICAgICAgZ2FwOiAxNXB4O1xuICAgIH1cblxuICAgIC50aXRsZSB7XG4gICAgICBjb2xvcjogIzFlMjMyOTtcbiAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBsaW5lLWhlaWdodDogMjhweDtcbiAgICB9XG5cbiAgICAuc3VidGl0bGUge1xuICAgICAgY29sb3I6ICM0NzRkNTc7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBsaW5lLWhlaWdodDogMjBweDtcbiAgICB9XG5cbiAgICAuY2xvc2Uge1xuICAgICAgY29sb3I6ICMxZTIzMjk7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgfVxuICB9XG5cbiAgLmJvZHkge1xuICAgIHBhZGRpbmc6IDlweCAxOHB4O1xuICAgIGdhcDogMTBweDtcblxuICAgIC5yaWdodC1pdGVtcyB7XG4gICAgICBnYXA6IDEwcHg7XG4gICAgICB3aWR0aDogMTAwJTtcblxuICAgICAgLndhbGxldC10aXRsZSB7XG4gICAgICAgIGNvbG9yOiAjMWUyMzI5O1xuICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4O1xuICAgICAgfVxuXG4gICAgICAud2FsbGV0LXN1YnRpdGxlIHtcbiAgICAgICAgY29sb3I6ICM0NzRkNTc7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgbGluZS1oZWlnaHQ6IDIwcHg7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLmNvbm5lY3QtaW5kaWNhdG9yIHtcbiAgICBnYXA6IDE1cHg7XG4gICAgcGFkZGluZzogOHB4IDA7XG5cbiAgICAuZmxvdy1pY29uIHtcbiAgICAgIGNvbG9yOiAjNDc0ZDU3O1xuICAgIH1cbiAgfVxuXG4gIC5sb2FkaW5nLWNvbG9yIHtcbiAgICBjb2xvcjogI2ZmZjtcbiAgfVxuXG4gIC5idXR0b24ge1xuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XG4gICAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xuICAgIG91dGxpbmUtb2Zmc2V0OiAycHg7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDUsIDAsIDI1NSk7XG4gICAgYm9yZGVyLWNvbG9yOiByZ2IoMjI5LCAyMzEsIDIzNSk7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBoZWlnaHQ6IDQ1cHg7XG5cbiAgICAuYnV0dG9uLXRleHQge1xuICAgICAgY29sb3I6ICNmZmY7XG4gICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgbGluZS1oZWlnaHQ6IDIwcHg7XG4gICAgfVxuICB9XG5cbiAgLmZvb3RlciB7XG4gICAgbWFyZ2luOiAyMHB4IDMwcHg7XG4gIH1cblxuICAuY2hlY2staWNvbiB7XG4gICAgY29sb3I6ICNmZmY7XG4gIH1cbn1cblxuQGZvbnQtZmFjZSB7XG4gIGZvbnQtZmFtaWx5OiAnQmluYW5jZSc7XG4gIHNyYzogdXJsKCcuL2ZvbnRzL2JpbmFuY2VQbGV4L0JpbmFuY2VQbGV4LVJlZ3VsYXIub3RmJykgZm9ybWF0KCdvcGVudHlwZScpO1xuICBmb250LXdlaWdodDogNDAwO1xuICBmb250LXN0eWxlOiBub3JtYWw7XG59XG5cbkBmb250LWZhY2Uge1xuICBmb250LWZhbWlseTogJ0JpbmFuY2UnO1xuICBzcmM6IHVybCgnLi9mb250cy9iaW5hbmNlUGxleC9CaW5hbmNlUGxleC1NZWRpdW0ub3RmJykgZm9ybWF0KCdvcGVudHlwZScpO1xuICBmb250LXdlaWdodDogNTAwO1xuICBmb250LXN0eWxlOiBub3JtYWw7XG59XG5cbkBmb250LWZhY2Uge1xuICBmb250LWZhbWlseTogJ0JpbmFuY2UnO1xuICBzcmM6IHVybCgnLi9mb250cy9iaW5hbmNlUGxleC9CaW5hbmNlUGxleC1TZW1pQm9sZC5vdGYnKSBmb3JtYXQoJ29wZW50eXBlJyk7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */
    </style>
    <style>
      .lottie-animation-container {
        width: var(--lottie-animation-container-width);
        height: var(--lottie-animation-container-height);
        background-color: var(--lottie-animation-container-background-color);
        overflow: hidden;
        margin: var(--lottie-animation-margin);
      }
      .lottie-animation-container svg {
        transform: scale(var(--lottie-animation-scale));
      }
    </style>
    <style>
      .lottie-animation-container {
        width: var(--lottie-animation-container-width);
        height: var(--lottie-animation-container-height);
        background-color: var(--lottie-animation-container-background-color);
        overflow: hidden;
        margin: var(--lottie-animation-margin);
      }
      .lottie-animation-container svg {
        transform: scale(var(--lottie-animation-scale));
      }
    </style>
    <script
      type="text/javascript"
      src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
      async=""
    ></script>
    <meta
      http-equiv="origin-trial"
      content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="
    />
    <meta
      http-equiv="origin-trial"
      content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="
    />
    <meta
      http-equiv="origin-trial"
      content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"
    />
    <meta
      http-equiv="origin-trial"
      content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"
    />
    <script
      src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202506260101/pubads_impl.js"
      async=""
    ></script>
    <link
      href="https://securepubads.g.doubleclick.net/pagead/managed/dict/m202507010101/gpt"
      rel="compression-dictionary"
    />
  </head>
  <body class="option-com_content view-category layout-blog tld-de">
    <div id="cmpwrapper" class="cmpwrapper"></div>

    <div
      class="uk-hidden-visually uk-notification uk-notification-top-left uk-width-auto"
    >
      <div class="uk-notification-message">
        <a href="#tm-main" class="uk-link-reset">Zum Hauptinhalt springen</a>
      </div>
    </div>

    <div class="tm-page-container uk-clearfix">
      <header class="tm-header-mobile uk-hidden@m">
        <div class="uk-navbar-container">
          <div class="uk-container uk-container-expand">
            <nav
              class="uk-navbar"
              uk-navbar='{"align":"left","container":".tm-header-mobile","boundary":".tm-header-mobile .uk-navbar-container"}'
            >
              <div class="uk-navbar-left">
                <div class="uk-navbar-item" id="module-594">
                  <div class="uk-margin-remove-last-child custom">
                    <p>
                      <a href="/"
                        ><img
                          src="/images/stories/logo_tierchenwelt.png"
                          alt="tierchenwelt.de - Das Tierlexikon für Kinder"
                          width="147"
                          height="50"
                          style="
                            margin-left: 0px;
                            margin-bottom: 10px;
                            float: left;
                          "
                          title="tierchenwelt.de - Das Tierlexikon für Kinder"
                      /></a>
                    </p>
                  </div>
                </div>
              </div>

              <div class="uk-navbar-right">
                <a
                  uk-toggle=""
                  href="#tm-dialog-mobile"
                  class="uk-navbar-toggle uk-navbar-toggle-animate"
                  role="button"
                  aria-label="Menü öffnen"
                  aria-expanded="false"
                >
                  <span class="uk-margin-small-right uk-text-middle">Menü</span>

                  <div
                    uk-navbar-toggle-icon=""
                    class="uk-icon uk-navbar-toggle-icon"
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <rect width="20" height="2" y="3" class="line-1"></rect>
                      <rect width="20" height="2" y="9" class="line-2"></rect>
                      <rect width="20" height="2" y="9" class="line-3"></rect>
                      <rect width="20" height="2" y="15" class="line-4"></rect>
                    </svg>
                  </div>
                </a>
              </div>
            </nav>
          </div>
        </div>

        <div
          id="tm-dialog-mobile"
          class="uk-dropbar uk-dropbar-top uk-drop"
          uk-drop='{"clsDrop":"uk-dropbar","flip":"false","container":".tm-header-mobile","target-y":".tm-header-mobile .uk-navbar-container","mode":"click","target-x":".tm-header-mobile .uk-navbar-container","stretch":true,"pos":"bottom-left","bgScroll":"false","animateOut":true,"duration":300,"toggle":"false"}'
        >
          <div class="tm-height-min-1-1 uk-flex uk-flex-column">
            <div class="uk-margin-auto-bottom">
              <div class="uk-grid uk-child-width-1-1 uk-grid-stack" uk-grid="">
                <div>
                  <div class="uk-panel _menu" id="module-595">
                    <ul class="nav-pills uk-nav uk-nav-default">
                      <li class="item-54 uk-parent">
                        <a href="/tierarten.html">Tierarten</a>
                        <ul class="uk-nav-sub">
                          <li class="item-55">
                            <a href="/tierarten/saeugetiere.html">Säugetiere</a>
                          </li>
                          <li class="item-56">
                            <a href="/tierarten/voegel.html">Vögel</a>
                          </li>
                          <li class="item-75">
                            <a href="/tierarten/amphibien-und-reptilien.html"
                              >Amphibien / Reptilien</a
                            >
                          </li>
                          <li class="item-57">
                            <a href="/tierarten/fische.html">Fische</a>
                          </li>
                          <li class="item-58">
                            <a href="/tierarten/insekten-und-spinnen.html"
                              >Insekten / Spinnen</a
                            >
                          </li>
                          <li class="item-59">
                            <a href="/tierarten/wirbellose.html">Wirbellose</a>
                          </li>
                        </ul>
                      </li>
                      <li class="item-254 uk-active uk-parent">
                        <a href="/tiernamen.html">Tiernamen</a>
                        <ul class="uk-nav-sub">
                          <li class="item-262">
                            <a href="/tiernamen/tiernamen-tierart.html"
                              >Tiernamen nach Tierart</a
                            >
                          </li>
                          <li class="item-263">
                            <a href="/tiernamen/tiernamen-charakter.html"
                              >Tiernamen nach Charakter</a
                            >
                          </li>
                          <li class="item-264">
                            <a
                              href="/tiernamen/tiernamen-maennlich-weiblich.html"
                              >Tiernamen: Männlich / Weiblich</a
                            >
                          </li>
                          <li class="item-266">
                            <a href="/tiernamen/tiernamen-zeichentrick.html"
                              >Tiernamen: TV, Filme und Games</a
                            >
                          </li>
                          <li class="item-268">
                            <a href="/tiernamen/tiernamen-a-bis-z.html"
                              >Tiernamen von A bis Z</a
                            >
                          </li>
                          <li class="item-265 uk-active">
                            <a href="/tiernamen/tiernamen-goetter-koenige.html"
                              >Tiernamen: Götter / Könige</a
                            >
                          </li>
                          <li class="item-267">
                            <a href="/tiernamen/tiernamen-sprache.html"
                              >Tiernamen nach Sprache</a
                            >
                          </li>
                          <li class="item-573">
                            <a href="/tiernamen/tiernamen-farbe.html"
                              >Tiernamen nach Farbe</a
                            >
                          </li>
                          <li class="item-550">
                            <a
                              href="/tiernamen/tiernamen-nach-anzahl-der-tiere.html"
                              >Tiernamen nach Anzahl</a
                            >
                          </li>
                        </ul>
                      </li>
                      <li class="item-269 uk-parent">
                        <a href="/haustiere.html">Haustiere</a>
                        <ul class="uk-nav-sub">
                          <li class="item-270">
                            <a href="/haustiere/haustier-hund.html"
                              >Haustier Hund</a
                            >
                          </li>
                          <li class="item-271">
                            <a href="/haustiere/haustier-katze.html"
                              >Haustier Katze</a
                            >
                          </li>
                          <li class="item-591">
                            <a href="/haustiere/pferde.html">Pferde</a>
                          </li>
                          <li class="item-1000">
                            <a href="/haustiere/kaninchen.html">Kaninchen</a>
                          </li>
                          <li class="item-273">
                            <a href="/haustiere/haustier-vogel.html"
                              >Haustier Vogel</a
                            >
                          </li>
                          <li class="item-274">
                            <a href="/haustiere/haustier-meerschweinchen.html"
                              >Meerschweinchen</a
                            >
                          </li>
                          <li class="item-272">
                            <a href="/haustiere/haustier-hamster.html"
                              >Hamster</a
                            >
                          </li>
                          <li class="item-531">
                            <a href="/haustiere/exotische-haustiere.html"
                              >Exotische Haustiere</a
                            >
                          </li>
                        </ul>
                      </li>
                      <li class="item-206 uk-parent">
                        <a href="/funfacts.html">Tier-Rekorde</a>
                        <ul class="uk-nav-sub">
                          <li class="item-225">
                            <a href="/funfacts/tierische-rekorde.html"
                              >Tierische Rekorde</a
                            >
                          </li>
                        </ul>
                      </li>
                      <li class="item-218 uk-parent">
                        <a href="/specials.html">Tier-Wissen</a>
                        <ul class="uk-nav-sub">
                          <li class="item-233">
                            <a href="/specials/tierleben.html">Tier-Wissen</a>
                          </li>
                          <li class="item-235">
                            <a href="/specials/tierisch-komisch.html"
                              >Tierisch komisch</a
                            >
                          </li>
                          <li class="item-231">
                            <a href="/specials/tierbegriffe.html"
                              >Tierbegriffe</a
                            >
                          </li>
                          <li class="item-228">
                            <a href="/specials/das-stimmt-nicht.html"
                              >Wahr oder unwahr?</a
                            >
                          </li>
                          <li class="item-229">
                            <a href="/specials/lustige-tiernamen.html"
                              >Lustige Tiernamen</a
                            >
                          </li>
                          <li class="item-240">
                            <a href="/specials/wo-ist-der-unterschied.html"
                              >Wo ist der Unterschied?</a
                            >
                          </li>
                          <li class="item-227">
                            <a href="/specials/schon-gewusst.html"
                              >Schon gewusst?</a
                            >
                          </li>
                        </ul>
                      </li>
                      <li class="item-815 uk-parent">
                        <a href="/videos.html">Videos</a>
                        <ul class="uk-nav-sub">
                          <li class="item-204">
                            <a href="/videos/tierwissen-videos.html"
                              >Tierwissen-Videos</a
                            >
                          </li>
                          <li class="item-1111">
                            <a href="/videos/steckbrief-videos.html"
                              >Steckbrief-Videos</a
                            >
                          </li>
                          <li class="item-896">
                            <a href="/videos/coole-fakten-videos.html"
                              >Coole-Fakten-Videos</a
                            >
                          </li>
                          <li class="item-219">
                            <a href="/videos/fun-fact-videos.html"
                              >Fun-Fact-Videos</a
                            >
                          </li>
                          <li class="item-210">
                            <a href="/videos/haustier-videos.html"
                              >Haustier-Videos</a
                            >
                          </li>
                        </ul>
                      </li>
                      <li class="item-41 uk-parent">
                        <a href="/fotos.html">Fotos</a>
                        <ul class="uk-nav-sub">
                          <li class="item-260">
                            <a href="/fotos/foto-des-tages.html"
                              >Foto des Tages</a
                            >
                          </li>
                          <li class="item-205">
                            <a href="/fotos/tierbabys.html">Tierbabys</a>
                          </li>
                          <li class="item-731">
                            <a href="/fotos/farben.html">Farben</a>
                          </li>
                        </ul>
                      </li>
                      <li class="item-429 uk-parent">
                        <a href="/tier-spiele.html">Spiele</a>
                        <ul class="uk-nav-sub">
                          <li class="item-524">
                            <a href="/tier-spiele/test.html">Tests</a>
                          </li>
                          <li class="item-478">
                            <a href="/tier-spiele/quiz.html">Quiz</a>
                          </li>
                          <li class="item-432">
                            <a href="/tier-spiele/malvorlagen.html"
                              >Malvorlagen</a
                            >
                          </li>
                        </ul>
                      </li>
                      <li class="item-558"><a href="/news.html">News</a></li>
                    </ul>
                  </div>
                </div>
                <div>
                  <div class="uk-panel" id="module-1489">
                    <div class="uk-margin-remove-last-child custom">
                      <p>
                        <span style="font-size: 8pt">
                          Besuche unsere englische Seite!</span
                        ><a
                          href="https://www.animalfunfacts.net/"
                          target="_blank"
                          rel="nofollow noopener noreferrer"
                          ><img
                            src="/images/stories/flag_US_2.png"
                            alt="animalfunfacts.net - The Animal Encyclopedia for Kids"
                            width="28"
                            height="28"
                            style="margin-right: 10px; float: left"
                            title="animalfunfacts.net - The Animal Encyclopedia for Kids"
                        /></a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <header class="tm-header uk-visible@m">
        <div class="tm-headerbar-default tm-headerbar tm-headerbar-top">
          <div class="uk-container tm-page-width">
            <div
              class="uk-grid-medium uk-child-width-auto uk-flex-center uk-flex-middle uk-grid"
              uk-grid=""
            >
              <div class="uk-first-column">
                <div class="uk-panel" id="module-575">
                  <div class="uk-margin-remove-last-child custom">
                    <p>
                      <a href="/"
                        ><img
                          src="/images/stories/logo_tierchenwelt.png"
                          alt="tierchenwelt.de - Das Tierlexikon für Kinder"
                          width="147"
                          height="50"
                          style="
                            margin-right: 10px;
                            margin-bottom: 10px;
                            float: left;
                          "
                          title="tierchenwelt.de - Das Tierlexikon für Kinder"
                      /></a>
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div class="uk-panel" id="module-1800">
                  <div class="uk-margin-remove-last-child custom">
                    <script>
                      window.googletag = window.googletag || {
                        cmd: [],
                      };
                    </script>
                    <script
                      class="cmplazyload"
                      data-cmp-newid="5175916"
                      data-cmp-ab="1"
                      type="text/javascript"
                      async=""
                      data-cmp-done="1"
                      data-cmp-activated="1"
                    >
                      (function (w, d, s, l, i) {
                        w[l] = w[l] || [];
                        w[l].push({
                          "gtm.start": new Date().getTime(),
                          event: "gtm.js",
                        });
                        var f = d.getElementsByTagName(s)[0],
                          j = d.createElement(s),
                          dl = l != "dataLayer" ? "&l=" + l : "";
                        j.async = true;
                        j.src =
                          "https://www.googletagmanager.com/gtm.js?id=" +
                          i +
                          dl;
                        f.parentNode.insertBefore(j, f);
                      })(
                        window,
                        document,
                        "script",
                        "dataLayer",
                        "GTM-5T5K3Z2"
                      );
                    </script>
                    <script
                      type="text/plain"
                      data-cmp-vendor="s905"
                      data-cmp-done="1"
                      data-cmp-ab="1"
                      data-cmp-orgid="5175916"
                      class="cmporgscript"
                      data-cmp-activated="1"
                    >
                      	(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                      })(window,document,'script','dataLayer','GTM-5T5K3Z2');
                    </script>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="uk-navbar-container">
          <div class="uk-container tm-page-width">
            <nav
              class="uk-navbar"
              uk-navbar='{"align":"left","container":".tm-header","boundary":".tm-header .uk-navbar-container"}'
            >
              <div class="uk-navbar-center">
                <ul class="nav-pills uk-navbar-nav" id="module-1">
                  <li class="item-54">
                    <a href="/tierarten.html">Tierarten</a>
                  </li>
                  <li class="item-254 uk-active">
                    <a href="/tiernamen.html">Tiernamen</a>
                  </li>
                  <li class="item-269">
                    <a href="/haustiere.html">Haustiere</a>
                  </li>
                  <li class="item-206">
                    <a href="/funfacts.html">Tier-Rekorde</a>
                  </li>
                  <li class="item-218">
                    <a href="/specials.html">Tier-Wissen</a>
                  </li>
                  <li class="item-815"><a href="/videos.html">Videos</a></li>
                  <li class="item-41"><a href="/fotos.html">Fotos</a></li>
                  <li class="item-429">
                    <a href="/tier-spiele.html">Spiele</a>
                  </li>
                  <li class="item-558"><a href="/news.html">News</a></li>
                </ul>
              </div>
            </nav>
          </div>
        </div>

        <div class="tm-headerbar-default tm-headerbar tm-headerbar-bottom">
          <div class="uk-container tm-page-width">
            <div class="uk-flex uk-flex-center">
              <div>
                <div class="uk-panel uk-visible@l" id="module-867">
                  <div class="uk-margin-remove-last-child custom">
                    <ul class="subnavicons2">
                      <li>HIGHLIGHTS:</li>
                      <li>
                        <a href="/tiernamen/tiernamen-tierart.html"
                          >Nach Tierart</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-goetter-koenige.html"
                          >Götter &amp; Könige</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-charakter.html"
                          >Charakter</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe.html">Nach Farbe</a>
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-sprache.html"
                          >Nach Sprache</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-zeichentrick.html"
                          >Film &amp; TV</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-maennlich-weiblich.html"
                          >Geschlecht</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-nach-anzahl-der-tiere.html"
                          >Nach Anzahl</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-a-bis-z.html"
                          >Von A bis Z</a
                        >
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div class="tm-page uk-margin-auto">
        <div
          class="tm-top uk-section-muted uk-section uk-padding-remove-top uk-padding-remove-bottom"
        >
          <div class="uk-container">
            <div class="uk-panel uk-hidden@l" id="module-1335">
              <div class="uk-margin-remove-last-child custom">
                <ul
                  class="lexi-mobile2"
                  style="text-align: center; padding: 10px 0px 0px 0px"
                >
                  <li>Zum <a href="/tiernamen.html">Tiernamen-Lexikon</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <main
          id="tm-main"
          class="tm-main uk-section uk-section-default uk-section-xsmall"
          uk-height-viewport="expand: true"
          style="min-height: max(0px, -4603.47px + 100vh)"
        >
          <div class="uk-container">
            <div class="uk-grid uk-grid-small uk-grid-divider" uk-grid="">
              <div class="uk-width-expand@l uk-first-column">
                <nav
                  class="uk-margin-medium-bottom"
                  aria-label="Navigationspfad"
                >
                  <ul class="uk-breadcrumb">
                    <li>
                      <a href="/"><span>Startseite</span></a>
                    </li>
                    <li>
                      <a href="/tiernamen.html"><span>Tiernamen</span></a>
                    </li>
                  </ul>
                </nav>

                <div id="system-message-container" aria-live="polite"></div>

                <div class="uk-panel uk-margin-medium-bottom">
                  <div class="uk-margin">
                    <h1
                      class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      property="headline"
                    >
                      Tiernamen nach Göttern und Königen
                    </h1>
                    <h2>
                      Lass dich bei der Namenssuche von Göttern und Königen
                      inspirieren!
                    </h2>
                    <p>
                      Ihre Namen tragen oft eine majestätische oder mystische
                      Bedeutung, die das Wesen deines Haustiers unterstreichen
                      kann. Zum Beispiel könnte ein stolzer Kater den Namen
                      „Zeus“ tragen, benannt nach dem mächtigen griechischen
                      Gott des Himmels und des Donners. Solche Namen verleihen
                      dem Haustier nicht nur Charakter, sondern können auch eine
                      Verbindung zu alten Mythen und Legenden schaffen. Eine
                      hübsche Hündin oder eine elegante Katze könnte den Namen
                      „Cleopatra“ tragen, inspiriert von der berühmten
                      ägyptischen Königin, die für ihre Schönheit und
                      Intelligenz bekannt war.
                    </p>
                    <hr />
                  </div>
                </div>

                <div
                  id="qmn5001"
                  data-height="250"
                  data-width="800"
                  data-responsive="desktop"
                  style="
                    display: flex;
                    height: 225px;
                    text-align: center;
                    margin-top: 20px;
                    margin-bottom: 20px;
                    width: 100%;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                  "
                  class="billboard-adspiritflash3116143"
                >
                  <div
                    class="qmn-ad-label"
                    data-nosnippet="true"
                    style="
                      font-size: 0.7rem;
                      color: rgb(51, 51, 51);
                      text-align: center;
                      padding: 5px 5px 2px;
                      text-transform: uppercase;
                      font-weight: bold;
                    "
                  >
                    Werbung
                  </div>
                  <ins
                    class="asm_async_creative"
                    data-asm-cdn="cdn.f11-ads.com"
                    data-asm-host="ads.qualitymedianetwork.de"
                    data-asm-fetch-gdpr="1"
                    data-asm-params="pid=5001"
                    style="
                      display: inline-block;
                      width: 720px;
                      height: 250px;
                      text-align: left;
                      text-decoration: none;
                    "
                    id="p49574x0"
                    data-asm-done="1"
                    data-asm-pushed="1"
                    ><script src="https://ads.qualitymedianetwork.de/adscript.php?async=p49574x0&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=648&amp;vis=4&amp;tz=1751436283987&amp;pid=5001&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
                    <style>
                      .billboard-adspiritflash3116143 {
                        margin: 20px 0;
                      }
                      .billboard-adspiritflash3116143 ins {
                        background-color: rgba(
                          0,
                          0,
                          0,
                          0
                        ) !important; /* Transparent background */
                      }

                      .billboard-adspiritflash3116143 ins:has(.asmbannerimg),
                      .billboard-adspiritflash3116143
                        ins:has(.asm_async_creative) {
                        height: auto !important;
                      }

                      /*.billboard-adspiritflash3116143 iframe[id^="adspiritflash"],*/
                      /*.billboard-adspiritflash3116143 img[id^="asmimg"] {*/
                      /*    border-radius: 15px;*/
                      /*}*/
                    </style>
                    <style id="intextad-style-adspiritflash3116143"></style>

                    <span
                      class=""
                      id="billboard-anchor-adspiritflash3116143"
                      style=""
                    >
                    </span>
                    <div
                      class="asmbeacon asmb3"
                      id=""
                      style="position: absolute; left: 0px; top: 0px"
                    >
                      <img
                        class="asmbeacon"
                        border="0"
                        style="
                          border: 0px white solid !important;
                          width: 1px;
                          height: 1px;
                        "
                        width="1"
                        height="1"
                        alt=""
                        src="https://ads.qualitymedianetwork.de/adview.php?tz=175143628374831745001tzmacro&amp;&amp;pid=5001&amp;kid=1193&amp;wmid=4940&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=1&amp;nvc=1&amp;target1=-"
                      />
                    </div>
                    <ins
                      class="asm_async_creative"
                      data-asm-cdn="cdn-de.f11-ads.com"
                      data-asm-host="de.f11-ads.com"
                      data-asm-params="pid=7953&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"
                      data-asm-click="https://ads.qualitymedianetwork.de/adclick.php?tz=1751436283500149405185101&amp;pid=5001&amp;kid=1193&amp;wmid=4940&amp;wsid=703&amp;sid=1&amp;ord=1751436283&amp;vlx=cf7&amp;target="
                      data-asm-encode="1"
                      id="p20069x1"
                      style="
                        display: inline-block;
                        width: 800px;
                        height: 250px;
                        text-align: left;
                        text-decoration: none;
                      "
                      data-asm-done="1"
                      data-asm-pushed="1"
                      ><script src="https://de.f11-ads.com/adscript.php?async=p20069x1&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=800&amp;vis=4&amp;tz=1751436284376&amp;prdclick_1=https%3A%2F%2Fads.qualitymedianetwork.de%2Fadclick.php%3Ftz%3D1751436283500149405185101%26pid%3D5001%26kid%3D1193%26wmid%3D4940%26wsid%3D703%26sid%3D1%26ord%3D1751436283%26vlx%3Dcf7%26target%3D&amp;pid=7953&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
                      <div class="" id="adv279274" style="display: none">
                        <img
                          src="https://de.f11-ads.com/adview.php?tz=175143628310014277953tzmacro&amp;&amp;pid=7953&amp;kid=81606&amp;wmid=279274&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=403&amp;target=&amp;sid2=DE01130"
                        />
                      </div>
                      <iframe
                        id="adspiritflash1665364"
                        width="800"
                        height="250"
                        noresize="noresize"
                        scrolling="no"
                        frameborder="0"
                        marginheight="0"
                        marginwidth="0"
                        src="https://www.ad-production-stage.com/11-1952/sa24tw5z1x/20250630T091635-99317688/index.html?clicktag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143628379532792746928734%26pid%3D7953%26kid%3D81606%26wmid%3D279274%26wsid%3D2099%26sid%3D403%26ord%3D1751436283%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D1751436283500149405185101%2526pid%253D5001%2526kid%253D1193%2526wmid%253D4940%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436283%2526vlx%253Dcf7%2526target%253D%26nenco%3D1%26vlx%3Dcf7%26target%3D&amp;clickTag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143628379532792746928734%26pid%3D7953%26kid%3D81606%26wmid%3D279274%26wsid%3D2099%26sid%3D403%26ord%3D1751436283%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D1751436283500149405185101%2526pid%253D5001%2526kid%253D1193%2526wmid%253D4940%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436283%2526vlx%253Dcf7%2526target%253D%26nenco%3D1%26vlx%3Dcf7%26target%3D&amp;collapse=asmflash1665364_collapse&amp;expand=asmflash1665364_expand&amp;mid=3707&amp;lat=52.5196&amp;lon=13.4069&amp;city=Berlin&amp;zip=10178&amp;kampagnen-id=81606&amp;flaechen-id=7953&amp;werbemittel-id=279274&amp;dppid=7953&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;refdomain=tierchenwelt.de&amp;svr=de.f11-ads.com&amp;viewtag=&amp;haendlerid=DE01130"
                        allowtransparency="true"
                        allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                        style="
                          background-color: rgb(237, 237, 237);
                          transform: scale(0.9);
                          transform-origin: 0px 0px;
                        "
                      ></iframe>
                      <script
                        type="text/javascript"
                        src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
                      ></script
                    ></ins>
                    <script
                      type="text/javascript"
                      src="https://cdn-de.f11-ads.com/adasync.min.js"
                    ></script>
                    <script
                      type="text/javascript"
                      src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
                    ></script
                  ></ins>
                </div>
                <div
                  uk-grid=""
                  class="uk-child-width-1-1 uk-grid-column-small uk-grid-row-large uk-grid uk-grid-stack"
                >
                  <div class="uk-first-column">
                    <article
                      id="article-1012"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/1012-tiernamen-griechische-goetter.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach griechischen Göttern"
                      />
                      <meta property="author" typeof="Person" content="Silke" />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:23:24+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2009-12-22T23:00:00+01:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1012-tiernamen-griechische-goetter.html"
                          class="uk-link-reset"
                          >Tiernamen nach griechischen Göttern</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/poseidon_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1012-tiernamen-griechische-goetter.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/7a/poseidon_l-7a028cc8.webp 720w
                              "
                              sizes="(min-width: 720px) 720px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/67/poseidon_l-67ee2fd3.jpeg"
                              width="720"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                  <div class="uk-grid-margin uk-first-column">
                    <article
                      id="article-1011"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/1011-tiernamen-nach-aegyptischen-goettern.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach ägyptischen Göttern"
                      />
                      <meta property="author" typeof="Person" content="Silke" />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:20:27+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2009-12-23T23:00:00+01:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1011-tiernamen-nach-aegyptischen-goettern.html"
                          class="uk-link-reset"
                          >Tiernamen nach ägyptischen Göttern</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/bastet_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1011-tiernamen-nach-aegyptischen-goettern.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/42/bastet_l-42c40d17.webp 720w
                              "
                              sizes="(min-width: 720px) 720px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/61/bastet_l-6120dd27.jpeg"
                              width="720"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                  <div class="uk-grid-margin uk-first-column">
                    <article
                      id="article-1013"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/1013-tiernamen-roemische-goetter.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach römischen Göttern"
                      />
                      <meta property="author" typeof="Person" content="Silke" />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:25:40+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2009-12-21T23:00:00+01:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1013-tiernamen-roemische-goetter.html"
                          class="uk-link-reset"
                          >Tiernamen nach römischen Göttern</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/apollo_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1013-tiernamen-roemische-goetter.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/3b/apollo_l-3b66e56c.webp 720w
                              "
                              sizes="(min-width: 720px) 720px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/3a/apollo_l-3ae6728f.jpeg"
                              width="720"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                  <div class="uk-grid-margin uk-first-column">
                    <article
                      id="article-2829"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/2829-tiernamen-nach-goettern-der-maya.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach Maya-Göttern"
                      />
                      <meta property="author" typeof="Person" content="Silke" />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:25:05+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2013-10-14T19:24:32+02:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2829-tiernamen-nach-goettern-der-maya.html"
                          class="uk-link-reset"
                          >Tiernamen nach Maya-Göttern</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/chichen_itza_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2829-tiernamen-nach-goettern-der-maya.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/8b/chichen_itza_l-8b87a456.webp 749w,
                                /templates/yootheme/cache/53/chichen_itza_l-53584812.webp 750w
                              "
                              sizes="(min-width: 750px) 750px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/b1/chichen_itza_l-b1b86821.jpeg"
                              width="750"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                  <div class="uk-grid-margin uk-first-column">
                    <article
                      id="article-787"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/787-tiernamen-aegyptische-koenige.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach ägyptischen Königen"
                      />
                      <meta
                        property="author"
                        typeof="Person"
                        content="Silke Menne"
                      />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:21:06+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2009-07-31T18:38:42+02:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/787-tiernamen-aegyptische-koenige.html"
                          class="uk-link-reset"
                          >Tiernamen nach ägyptischen Königen</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/tutanchamun_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/787-tiernamen-aegyptische-koenige.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/06/tutanchamun_l-06203d6e.webp 720w
                              "
                              sizes="(min-width: 720px) 720px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/30/tutanchamun_l-30091fea.jpeg"
                              width="720"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                  <div class="uk-grid-margin uk-first-column">
                    <article
                      id="article-2820"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/2820-tiernamen-nach-azteken-goettern.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach Azteken-Göttern"
                      />
                      <meta property="author" typeof="Person" content="Silke" />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:21:52+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2013-10-12T15:01:39+02:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2820-tiernamen-nach-azteken-goettern.html"
                          class="uk-link-reset"
                          >Tiernamen nach Azteken-Göttern</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/aztekischer_kalender_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2820-tiernamen-nach-azteken-goettern.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/54/aztekischer_kalender_l-5468314f.webp 720w
                              "
                              sizes="(min-width: 720px) 720px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/71/aztekischer_kalender_l-71f838e6.jpeg"
                              width="720"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                  <div class="uk-grid-margin uk-first-column">
                    <article
                      id="article-2821"
                      class="uk-article"
                      data-permalink="https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige/2821-tiernamen-nach-hinduistischen-goettern.html"
                      typeof="Article"
                      vocab="https://schema.org/"
                    >
                      <meta
                        property="name"
                        content="Tiernamen nach hinduistischen Göttern"
                      />
                      <meta property="author" typeof="Person" content="Silke" />
                      <meta
                        property="dateModified"
                        content="2024-11-14T14:24:19+01:00"
                      />
                      <meta
                        property="datePublished"
                        content="2013-10-12T15:15:54+02:00"
                      />
                      <meta
                        class="uk-margin-remove-adjacent"
                        property="articleSection"
                        content="Tiernamen nach Göttern und Königen"
                      />

                      <h2
                        property="headline"
                        class="uk-margin-large-top uk-margin-remove-bottom uk-article-title"
                      >
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2821-tiernamen-nach-hinduistischen-goettern.html"
                          class="uk-link-reset"
                          >Tiernamen nach hinduistischen Göttern</a
                        >
                      </h2>

                      <div
                        class="uk-text-center uk-margin-medium-top"
                        property="image"
                        typeof="ImageObject"
                      >
                        <meta
                          property="url"
                          content="https://tierchenwelt.de/images/stories/illustrationen/tiernamen/goetter_koenige/hindu_tempel_l.jpg"
                        />
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2821-tiernamen-nach-hinduistischen-goettern.html"
                          ><picture>
                            <source
                              type="image/webp"
                              srcset="
                                /templates/yootheme/cache/c5/hindu_tempel_l-c5bccd85.webp 720w
                              "
                              sizes="(min-width: 720px) 720px"
                            />
                            <img
                              loading="lazy"
                              alt=""
                              class="none"
                              src="/templates/yootheme/cache/37/hindu_tempel_l-3751cf8a.jpeg"
                              width="720"
                              height="480"
                            /> </picture
                        ></a>
                      </div>
                    </article>
                  </div>
                </div>
              </div>

              <aside id="tm-sidebar" class="tm-sidebar uk-width-1-4@l">
                <div
                  class="uk-grid uk-child-width-1-1 uk-visible@l uk-grid-stack"
                  uk-grid=""
                >
                  <div class="uk-visible@l uk-first-column">
                    <div class="uk-panel uk-visible@l" id="module-1789">
                      <div class="uk-margin-remove-last-child custom">
                        <p style="text-align: center" data-nosnippet="">
                          <a href="/schule/material-tierarten.html"
                            ><img
                              src="/images/stories/schule/unterrichtsmaterial_banner.png"
                              alt=""
                              width="270"
                              height="131"
                              loading="lazy"
                              data-alt="tierchenwelt.de - Unterrichtsmaterialien"
                          /></a>
                        </p>
                        <hr />
                        <p style="text-align: center" data-nosnippet="">
                          <a href="/schul-lizenz.html"
                            ><img
                              src="/images/stories/schul_lizenz.png"
                              alt=""
                              width="270"
                              height="159"
                              loading="lazy"
                              data-alt="tierchenwelt.de - Schul-Lizenz"
                          /></a>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="uk-visible@l uk-grid-margin uk-first-column">
                    <div
                      class="uk-panel uk-hidden@schule uk-visible@l"
                      id="module-1809"
                    >
                      <div class="uk-margin-remove-last-child custom">
                        <hr />
                        <p style="text-align: center" data-nosnippet="">
                          <a
                            href="/produkte/4115-hunderassen-das-grosse-buch-fuer-kinder.html"
                            ><img
                              src="/images/stories/produkte/tierchenwelt_hunderassen_banner_sidebar.jpg"
                              alt="Hunderassen - Das große Buch für Kinder"
                              width="300"
                              height="140"
                              loading="lazy"
                          /></a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </aside>
            </div>
          </div>
        </main>

        <div class="tm-bottom uk-section-default uk-section uk-section-small">
          <div
            id="watchbetter-player"
            style="
              height: 500px;
              text-align: center;
              margin-top: 10px;
              margin-bottom: 10px;
            "
          >
            <div
              class="qmn-ad-label"
              data-nosnippet="true"
              style="
                font-size: 0.7rem;
                color: rgb(51, 51, 51);
                text-align: center;
                padding: 5px 5px 2px;
                text-transform: uppercase;
                font-weight: bold;
              "
            >
              Werbung
            </div>
            <div
              id="watchbetter-embed"
              data-playlistid="Tierchenwelt"
              data-autoplay="true"
              data-player-type="embedPartner"
              style="height: 500px"
            >
              <iframe
                loading="lazy"
                width="100%"
                height="493"
                src="https://watchbetter.com/embedPartner/Tierchenwelt?autoplay=1#cmpnoscreen"
                title="watchbetter player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen=""
                style="max-width: 700px"
              ></iframe>
            </div>
            <script
              src="https://watchbetter.com/embedPartner.js"
              async=""
              defer=""
            ></script>
          </div>
          <div class="uk-container">
            <div class="uk-grid uk-child-width-expand@m" uk-grid="">
              <div class="uk-width-1-1@m uk-first-column">
                <div class="uk-panel" id="module-1727">
                  <div class="uk-margin-remove-last-child custom">
                    <hr class="verwhr" />
                    <h3>Alle Tiernamen im Überblick</h3>
                    <p class="twh"><strong>Nach Geschlecht:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li style="display: block">
                        <a
                          href="/tiernamen/tiernamen-maennlich-weiblich/1076-tiernamen-weiblich.html"
                          >Weibliche Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-maennlich-weiblich/1071-tiernamen-maennlich.html"
                          >Männliche Tiernamen</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Katzen:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/994-katzennamen.html"
                          >Die 100 beliebtesten Katzennamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3704-schoene-weibliche-katzennamen.html"
                          >Namen für Kätzinnen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3703-coole-maennliche-katzennamen.html"
                          >Namen für Kater</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3712-beste-namen-rote-katzen.html"
                          >Namen für rote Katzen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3713-beste-namen-grau-blau-schwarze-katzen.html"
                          >Namen für schwarze Katzen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/2963-katzennamen-a.html"
                          >Katzennamen von A bis Z</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Hunde:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/993-hundenamen.html"
                          >Die 100 schönsten Hundenamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3782-weibliche-hundenamen.html"
                          >Namen für Hündinnen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3781-maennliche-hundenamen.html"
                          >Namen für Rüden</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3806-hundenamen-braun.html"
                          >Namen für braune Hunde</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3807-hundenamen-schwarz.html"
                          >Namen für schwarze Hunde</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Nager und Kleintiere:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/1020-tiernamen-nager.html"
                          >Top 100 Namen für Nager</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3792-kaninchen-namen.html"
                          >Namen für Kaninchen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3791-meerschweinchen-namen.html"
                          >Namen für Meerschweinchen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/3790-hamster-namen.html"
                          >Namen für Hamster</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/1057-tiernamen-vogel.html"
                          >Namen für Vögel</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-tierart/2830-namen-fuer-reptilien.html"
                          >Namen für Reptilien</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Nach Sprache:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-sprache/1014-englische-tiernamen.html"
                          >Englische Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-sprache/1923-italienische-tiernamen.html"
                          >Italienische Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-sprache/1924-spanische-tiernamen.html"
                          >Spanische Tiernamen</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Aus Film, TV und Games:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-zeichentrick/790-tiernamen-film-trickfiguren.html"
                          >Namen aus Filmen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-zeichentrick/986-tiernamen-zeichentrickfiguren.html"
                          >Namen aus Zeichentrick</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-zeichentrick/2832-tiernamen-aus-videospielen.html"
                          >Namen aus Videospielen</a
                        >
                      </li>
                    </ul>
                    <p class="twh">
                      <strong>Nach Charakter und Wesen:</strong>
                    </p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-charakter/785-suesse-tiernamen.html"
                          >Süße Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-charakter/789-lustige-tiernamen.html"
                          >Lustige Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-charakter/788-exotische-tiernamen.html"
                          >Exotische Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-charakter/786-zauberhafte-tiernamen.html"
                          >Zauberhafte Tiernamen</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-charakter/1019-tiernamen-schlau.html"
                          >Schlaue Tiernamen</a
                        >
                      </li>
                    </ul>
                    <p class="twh">
                      <strong>Nach Göttern und Königen:</strong>
                    </p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1011-tiernamen-nach-aegyptischen-goettern.html"
                          >Ägyptische Götter</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/787-tiernamen-aegyptische-koenige.html"
                          >Ägyptische Könige</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2820-tiernamen-nach-azteken-goettern.html"
                          >Azteken-Götter</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1012-tiernamen-griechische-goetter.html"
                          >Griechische Götter</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2821-tiernamen-nach-hinduistischen-goettern.html"
                          >Hindu-Götter</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/2829-tiernamen-nach-goettern-der-maya.html"
                          >Maya-Götter</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-goetter-koenige/1013-tiernamen-roemische-goetter.html"
                          >Römische Götter</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Nach Farbe:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3042-blau.html"
                          >Blau</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3046-braun.html"
                          >Braun</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3044-gelb.html"
                          >Gelb</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3043-gruen.html"
                          >Grün</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3045-orange.html"
                          >Orange</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3048-rot.html"
                          >Rot</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3049-schwarz.html"
                          >Schwarz</a
                        >
                      </li>
                      <li>
                        <a href="/tiernamen/tiernamen-farbe/3047-weiss.html"
                          >Weiß</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Nach Anzahl:</strong></p>
                    <ul class="mdlistsb uk-column-1-2@m uk-column-1-1@s">
                      <li style="display: block">
                        <a
                          href="/tiernamen/tiernamen-nach-anzahl-der-tiere/2831-tiernamen-fuer-duos.html"
                          >Tiernamen für Duos</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-nach-anzahl-der-tiere/4097-tiernamen-fuer-trios.html"
                          >Tiernamen für Trios</a
                        >
                      </li>
                    </ul>
                    <p class="twh"><strong>Tiernamen von A bis Z:</strong></p>
                    <ul class="mdlistsb uk-column-1-5@m uk-column-1-3">
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1579-tiernamen-a.html"
                          >A</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1580-tiernamen-b.html"
                          >B</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1581-tiernamen-c.html"
                          >C</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1582-tiernamen-d.html"
                          >D</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1583-tiernamen-e.html"
                          >E</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1584-tiernamen-f.html"
                          >F</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1585-tiernamen-g.html"
                          >G</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1586-tiernamen-h.html"
                          >H</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1587-tiernamen-i-j.html"
                          >I-J</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1588-tiernamen-k.html"
                          >K</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1596-tiernamen-l.html"
                          >L</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1597-tiernamen-m.html"
                          >M</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1598-tiernamen-n.html"
                          >N</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1599-tiernamen-o.html"
                          >O</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1600-tiernamen-p.html"
                          >P</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1601-tiernamen-q-r.html"
                          >Q-R</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1602-tiernamen-s.html"
                          >S</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1603-tiernamen-t.html"
                          >T</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1604-tiernamen-u-v-w.html"
                          >UVW</a
                        >
                      </li>
                      <li>
                        <a
                          href="/tiernamen/tiernamen-a-bis-z/1605-tiernamen-x-y-z.html"
                          >YXZ</a
                        >
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="uk-grid-margin uk-first-column">
                <div class="uk-panel uk-hidden@l" id="module-1785">
                  <div class="uk-margin-remove-last-child custom">
                    <p style="text-align: center" data-nosnippet="">
                      <a href="/schule/material-tierarten.html"
                        ><img
                          src="/images/stories/schule/unterrichtsmaterial_banner.png"
                          alt=""
                          width="270"
                          height="131"
                          loading="lazy"
                          data-alt="tierchenwelt.de - Unterrichtsmaterialien"
                      /></a>
                    </p>
                    <hr />
                    <p style="text-align: center">
                      <a href="/schul-lizenz.html"
                        ><img
                          src="/images/stories/schul_lizenz.png"
                          alt=""
                          width="270"
                          height="131"
                          loading="lazy"
                          data-alt="tierchenwelt.de - Schullizenz"
                      /></a>
                    </p>
                  </div>
                </div>
              </div>
              <div class="uk-grid-margin">
                <div
                  class="uk-panel uk-hidden@l uk-hidden@schule"
                  id="module-1787"
                >
                  <div class="uk-margin-remove-last-child custom">
                    <hr />
                    <p style="text-align: center" data-nosnippet="">
                      <a
                        href="/produkte/4115-hunderassen-das-grosse-buch-fuer-kinder.html"
                        ><img
                          src="/images/stories/produkte/tierchenwelt_hunderassen_banner_sidebar.jpg"
                          alt="Hunderassen - Das große Buch für Kinder"
                          width="280"
                          height="131"
                          loading="lazy"
                      /></a>
                    </p>
                  </div>
                </div>
              </div>
              <div class="uk-width-1-1@m uk-grid-margin uk-first-column">
                <div class="uk-panel uk-hidden@category" id="module-1817">
                  <div class="uk-margin-remove-last-child custom"><hr /></div>
                </div>
              </div>
              <div class="uk-width-1-1@m uk-grid-margin uk-first-column">
                <div
                  id="qmn5002"
                  data-height="600"
                  data-width="300"
                  data-responsive="desktop"
                  class="prospektad-adspiritflash1951503"
                  style="
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    margin: 40px 0px;
                    height: 800px;
                    max-height: 800px;
                    z-index: 0;
                  "
                >
                  <div
                    class="qmn-ad-label"
                    data-nosnippet="true"
                    style="
                      font-size: 0.7rem;
                      color: rgb(51, 51, 51);
                      text-align: center;
                      padding: 5px 5px 2px;
                      text-transform: uppercase;
                      font-weight: bold;
                    "
                  >
                    Werbung
                  </div>
                  <ins
                    class="asm_async_creative ins-adspiritflash1951503"
                    data-asm-cdn="cdn.f11-ads.com"
                    data-asm-host="ads.qualitymedianetwork.de"
                    data-asm-fetch-gdpr="1"
                    data-asm-params="pid=5002"
                    id="p15466x2"
                    data-asm-done="1"
                    data-asm-pushed="1"
                    style="
                      display: inline-block;
                      width: 550px;
                      height: 800px;
                      text-align: left;
                      text-decoration: none;
                      overflow: hidden;
                    "
                    scrolling="no"
                    ><script src="https://ads.qualitymedianetwork.de/adscript.php?async=p15466x2&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=300&amp;vis=0&amp;tz=1751436295921&amp;pid=5002&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
                    <style id="prospektad-styles-adspiritflash1951503">
                      :root {
                        --index-adspiritflash1951503: 5;
                      }

                      .prospektad-adspiritflash1951503-wrapper {
                        width: 100%;
                        height: 100%;
                        overflow: hidden;
                        margin: 0 auto;
                      }

                      .prospektad-creatives-adspiritflash1951503 {
                        display: flex;
                        width: calc(550px * var(--index-adspiritflash1951503));
                        /* transition: transform 0.4s ease-in; */
                        transform: translateX(0);
                      }

                      .prospektad-creative-adspiritflash1951503 {
                        width: 100%;
                        height: 100%;
                      }

                      .prospektad-adspiritflash1951503 {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                      }

                      .prospektad-adspiritflash1951503-nav {
                        display: flex;
                        box-sizing: border-box;
                        width: 100%;
                        padding: 2px 5px;
                        align-items: flex-start;
                        gap: 10px;
                        align-items: center;
                      }

                      .prospektad-adspiritflash1951503-nav-item {
                        height: 5px;
                        flex: 1 0 0;
                        border-radius: 5px;
                        background: #c7c7c7;
                      }

                      .prospektad-adspiritflash1951503-nav-item-active {
                        background: #888;
                      }

                      .prospektad-adspiritflash1951503-arrow-button,
                      .prospektad-adspiritflash1951503-arrow-button:active,
                      .prospektad-adspiritflash1951503-arrow-button:focus {
                        width: 30px;
                        height: 300px;
                        background: #323030;
                        border: none;
                        position: absolute;
                        color: #fff;
                        margin: 300px 0 0 0;
                        padding: initial;
                        fill: #fff;
                      }

                      .prospektad-adspiritflash1951503-arrow-button:hover {
                        background: #fff;
                        color: #000;
                        border: 1px solid #000;
                        fill: #000;
                      }

                      .prospektad-adspiritflash1951503-container {
                        /*background-color: #ededed;*/
                        position: relative;
                        padding-bottom: 1px;
                        height: 100%;
                        display: grid;
                      }

                      /* verhindert animation flackern */
                      .prospektad-adspiritflash1951503-container::before {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        /*border: 2px solid #ededed;*/
                        pointer-events: none;
                        z-index: 1;
                      }

                      button.prospektad-adspiritflash1951503-btn {
                        background-color: #c1c1c1;
                        color: rgb(37, 37, 37);
                        padding: 0px 8px;
                        margin: 5px auto;
                        border-radius: 8px;
                        outline: none;
                        border: none;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        min-height: 30px;
                        font-size: 14px;
                        cursor: pointer;

                        transition: background-color 2s ease;
                      }

                      .prospektad-adspiritflash1951503-btn.switchColor {
                        background-color: #3b3b3b;
                        color: white;
                      }

                      .prospektad-adspiritflash1951503-image-container {
                        display: flex;
                        width: 180px;
                        height: 152px;
                        transform-origin: bottom right;
                        position: absolute;
                        bottom: 0px;
                        right: 0;
                        overflow: hidden;
                        cursor: pointer;
                      }

                      .prospektad-adspiritflash1951503-image-mask {
                        width: 100%;
                        height: 100%;
                        /* background-image: url(bg.png); */
                        background-image: url("https://cdn-de.f11-ads.com/banner/qualitymedianetwork/603/2024-10-31/0_2024-10-31_hpa-peel_zip/bg.png");
                        background-size: 180px 152px;
                        background-repeat: no-repeat;
                        background-position: bottom right;
                        /* mask-image: url(mask.png); */
                        mask-image: url("https://cdn-de.f11-ads.com/banner/qualitymedianetwork/603/2024-10-31/0_2024-10-31_hpa-peel_zip/mask.png");
                        mask-size: 58px 49px;
                        mask-repeat: no-repeat;
                        mask-position: bottom right;
                      }

                      .prospektad-adspiritflash1951503-image-container.visible
                        .prospektad-adspiritflash1951503-image-mask {
                        mask-size: calc(58px * var(--scaleBase))
                          calc(49px * var(--scaleBase));
                      }

                      .prospektad-adspiritflash1951503-container
                        .prospektad-adspiritflash1951503-image-container
                        img.prospektad-adspiritflash1951503-scalable-image {
                        display: flex;
                        width: 58px;
                        max-width: 200px;
                        transform-origin: bottom right;
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        transform: scale(1) translateY(2px);
                        margin: initial;
                        border-width: initial;
                        box-shadow: initial;
                        padding: initial;
                        border-color: initial;
                      }

                      .prospektad-adspiritflash1951503-container
                        .prospektad-adspiritflash1951503-image-container.visible
                        img.prospektad-adspiritflash1951503-scalable-image {
                        opacity: 1;
                        transform: scale(var(--scaleBase)) translateY(0);
                      }

                      .prospektad-adspiritflash1951503-scalable-image.visible {
                        transform: scale(1) translateY(0);
                      }

                      #prospektad-adspiritflash1951503-counter {
                        color: #2a2a2a;
                        font-size: 10px;
                        letter-spacing: 1px;
                      }

                      .prospektad-adspiritflash1951503-w {
                        color: #2a2a2a;
                        font-size: 12px;
                        line-height: 12px;
                      }

                      .prospektad-adspiritflash1951503-image-container.animate
                        .prospektad-adspiritflash1951503-image-mask {
                        will-change: mask-size;
                        transform: translateZ(0);
                        animation: scaleadspiritflash1951503UpMask 3s infinite
                          ease-in-out;
                      }

                      .prospektad-adspiritflash1951503-image-container.animate {
                        animation: scaleadspiritflash1951503Container 3s
                          infinite ease-in-out;
                      }

                      @keyframes scaleadspiritflash1951503UpMask {
                        0%,
                        100% {
                          mask-size: calc(58px * var(--scaleBase))
                            calc(49px * var(--scaleBase));
                        }

                        50% {
                          mask-size: calc(58px * var(--scaleBase) * 0.7)
                            calc(49px * var(--scaleBase) * 0.7);
                        }
                      }

                      @keyframes scaleadspiritflash1951503Container {
                        0%,
                        100% {
                          width: calc(58px * var(--scaleBase));
                          height: calc(49px * var(--scaleBase));
                        }

                        50% {
                          width: calc(58px * var(--scaleBase) * 0.7);
                          height: calc(49px * var(--scaleBase) * 0.7);
                        }
                      }

                      .prospektad-adspiritflash1951503-container
                        .prospektad-adspiritflash1951503-image-container.animate
                        img.prospektad-adspiritflash1951503-scalable-image {
                        animation: scaleadspiritflash1951503UpDown 3s infinite
                          ease-in-out;
                      }

                      @keyframes scaleadspiritflash1951503UpDown {
                        0%,
                        100% {
                          transform: scale(var(--scaleBase));
                        }

                        50% {
                          transform: scale(calc(var(--scaleBase) * 0.7));
                        }
                      }
                    </style>
                    <style id="prospektad-style-adspiritflash1951503">
                      .ins-adspiritflash1951503 iframe {
                        max-width: 550px !important;
                        max-height: 800px !important;
                        height: 800px !important;
                        width: 550px !important;
                      }
                      .ins-adspiritflash1951503 .asmdivouter {
                        z-index: 0 !important;
                      }
                      .ins-adspiritflash1951503 .asmscroller {
                        position: absolute !important;
                        top: 0 !important;
                        left: 0 !important;
                      }
                      .prospektad-adspiritflash1951503-wrapper {
                        height: 800px !important;
                      }
                    </style>

                    <div
                      class="prospektad-adspiritflash1951503-container"
                      id=""
                      style="display: block"
                    >
                      <!--    <div class="prospektad-adspiritflash1951503-nav">-->
                      <!--        <span class="prospektad-adspiritflash1951503-w">-w-</span>-->
                      <!--        <div class="prospektad-adspiritflash1951503-nav-item prospektad-adspiritflash1951503-nav-item-active" data-index="0"></div>-->
                      <!--        <div class="prospektad-adspiritflash1951503-nav-item" data-index="1"></div>-->
                      <!--        <div class="prospektad-adspiritflash1951503-nav-item" data-index="2"></div>-->
                      <!--        <div class="prospektad-adspiritflash1951503-nav-item" data-index="3"></div>-->
                      <!--        <div class="prospektad-adspiritflash1951503-nav-item" data-index="4"></div>-->
                      <!--        <span id="prospektad-adspiritflash1951503-counter">1/5</span>-->
                      <!--    </div>-->

                      <div
                        class="prospektad-adspiritflash1951503-wrapper"
                        id=""
                        style=""
                      >
                        <div
                          class="prospektad-creatives-adspiritflash1951503"
                          id=""
                          style=""
                        >
                          <div
                            class="prospektad-creative-adspiritflash1951503"
                            data-index="0"
                            id=""
                            style=""
                          >
                            <ins
                              class="asm_async_creative"
                              data-asm-cdn="cdn-de.f11-ads.com"
                              data-asm-host="de.f11-ads.com"
                              data-asm-params="pid=7960&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"
                              data-asm-click="https://ads.qualitymedianetwork.de/adclick.php?tz=1751436295500249423925477&amp;pid=5002&amp;kid=1193&amp;wmid=4942&amp;wsid=703&amp;sid=1&amp;ord=1751436295&amp;vlx=cf7&amp;target="
                              data-asm-encode="1"
                              id="p2664x3"
                              style="
                                display: inline-block;
                                width: 550px;
                                height: 800px;
                                text-align: left;
                                text-decoration: none;
                                max-width: 550px;
                              "
                              data-asm-done="1"
                              data-asm-pushed="1"
                              ><script src="https://de.f11-ads.com/adscript.php?async=p2664x3&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=800&amp;vis=0&amp;tz=1751436296043&amp;prdclick_1=https%3A%2F%2Fads.qualitymedianetwork.de%2Fadclick.php%3Ftz%3D1751436295500249423925477%26pid%3D5002%26kid%3D1193%26wmid%3D4942%26wsid%3D703%26sid%3D1%26ord%3D1751436295%26vlx%3Dcf7%26target%3D&amp;pid=7960&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
                              <script
                                type="text/javascript"
                                src="https://de.f11-ads.com/adscript.php?pid=7960&amp;hr=1&amp;nrc=1&amp;&amp;async=p2664x3&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=800&amp;vis=&amp;tz=1751436296043&amp;prdclick_1=https%3A%2F%2Fads.qualitymedianetwork.de%2Fadclick.php%3Ftz%3D1751436295500249423925477%26pid%3D5002%26kid%3D1193%26wmid%3D4942%26wsid%3D703%26sid%3D1%26ord%3D1751436295%26vlx%3Dcf7%26target%3D&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;ptvf11de_x3207=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3326=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3368=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3367=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3366=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3365=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3387=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3364=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3380=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3324=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3370=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3348=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3347=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3359=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3358=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3384=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3369=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3381=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3272=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3354=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3361=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3360=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3375=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3376=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3363=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3391=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3393=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3392=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3353=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3269=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3346=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3388=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3291=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3295=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3327=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3325=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3379=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3355=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3373=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3390=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3377=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3378=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3372=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3389=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3362=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x3371=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x1189=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;ptvf11de_x1190=MT_LOAD_ON_DEMAND_%21E6jsVqu%3BtCF.%2C4x69wP~8FSTu2%25L_MT&amp;sid=371&amp;ex=|81156&amp;ord=1751436296144"
                              ></script>
                              <div
                                class="asmbeacon asmdivouter"
                                id="asmobj_580357"
                                style="
                                  z-index: 2500;
                                  position: relative;
                                  top: 0px;
                                  left: 0px;
                                  height: 601px;
                                  font-size: 1px;
                                  line-height: 1px;
                                  vertical-align: top;
                                  text-align: left;
                                  display: block;
                                "
                              >
                                <div
                                  class="asmbeacon asmdiv"
                                  id="asmobj_580357div"
                                  style="
                                    z-index: 2500;
                                    position: absolute;
                                    left: 0px;
                                    top: 0px;
                                    font-size: 1px;
                                    line-height: 1px;
                                    vertical-align: top;
                                    text-align: left;
                                    display: inline-block !important;
                                  "
                                >
                                  <div
                                    class="asmbeacon"
                                    id="adspiritflash2048655_scrolldivouter"
                                    style="
                                      position: relative;
                                      font-size: 1px;
                                      line-height: 1px;
                                      width: 1px;
                                      height: 1px;
                                      text-align: left;
                                      display: inline-block !important;
                                    "
                                  >
                                    <div
                                      class="asmbeacon asmscroller"
                                      id="adspiritflash2048655_scrolldiv"
                                      style="
                                        position: absolute;
                                        font-size: 1px;
                                        line-height: 1px;
                                        width: 300px;
                                        height: 601px;
                                        text-align: left;
                                        top: 0px;
                                        left: 0px;
                                      "
                                    >
                                      <div
                                        class="asmbeacon asmb3"
                                        id=""
                                        style="
                                          position: absolute;
                                          left: 0px;
                                          top: 0px;
                                        "
                                      >
                                        <img
                                          class="asmbeacon"
                                          border="0"
                                          style="
                                            border: 0px white solid !important;
                                            width: 1px;
                                            height: 1px;
                                          "
                                          width="1"
                                          height="1"
                                          alt=""
                                          src="https://de.f11-ads.com/adview.php?tz=17514362956121727960tzmacro&amp;&amp;pid=7960&amp;kid=81689&amp;wmid=279288&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=371&amp;target1=-"
                                        />
                                      </div>
                                      <iframe
                                        id="adspiritflash2048655"
                                        width="300"
                                        height="601"
                                        noresize="noresize"
                                        scrolling="no"
                                        frameborder="0"
                                        marginheight="0"
                                        marginwidth="0"
                                        src="https://www.ad-production-stage.com/manage-media/zgsf4y4zmn/20250630T111215-44848605/index.html?city=Berlin&amp;clicktag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143629579602792885063884%26pid%3D7960%26kid%3D81689%26wmid%3D279288%26wsid%3D1991%26sid%3D371%26ord%3D1751436296144%26clex%3D2592000%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D1751436295500249423925477%2526pid%253D5002%2526kid%253D1193%2526wmid%253D4942%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436295%2526vlx%253Dcf7%2526target%253D%26nenco%3D1%26vlx%3Dcf7%26target%3D&amp;clickTag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143629579602792885063884%26pid%3D7960%26kid%3D81689%26wmid%3D279288%26wsid%3D1991%26sid%3D371%26ord%3D1751436296144%26clex%3D2592000%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D1751436295500249423925477%2526pid%253D5002%2526kid%253D1193%2526wmid%253D4942%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436295%2526vlx%253Dcf7%2526target%253D%26nenco%3D1%26vlx%3Dcf7%26target%3D&amp;close=asmflash2048655_close&amp;collapse=asmflash2048655_collapse&amp;dppid=7960&amp;expand=asmflash2048655_expand&amp;flaechen-id=7960&amp;h=601&amp;kampagnen-id=81689&amp;lat=52.5196&amp;lon=13.4069&amp;ref=https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige.html&amp;refdomain=tierchenwelt.de&amp;w=300&amp;werbemittel-id=279288&amp;zip=10178&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"
                                        allowtransparency="true"
                                        allow="accelerometer;autoplay;encrypted-media;gyroscope;picture-in-picture"
                                        style="
                                          left: 0px;
                                          width: 1322px;
                                          height: 34px;
                                        "
                                      ></iframe>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <script
                                type="text/javascript"
                                src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
                              ></script
                            ></ins>
                          </div>
                          <div
                            class="prospektad-creative-adspiritflash1951503"
                            data-index="1"
                            id=""
                            style=""
                          ></div>
                          <div
                            class="prospektad-creative-adspiritflash1951503"
                            data-index="2"
                            id=""
                            style=""
                          ></div>
                          <div
                            class="prospektad-creative-adspiritflash1951503"
                            data-index="3"
                            id=""
                            style=""
                          ></div>
                          <div
                            class="prospektad-creative-adspiritflash1951503"
                            data-index="4"
                            id=""
                            style=""
                          ></div>
                        </div>
                      </div>

                      <!--    <button class="prospektad-adspiritflash1951503-btn">-->
                      <!--        Neues Angebot <svg width="2em" height="2em" viewBox="0 0 16 16" xmlns="https://www.w3.org/2000/svg"-->
                      <!--            fill="currentColor" class="bi bi-arrow-right-short">-->
                      <!--            <path fill-rule="evenodd"-->
                      <!--                d="M4 8a.5.5 0 0 1 .5-.5h5.793L8.146 5.354a.5.5 0 1 1 .708-.708l3 3a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708L10.293 8.5H4.5A.5.5 0 0 1 4 8z" />-->
                      <!--        </svg>-->
                      <!--    </button>-->
                      <!--    <div class="prospektad-adspiritflash1951503-image-container" id="prospektad-adspiritflash1951503-image-peel">-->
                      <!--        <div class="prospektad-adspiritflash1951503-image-mask"></div>-->
                      <!--        &lt;!&ndash; <img class="prospektad-adspiritflash1951503-scalable-image" src="peel-raw.png" /> &ndash;&gt;-->
                      <!--        <img class="prospektad-adspiritflash1951503-scalable-image" src="https://cdn-de.f11-ads.com/banner/qualitymedianetwork/603/2024-10-31/0_2024-10-31_hpa-peel_zip/peel-raw.png">-->
                      <!--    </div>-->
                    </div>
                    <span
                      class=""
                      id="prwrapper-adspiritflash1951503"
                      style=""
                    ></span>
                    <div
                      class="asmbeacon asmb3"
                      id=""
                      style="position: absolute; left: 0px; top: 0px"
                    >
                      <img
                        class="asmbeacon"
                        border="0"
                        style="
                          border: 0px white solid !important;
                          width: 1px;
                          height: 1px;
                        "
                        width="1"
                        height="1"
                        alt=""
                        src="https://ads.qualitymedianetwork.de/adview.php?tz=175143629574050725002tzmacro&amp;&amp;pid=5002&amp;kid=1193&amp;wmid=4942&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=1&amp;nvc=1&amp;target1=-"
                      />
                    </div>
                    <script
                      type="text/javascript"
                      src="https://cdn-de.f11-ads.com/adasync.min.js"
                    ></script>
                    <script
                      type="text/javascript"
                      src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
                    ></script
                  ></ins>
                </div>
                <div
                  class="uk-card uk-card-body uk-card-primary"
                  id="module-1269"
                >
                  <div class="uk-margin-remove-last-child custom">
                    <p
                      style="
                        text-align: center;
                        padding: 10px 0px 0px 0px;
                        font-size: 0.9rem;
                        margin: 0px !important;
                      "
                    >
                      Alle Inhalte auf tierchenwelt.de:
                    </p>
                    <ul
                      class="alphabet"
                      style="text-align: center; margin: 0px"
                    >
                      <li><a href="/tags/3176-a.html">A</a></li>
                      <li><a href="/tags/3177-b.html">B</a></li>
                      <li><a href="/tags/3178-c.html">C</a></li>
                      <li><a href="/tags/3179-d.html">D</a></li>
                      <li><a href="/tags/3180-e.html">E</a></li>
                      <li><a href="/tags/3181-f.html">F</a></li>
                      <li><a href="/tags/3182-g.html">G</a></li>
                      <li><a href="/tags/3183-h.html">H</a></li>
                      <li><a href="/tags/3184-i.html">I</a></li>
                      <li><a href="/tags/3185-j.html">J</a></li>
                      <li><a href="/tags/3186-k.html">K</a></li>
                      <li><a href="/tags/3187-l.html">L</a></li>
                      <li><a href="/tags/3188-m.html">M</a></li>
                      <li><a href="/tags/3189-n.html">N</a></li>
                      <li><a href="/tags/3190-o.html">O</a></li>
                      <li><a href="/tags/3191-p.html">P</a></li>
                      <li><a href="/tags/3192-q.html">Q</a></li>
                      <li><a href="/tags/3193-r.html">R</a></li>
                      <li><a href="/tags/3194-s.html">S</a></li>
                      <li><a href="/tags/3195-t.html">T</a></li>
                      <li><a href="/tags/3196-u.html">U</a></li>
                      <li><a href="/tags/3197-v.html">V</a></li>
                      <li><a href="/tags/3198-w.html">W</a></li>
                      <li>X</li>
                      <li><a href="/tags/3200-y.html">Y</a></li>
                      <li><a href="/tags/3201-z.html">Z</a></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <footer>
          <!-- Builder #footer --><style class="uk-margin-remove-adjacent">
            #footer\#0 {
              font-size: 0.8rem !important;
              font-weight: normal;
              color: #e5e5e5 !important;
              line-height: 1.5rem;
            }   
          </style>
          <div class="uk-section-secondary uk-section uk-section-small">
            <div class="uk-container uk-container-expand">
              <div
                class="uk-grid tm-grid-expand uk-child-width-1-1 uk-grid-margin uk-margin-remove-bottom"
              >
                <div class="uk-width-1-1">
                  <div id="footer#0">
                    <div
                      class="uk-grid uk-child-width-1-1 uk-child-width-1-4@m uk-flex-center uk-grid-small uk-grid-divider uk-grid-match"
                      uk-grid=""
                    >
                      <div class="uk-first-column">
                        <div
                          class="el-item uk-panel uk-margin-remove-first-child"
                        >
                          <div class="el-content uk-panel uk-margin-top">
                            <p>
                              <img
                                src="/images/stories/logo_tierchenwelt_ondark.png"
                                alt=""
                                width="147"
                                height="50"
                                loading="lazy"
                              />
                            </p>
                            <p>
                              Copyright © 2008-2025 tierchenwelt.de. Alle Rechte
                              vorbehalten. Kein Teil dieser Seite oder ihr
                              Inhalt darf ohne Erlaubnis der Rechteinhaber
                              vervielfältigt werden.
                            </p>
                          </div>
                        </div>
                      </div>
                      <div>
                        <div
                          class="el-item uk-panel uk-margin-remove-first-child"
                        >
                          <div class="el-content uk-panel uk-margin-top">
                            <p>• ÜBERSICHT:</p>
                            <ul class="foosty" style="padding-bottom: 60px">
                              <li><a href="/tierarten.html">Tierarten</a></li>
                              <li><a href="/tiernamen.html">Tiernamen</a></li>
                              <li><a href="/haustiere.html">Haustiere</a></li>
                              <li><a href="/funfacts.html">Tier-Rekorde</a></li>
                              <li><a href="/specials.html">Tier-Wissen</a></li>
                              <li><a href="/videos.html">Tier-Videos</a></li>
                              <li><a href="/fotos.html">Tier-Fotos</a></li>
                              <li>
                                <a href="/tier-spiele.html">Tier-Spiele</a>
                              </li>
                              <li><a href="/news.html">Tier-News</a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div>
                        <div
                          class="el-item uk-panel uk-margin-remove-first-child"
                        >
                          <div class="el-content uk-panel uk-margin-top">
                            <p>• NÜTZLICHE LINKS:</p>
                            <ul class="foosty">
                              <li>
                                <a
                                  href="/ueber-tierchenwelt/922-kinderinfo.html"
                                  >Über tierchenwelt.de</a
                                >
                              </li>
                              <li>
                                <a
                                  href="/ueber-tierchenwelt/4023-ueber-uns.html"
                                  >Über uns</a
                                >
                              </li>
                              <li>
                                <a
                                  href="/ueber-tierchenwelt/3140-auszeichnungen.html"
                                  >Auszeichnungen</a
                                >
                              </li>
                              <li>
                                <a href="/schul-lizenz.html">Schullizenz</a>
                              </li>
                              <li>
                                <a href="/schule/material-tierarten.html"
                                  >Unterrichtsmaterialien</a
                                >
                              </li>
                              <li>
                                <a href="/nutzungsbedingungen.html"
                                  >Nutzungsbedingungen</a
                                >
                              </li>
                              <li>
                                <a href="/datenschutz.html"
                                  >Datenschutzerklärung</a
                                >
                              </li>
                              <li>
                                <a href="/?cmpscreen">Cookie-Einstellungen</a>
                              </li>
                              <li><a href="/warum-werbung.html">Werbung</a></li>
                              <li><a href="/impressum.html">Impressum</a></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div>
                        <div
                          class="el-item uk-panel uk-margin-remove-first-child"
                        >
                          <div class="el-content uk-panel uk-margin-top">
                            <p>• FOLGE UNS:</p>
                            <ul class="foosty">
                              <li>
                                <a
                                  href="https://www.facebook.com/tierchenwelt"
                                  target="_blank"
                                  rel="nofollow noopener noreferrer"
                                  title="Besuche uns auf facebook"
                                  uk-icon="icon:facebook;ratio:1.5"
                                  class="uk-icon"
                                  ><svg
                                    width="30"
                                    height="30"
                                    viewBox="0 0 20 20"
                                    aria-hidden="true"
                                  >
                                    <path
                                      d="M11,10h2.6l0.4-3H11V5.3c0-0.9,0.2-1.5,1.5-1.5H14V1.1c-0.3,0-1-0.1-2.1-0.1C9.6,1,8,2.4,8,5v2H5.5v3H8v8h3V10z"
                                    ></path></svg></a
                                ><span>&nbsp;</span
                                ><a
                                  href="https://twitter.com/tierchenwelt"
                                  target="_blank"
                                  rel="nofollow noopener noreferrer"
                                  title="Besuche uns auf Twitter"
                                  uk-icon="icon:twitter;ratio:1.5"
                                  style="margin-left: 5px"
                                  class="uk-icon"
                                  ><svg
                                    width="30"
                                    height="30"
                                    viewBox="0 0 20 20"
                                    aria-hidden="true"
                                  >
                                    <path
                                      d="m15.08,2.1h2.68l-5.89,6.71,6.88,9.1h-5.4l-4.23-5.53-4.84,5.53H1.59l6.24-7.18L1.24,2.1h5.54l3.82,5.05,4.48-5.05Zm-.94,14.23h1.48L6,3.61h-1.6l9.73,12.71h0Z"
                                    ></path></svg></a
                                ><span>&nbsp;</span
                                ><a
                                  href="https://www.youtube.com/channel/UC9uFoOZG2SoGN7B9in2LCbg"
                                  target="_blank"
                                  rel="nofollow noopener noreferrer"
                                  title="Besuche uns auf YouTube"
                                  uk-icon="icon:youtube;ratio:1.5"
                                  style="margin-left: 5px"
                                  class="uk-icon"
                                  ><svg
                                    width="30"
                                    height="30"
                                    viewBox="0 0 20 20"
                                    aria-hidden="true"
                                  >
                                    <path
                                      d="M15,4.1c1,0.1,2.3,0,3,0.8c0.8,0.8,0.9,2.1,0.9,3.1C19,9.2,19,10.9,19,12c-0.1,1.1,0,2.4-0.5,3.4c-0.5,1.1-1.4,1.5-2.5,1.6 c-1.2,0.1-8.6,0.1-11,0c-1.1-0.1-2.4-0.1-3.2-1c-0.7-0.8-0.7-2-0.8-3C1,11.8,1,10.1,1,8.9c0-1.1,0-2.4,0.5-3.4C2,4.5,3,4.3,4.1,4.2 C5.3,4.1,12.6,4,15,4.1z M8,7.5v6l5.5-3L8,7.5z"
                                    ></path></svg></a
                                ><span>&nbsp;</span
                                ><a
                                  href="https://www.instagram.com/tierchenwelt.de/"
                                  target="_blank"
                                  rel="nofollow noopener noreferrer"
                                  title="Besuche uns auf Instagram"
                                  uk-icon="icon:instagram;ratio:1.5"
                                  style="margin-left: 5px"
                                  class="uk-icon"
                                  ><svg
                                    width="30"
                                    height="30"
                                    viewBox="0 0 20 20"
                                    aria-hidden="true"
                                  >
                                    <path
                                      d="M13.55,1H6.46C3.45,1,1,3.44,1,6.44v7.12c0,3,2.45,5.44,5.46,5.44h7.08c3.02,0,5.46-2.44,5.46-5.44V6.44 C19.01,3.44,16.56,1,13.55,1z M17.5,14c0,1.93-1.57,3.5-3.5,3.5H6c-1.93,0-3.5-1.57-3.5-3.5V6c0-1.93,1.57-3.5,3.5-3.5h8 c1.93,0,3.5,1.57,3.5,3.5V14z"
                                    ></path>
                                    <circle
                                      cx="14.87"
                                      cy="5.26"
                                      r="1.09"
                                    ></circle>
                                    <path
                                      d="M10.03,5.45c-2.55,0-4.63,2.06-4.63,4.6c0,2.55,2.07,4.61,4.63,4.61c2.56,0,4.63-2.061,4.63-4.61 C14.65,7.51,12.58,5.45,10.03,5.45L10.03,5.45L10.03,5.45z M10.08,13c-1.66,0-3-1.34-3-2.99c0-1.65,1.34-2.99,3-2.99s3,1.34,3,2.99 C13.08,11.66,11.74,13,10.08,13L10.08,13L10.08,13z"
                                    ></path></svg
                                ></a>
                              </li>
                            </ul>
                            <hr />
                            <p>• NETZWERK:</p>
                            <ul class="foosty" style="padding-bottom: 0px">
                              <li>
                                <a
                                  href="https://www.animalfunfacts.net/"
                                  target="_blank"
                                  rel="nofollow noopener noreferrer"
                                  >animalfunfacts.net<span>&nbsp;</span
                                  ><img
                                    src="/images/stories/flag_US_2.png"
                                    alt="animalfunfacts.net - The Animal Encyclopedia for Kids"
                                    width="28"
                                    height="28"
                                    style="margin-left: 5px"
                                    title="animalfunfacts.net - The Animal Encyclopedia for Kids"
                                    loading="lazy"
                                /></a>
                              </li>
                              <li>Besuche unsere englische<br />Seite!</li>
                            </ul>
                            <hr />
                            <p>
                              <img
                                src="/images/stories/tierchenwelt_green_hosting.png"
                                alt="This website runs on green hosting - verified by thegreenwebfoundation.org"
                                width="100"
                                height="48"
                                style="margin-left: 5px"
                                title="This website runs on green hosting - verified by thegreenwebfoundation.org"
                                loading="lazy"
                              />
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <iframe
      name="__cmpLocator"
      title="Intentionally hidden, please ignore"
      role="none"
      tabindex="-1"
      style="display: none"
    ></iframe
    ><iframe
      name="__uspapiLocator"
      title="Intentionally hidden, please ignore"
      role="none"
      tabindex="-1"
      style="display: none"
    ></iframe
    ><iframe
      name="__tcfapiLocator"
      title="Intentionally hidden, please ignore"
      role="none"
      tabindex="-1"
      style="display: none"
    ></iframe
    ><iframe
      name="__gppLocator"
      title="Intentionally hidden, please ignore"
      role="none"
      tabindex="-1"
      style="display: none"
    ></iframe>

    <script
      async=""
      type="text/javascript"
      data-cmp-ab="2"
      src="https://cdn.consentmanager.net/delivery/customdata/bV8xLndfNzQ3NDkucl9HRFBSLmxfZGUuZF8yODk2Ny54XzUzLnYucC50XzI4OTY3Lnh0XzQw.js"
    ></script>
    <iframe
      data-cmp-ab="1"
      src="https://cdn.consentmanager.net/delivery/crossdomain.html"
      name="__cmpcdframe"
      title="Intentionally hidden, please ignore"
      role="none"
      tabindex="-1"
      style="display: none"
    ></iframe
    ><style type="text/css">
      div.cmpwrapper:empty,
      div.cmpwrapper,
      div#cmpwrapper.cmpwrapper,
      div#cmpwrapper.cmpwrapper:empty {
        display: block !important;
      }
    </style>
    <div
      id="qmn5003"
      data-height="1"
      data-width="1"
      data-responsive="desktop"
      style="display: none"
    >
      <div
        class="qmn-ad-label"
        data-nosnippet="true"
        style="
          font-size: 0.7rem;
          color: rgb(51, 51, 51);
          text-align: center;
          padding: 5px 5px 2px;
          text-transform: uppercase;
          font-weight: bold;
        "
      >
        Werbung
      </div>
      <ins
        class="asm_async_creative"
        data-asm-cdn="cdn.f11-ads.com"
        data-asm-host="ads.qualitymedianetwork.de"
        data-asm-fetch-gdpr="1"
        data-asm-params="pid=5003"
        style="
          display: inline-block;
          width: 1px;
          height: 1px;
          text-align: left;
          text-decoration: none;
        "
        id="p36987x1"
        data-asm-done="1"
        data-asm-pushed="1"
        ><script src="https://ads.qualitymedianetwork.de/adscript.php?async=p36987x1&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=0&amp;vis=4&amp;tz=1751436283989&amp;pid=5003&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
        <div
          class="asmbeacon asmb2"
          id=""
          style="position: absolute; left: 0px; top: 0px"
        >
          <img
            class="asmbeacon"
            src="https://ads.qualitymedianetwork.de/adview.php?tz=175143628393170975003tzmacro&amp;&amp;pid=5003&amp;kid=1193&amp;wmid=4937&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=1&amp;nvc=1&amp;target=-"
            width="1"
            height="1"
            border="0"
            style="width: 1px; height: 1px; border: 0px white solid !important"
          />
        </div>
        <a
          class="asmlink"
          target="_blank"
          href="https://ads.qualitymedianetwork.de/adclick.php?tz=175143628350034937594653&amp;pid=5003&amp;kid=1193&amp;wmid=4937&amp;wsid=703&amp;sid=1&amp;ord=1751436283&amp;vlx=cf7&amp;target1="
          ><img
            class="asmbannerimg"
            id="asmimg6759095"
            border="0"
            style="border: 0px white solid !important; width: 1px; height: 1px"
            width="1"
            height="1"
            alt="NULL"
            src=""
        /></a>
        <script
          type="text/javascript"
          src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
        ></script
      ></ins>
    </div>
    <div
      id="qmn5004"
      data-height="600"
      data-width="300"
      data-responsive="desktop"
      class="floorad-desktop-adspiritflash2761382"
    >
      <div id="mflwrapper-adspiritflash2761382">
        <div class="mfldesign-adspiritflash2761382">
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 800 64"
            fill="none"
            preserveAspectRatio="none"
            xmlns="https://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_2021_10)">
              <path
                d="M344.951 8.79256C350.908 3.2265 359.188 0 367.882 0H432.118C440.812 0 449.092 3.2265 455.049 8.79256C458.329 11.8551 462.077 15.3088 465.598 18.445C478.46 29.8972 496.216 30.0253 496.613 30.0268H761.353C782.698 30.0268 800 45.2371 800 64H0C0 45.2371 17.303 30.0268 38.6473 30.0268H303.387C303.784 30.0253 321.54 29.8972 334.402 18.445C337.923 15.3088 341.671 11.8551 344.951 8.79256Z"
                fill="var(--backgroundColor-adspiritflash2761382)"
              ></path>
            </g>
          </svg>
        </div>
        <div style="position: relative">
          <div id="mflbuttons-adspiritflash2761382">
            <div
              id="mflup-adspiritflash2761382"
              class="mflopener-adspiritflash2761382"
            >
              <div
                class="mflarrow-adspiritflash2761382 mflarrowup-adspiritflash2761382 mflbounce-adspiritflash2761382"
              >
                <span>〈</span>
              </div>
              <div
                class="mflarrow-adspiritflash2761382 mflarrowup-adspiritflash2761382 mflarrowup2-adspiritflash2761382 mflbounce-adspiritflash2761382"
              >
                <span>〈</span>
              </div>
            </div>
            <div
              id="mfldown-adspiritflash2761382"
              class="mflopener-adspiritflash2761382"
            >
              <div
                class="mflarrow-adspiritflash2761382 mflarrowdown-adspiritflash2761382"
              >
                <span>〉</span>
              </div>
              <div
                class="mflarrow-adspiritflash2761382 mflarrowdown-adspiritflash2761382 mflarrowdown2-adspiritflash2761382"
              >
                <span>〉</span>
              </div>
            </div>
            <div id="mflclose-adspiritflash2761382">
              <svg
                width="15"
                height="15"
                fill="none"
                xmlns="https://www.w3.org/2000/svg"
                style="scale: 0.7"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M.305.305a1.042 1.042 0 0 1 1.473 0L7.292 5.82 12.805.305a1.042 1.042 0 1 1 1.473 1.473L8.765 7.292l5.513 5.513a1.042 1.042 0 1 1-1.473 1.473L7.292 8.765l-5.514 5.513a1.042 1.042 0 0 1-1.473-1.473L5.82 7.292.305 1.778a1.042 1.042 0 0 1 0-1.473z"
                  fill="#505050"
                ></path>
              </svg>
            </div>
            <div
              id="mflwerbung-adspiritflash2761382"
              data-nosnippet="true"
              style="
                color: rgb(51, 51, 51);
                font-size: 0.7rem;
                text-align: center;
                padding: 5px 5px 2px;
                text-transform: uppercase;
                font-weight: bold;
              "
            >
              Werbung
            </div>
            <a
              id="mflLogo-adspiritflash2761382"
              href="https://www.qualitymedianetwork.de"
              target="_blank"
              ><svg
                width="100%"
                height="100%"
                viewBox="0 0 275 100"
                fill="none"
                xmlns="https://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M70.234 73.534C71.579 74.579 73 76 73 76l11.5 11.5-.568-.105c-7.717-1.394-13.527-1.36-17.432.105l-.28.104c-5.945 2.19-10.588 2.654-17.076 2.353a40 40 0 1 1 41.16-47.387c.97 5.133.945 10.16-.05 15-.882 4.293-2.526 8.439-4.917 12.383-.11.183-.223.365-.337.547l-9.5-9s-.085-.026-.156-.138a.511.511 0 0 1-.045-.092c-.077-.204-.087-.582.201-1.27 1.993-4.765 2.565-9.796 1.594-14.933A26.556 26.556 0 1 0 64.615 72.8c.877-.523 2.235-.707 3.486-.402.*************.608.192.277.109.543.245.791.41a13.988 13.988 0 0 1 .734.533z"
                  fill="#006AFF"
                ></path>
                <path
                  d="m106 13 37.5 44L181 13.077V86h-12V45.5l-25.5 30-25.5-30V86h-12V13zM200 13l52 47.5V13h12v73l-52-46.5V86h-12V13z"
                  fill="#006AFF"
                ></path></svg
            ></a>
          </div>
        </div>
      </div>
      <div id="mobileFloorAdbackground-adspiritflash2761382"></div>
      <ins
        class="asm_async_creative mobileFloorAdiframe-adspiritflash2761382"
        data-asm-cdn="cdn.f11-ads.com"
        data-asm-host="ads.qualitymedianetwork.de"
        data-asm-fetch-gdpr="1"
        data-asm-params="pid=5004"
        style="
          display: inline-block;
          width: 300px;
          height: 600px;
          text-align: left;
          text-decoration: none;
        "
        id="p71278x2"
        data-asm-done="1"
        data-asm-pushed="1"
        scrolling="no"
        width="635.8809891808346"
        height="769"
        ><script src="https://ads.qualitymedianetwork.de/adscript.php?async=p71278x2&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=300&amp;vis=0&amp;tz=1751436283990&amp;pid=5004&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
        <style id="f11-floorAd-adspiritflash2761382">
          :root {
            --backgroundColor-adspiritflash2761382: #dadadae3;
            --buttonColor-adspiritflash2761382: #989898;
            --arrowColor-adspiritflash2761382: #fff;
          }

          iframe#adspiritflash2761382 {
            width: 100%;
          }

          .floorad-desktop-adspiritflash2761382 iframe,
          .floorad-desktop-adspiritflash2761382 ins {
            width: 800px !important;
            height: 1100px !important;
          }

          .floorad-desktop-adspiritflash2761382 svg {
            max-width: initial;
            height: initial;
          }
          .mobileFloorAdiframe-adspiritflash2761382 .asmdivouter {
            top: 0px !important;
          }

          .mobileFloorAdiframe-adspiritflash2761382 {
            background-color: #fff;
            width: 800px !important;
            height: 1100px !important;
            position: fixed;
            bottom: calc(100px - 860px);
            left: calc(50% - 225px);
            transition: transform 1s;
            line-height: 0px;
            font-size: 0px;
            z-index: 10000001;
            text-align: center !important;
            /* bottom:0px; */
          }
          #mobileFloorAdbackground-adspiritflash2761382 {
            background-color: #fff;
            width: 800px;
            height: 1100px;
            position: fixed;
            bottom: calc(100px - 860px);
            left: calc(50% - 225px);
            transition: transform 1s;
            z-index: 10000000;
            -webkit-box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.53);
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.53);
          }
          #mflwrapper-adspiritflash2761382 {
            position: fixed;
            bottom: 100px;
            left: 0px;
            z-index: 100000001;
            width: 100%;
            height: 38px;
            transition: transform 1s;
          }
          #mobileFloorAd-adspiritflash2761382 {
            background-color: #dadada;
            width: 100%;
            height: 1100px;
            position: fixed;
            bottom: calc(100px - 860px);
            left: 0px;
            transition: transform 1s;
            -webkit-box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.53);
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.53);
          }
          .mflopener-adspiritflash2761382 {
            width: 52px;
            height: 30px;
            position: relative;
            line-height: 1.5;
            left: calc(50% - 26px);
            top: -6px;
            transition: transform 1s;
            border-radius: 4px;
            -webkit-box-shadow: 0px 0px 6px 0px rgb(129 129 129 / 53%);
            box-shadow: 0px 0px 6px 0px rgb(129 129 129 / 53%);
          }
          #mflup-adspiritflash2761382 {
            background-color: var(--buttonColor-adspiritflash2761382);
            display: block;
          }
          #mfldown-adspiritflash2761382 {
            background-color: var(--buttonColor-adspiritflash2761382);
            display: none;
          }

          @-moz-keyframes mflbounce {
            0%,
            20%,
            50%,
            80%,
            100% {
              -moz-transform: translateY(0) rotate(90deg);
              transform: translateY(0) rotate(90deg);
            }
            40% {
              -moz-transform: translateY(-7px) rotate(90deg);
              transform: translateY(-7px) rotate(90deg);
            }
            60% {
              -moz-transform: translateY(-2px) rotate(90deg);
              transform: translateY(-2px) rotate(90deg);
            }
          }
          @-webkit-keyframes mflbounce {
            0%,
            20%,
            50%,
            80%,
            100% {
              -webkit-transform: translateY(0) rotate(90deg);
              transform: translateY(0) rotate(90deg);
            }
            40% {
              -webkit-transform: translateY(-7px) rotate(90deg);
              transform: translateY(-7px) rotate(90deg);
            }
            60% {
              -webkit-transform: translateY(-2px) rotate(90deg);
              transform: translateY(-2px) rotate(90deg);
            }
          }
          @keyframes mflbounce {
            0%,
            20%,
            50%,
            80%,
            100% {
              -moz-transform: translateY(0) rotate(90deg);
              -ms-transform: translateY(0) rotate(90deg);
              -webkit-transform: translateY(0) rotate(90deg);
              transform: translateY(0) rotate(90deg);
            }
            40% {
              -moz-transform: translateY(-7px) rotate(90deg);
              -ms-transform: translateY(-7px) rotate(90deg);
              -webkit-transform: translateY(-7px) rotate(90deg);
              transform: translateY(-7px) rotate(90deg);
            }
            60% {
              -moz-transform: translateY(-2px) rotate(90deg);
              -ms-transform: translateY(-2px) rotate(90deg);
              -webkit-transform: translateY(-2px) rotate(90deg);
              transform: translateY(-2px) rotate(90deg);
            }
          }

          .mflarrow-adspiritflash2761382 {
            position: absolute;
            left: 50%;
            margin-left: -5px;
            width: 20px;
            height: 20px;
            color: white;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 20px;
            font-weight: bold;
            -webkit-transform: rotate(90deg);
            -moz-transform: rotate(90deg);
            -o-transform: rotate(90deg);
            -ms-transform: rotate(90deg);
            transform: rotate(90deg);
            /* writing-mode: vertical-rl;*/
            /* writing-mode:tb-rl; */
          }

          .mflarrowup-adspiritflash2761382 {
            bottom: 8px;
          }
          .mflarrowup2-adspiritflash2761382 {
            bottom: 14px;
          }
          .mflarrowdown-adspiritflash2761382 {
            bottom: 3px;
          }
          .mflarrowdown2-adspiritflash2761382 {
            bottom: -3px;
          }
          .mflbounce-adspiritflash2761382 {
            -moz-animation: mflbounce 2s infinite;
            -webkit-animation: mflbounce 2s infinite;
            animation: mflbounce 2s infinite;
            animation-iteration-count: 2;
          }

          #mflclose-adspiritflash2761382 {
            display: block;
            width: 12px;
            height: 30px;
            color: #323232;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 18px;
            font-weight: 100;
            position: absolute;
            top: 12px;
            left: 22px;
            text-align: center;
            line-height: 1.9;
          }

          #mflLogo-adspiritflash2761382 {
            height: 14px;
            width: 38px;
            display: none;
            position: absolute;
            top: 16px;
            right: 22px;
            line-height: 1.7;
            font-size: 18px;
          }
          #mflwerbung-adspiritflash2761382 {
            position: absolute;
            top: 10px;
            left: 50px;
            font-size: 12px;
            color: black;
            /*display: none;*/
            line-height: 30.6px;
          }
          .mfldesign-adspiritflash2761382 {
            position: absolute;
            bottom: -1px;
            display: flex;
            z-index: -1;
            width: 100%;
            height: 100%;
          }

          #mflbuttons-adspiritflash2761382 {
            z-index: 1000;
            cursor: pointer;
          }
          .trf-ad-bottom-sticky-label {
            display: none !important;
          }
        </style>
        <style id="f11-floorAd-adspiritflash2761382-overwrite">
          .floorad-desktop-adspiritflash2761382 iframe,
          .floorad-desktop-adspiritflash2761382 ins {
            width: 635.8809891808346px !important;
            height: 769px !important;
          }
          .mobileFloorAdiframe-adspiritflash2761382 {
            width: 635.8809891808346px !important;
            height: 769px !important;
            bottom: calc(100px - 769px);
            left: calc(50% - 317.9404945904173px);
          }
          .mobileFloorAdiframe-adspiritflash2761382 .asmbeacon.asmscroller {
            width: 635.8809891808346px !important;
            height: 769px !important;
          }
          #mobileFloorAdbackground-adspiritflash2761382 {
            width: 635.8809891808346px;
            height: 769px;
            bottom: calc(100px - 769px);
            left: calc(50% - 317.9404945904173px);
          }
          #mobileFloorAd-adspiritflash2761382 {
            height: 769px;
          }
          #mflwrapper-adspiritflash2761382 {
            width: 635.8809891808346px;
            left: calc(50% - 317.9404945904173px);
          }
        </style>

        <span class="mflbefore" id="mflwrapper-adspiritflash2761382" style="">
          <!-- // Werbefläche -->
        </span>
        <div
          class="asmbeacon asmb3"
          id=""
          style="position: absolute; left: 0px; top: 0px"
        >
          <img
            class="asmbeacon"
            border="0"
            style="border: 0px white solid !important; width: 1px; height: 1px"
            width="1"
            height="1"
            alt=""
            src="https://ads.qualitymedianetwork.de/adview.php?tz=175143628352832105004tzmacro&amp;&amp;pid=5004&amp;kid=1193&amp;wmid=4938&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=1&amp;nvc=1&amp;target1=-"
          />
        </div>
        <ins
          class="asm_async_creative"
          data-asm-cdn="cdn-de.f11-ads.com"
          data-asm-host="de.f11-ads.com"
          data-asm-params="pid=7954&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"
          data-asm-click="https://ads.qualitymedianetwork.de/adclick.php?tz=175143628350044938462001&amp;pid=5004&amp;kid=1193&amp;wmid=4938&amp;wsid=703&amp;sid=1&amp;ord=1751436283&amp;vlx=cf7&amp;target="
          data-asm-encode="1"
          id="p95717x4"
          style="
            display: inline-block;
            width: 300px;
            height: 600px;
            text-align: left;
            text-decoration: none;
          "
          data-asm-done="1"
          data-asm-pushed="1"
          ><script src="https://de.f11-ads.com/adscript.php?async=p95717x4&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=1920&amp;scy=1080&amp;wcx=1920&amp;wcy=919&amp;dcx=636&amp;vis=4&amp;tz=1751436284377&amp;prdclick_1=https%3A%2F%2Fads.qualitymedianetwork.de%2Fadclick.php%3Ftz%3D175143628350044938462001%26pid%3D5004%26kid%3D1193%26wmid%3D4938%26wsid%3D703%26sid%3D1%26ord%3D1751436283%26vlx%3Dcf7%26target%3D&amp;pid=7954&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
          <div
            class="asmbeacon asmdivouter"
            id="asmobj_63641"
            style="
              z-index: 2500;
              position: relative;
              top: 0px;
              left: 0px;
              height: 601px;
              font-size: 1px;
              line-height: 1px;
              vertical-align: top;
              text-align: left;
              display: block;
            "
          >
            <div
              class="asmbeacon asmdiv"
              id="asmobj_63641div"
              style="
                z-index: 2500;
                position: absolute;
                left: 0px;
                top: 0px;
                font-size: 1px;
                line-height: 1px;
                vertical-align: top;
                text-align: left;
                display: inline-block !important;
              "
            >
              <div
                class="asmbeacon"
                id="adspiritflash5409017_scrolldivouter"
                style="
                  position: relative;
                  font-size: 1px;
                  line-height: 1px;
                  width: 1px;
                  height: 1px;
                  text-align: left;
                  display: inline-block !important;
                "
              >
                <div
                  class="asmbeacon asmscroller"
                  id="adspiritflash5409017_scrolldiv"
                  style="
                    position: absolute;
                    font-size: 1px;
                    line-height: 1px;
                    width: 300px;
                    height: 601px;
                    text-align: left;
                    top: 0px;
                    left: 0px;
                  "
                >
                  <div
                    class="asmbeacon asmb3"
                    id=""
                    style="position: absolute; left: 0px; top: 0px"
                  >
                    <img
                      class="asmbeacon"
                      border="0"
                      style="
                        border: 0px white solid !important;
                        width: 1px;
                        height: 1px;
                      "
                      width="1"
                      height="1"
                      alt=""
                      src="https://de.f11-ads.com/adview.php?tz=175143628390577607954tzmacro&amp;&amp;pid=7954&amp;kid=81688&amp;wmid=279285&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=422&amp;target1=-"
                    />
                  </div>
                  <iframe
                    id="adspiritflash5409017"
                    width="300"
                    height="601"
                    noresize="noresize"
                    scrolling="no"
                    frameborder="0"
                    marginheight="0"
                    marginwidth="0"
                    src="https://www.ad-production-stage.com/manage-media/2cue9kwph0/20250630T110128-79382433/index.html?city=Berlin&amp;clicktag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143628379542792851164077%26pid%3D7954%26kid%3D81688%26wmid%3D279285%26wsid%3D1991%26sid%3D422%26ord%3D1751436283%26clex%3D2592000%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D175143628350044938462001%2526pid%253D5004%2526kid%253D1193%2526wmid%253D4938%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436283%2526vlx%253Dcf7%2526target%253D%26nenco%3D1%26vlx%3Dcf7%26target%3D&amp;clickTag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143628379542792851164077%26pid%3D7954%26kid%3D81688%26wmid%3D279285%26wsid%3D1991%26sid%3D422%26ord%3D1751436283%26clex%3D2592000%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D175143628350044938462001%2526pid%253D5004%2526kid%253D1193%2526wmid%253D4938%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436283%2526vlx%253Dcf7%2526target%253D%26nenco%3D1%26vlx%3Dcf7%26target%3D&amp;close=asmflash5409017_close&amp;collapse=asmflash5409017_collapse&amp;dppid=7954&amp;expand=asmflash5409017_expand&amp;flaechen-id=7954&amp;h=601&amp;kampagnen-id=81688&amp;lat=52.5196&amp;lon=13.4069&amp;ref=https://tierchenwelt.de/tiernamen/tiernamen-goetter-koenige.html&amp;refdomain=tierchenwelt.de&amp;w=300&amp;werbemittel-id=279285&amp;zip=10178&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"
                    allowtransparency="true"
                    allow="accelerometer;autoplay;encrypted-media;gyroscope;picture-in-picture"
                    style="
                      background-color: rgb(237, 237, 237);
                      left: 0px;
                      width: 1365px;
                      height: 100px;
                    "
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
          <script
            type="text/javascript"
            src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
          ></script
        ></ins>
        <script
          type="text/javascript"
          src="https://cdn-de.f11-ads.com/adasync.min.js"
        ></script>
        <script
          type="text/javascript"
          src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
        ></script
      ></ins>
    </div>
    <div
      id="qmn5005"
      data-height="600"
      data-width="300"
      data-responsive="desktop"
      class="sitebar-adspiritflash5944841 sitebar-adspiritflash8287974 sitebar-fixed-adspiritflash5944841 sitebar-fixed-adspiritflash8287974 sitebar-adspiritflash6704816 sitebar-fixed-adspiritflash6704816"
      style="
        opacity: 1;
        pointer-events: all;
        top: 0px;
        left: 1262px;
        transition: opacity 0.1s linear, left 0.2s linear, width 0.5s linear,
          transform 0.5s;
        max-width: 600px;
        width: 832px;
      "
    >
      <div
        class="qmn-ad-label"
        data-nosnippet="true"
        style="
          font-size: 0.7rem;
          color: rgb(51, 51, 51);
          text-align: center;
          padding: 5px 5px 2px;
          text-transform: uppercase;
          font-weight: bold;
        "
      >
        Werbung
      </div>
      <ins
        class="asm_async_creative"
        data-asm-cdn="cdn.f11-ads.com"
        data-asm-host="ads.qualitymedianetwork.de"
        data-asm-fetch-gdpr="1"
        data-asm-params="pid=5005&amp;&amp;rfpid=75&amp;rfcnt=2&amp;tz=1751436434664"
        style="
          display: inline-block;
          width: 300px;
          height: 600px;
          text-align: left;
          text-decoration: none;
        "
        id="p59939x3"
        data-asm-done="1"
        data-asm-pushed="1"
        ><script src="https://ads.qualitymedianetwork.de/adscript.php?async=p59939x3&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=2094&amp;scy=1361&amp;wcx=2094&amp;wcy=1361&amp;dcx=300&amp;vis=0&amp;tz=1751436434695&amp;pid=5005&amp;&amp;rfpid=75&amp;rfcnt=2&amp;tz=1751436434664&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
        <style id="sitebar-css-adspiritflash6704816">
          .sitebar-adspiritflash6704816 {
            /*specifies smooth animations for changes in the opacity & left properties*/
            transition: opacity 0.1s linear, left 0.2s linear, width 0.5s linear,
              transform 0.5s;
            position: relative;
            /*determins the positon in the stacking order of the element*/
            z-index: 1;
          }
          .sitebar-fixed-adspiritflash6704816 {
            position: fixed;
          }

          /*all ins and iframe elements with tha class sitebar-adspiritflash6704816 have no background (can not be overwritten)*/
          .sitebar-adspiritflash6704816 ins,
          .sitebar-adspiritflash6704816 iframe {
            background: none !important;
          }
        </style>

        <span
          class="sitebarbefore"
          id="sitebarwrapper-adspiritflash6704816"
          style=""
        >
          <!-- // Werbefläche -->
        </span>
        <div
          class="asmbeacon asmdivouter"
          id="asmobj_728211"
          style="
            z-index: 2500;
            position: relative;
            top: 0px;
            left: 0px;
            height: 601px;
            font-size: 1px;
            line-height: 1px;
            vertical-align: top;
            text-align: left;
            display: inline-block !important;
          "
        >
          <div
            class="asmbeacon asmdiv"
            id="asmobj_728211div"
            style="
              z-index: 2500;
              position: absolute;
              left: 0px;
              top: 0px;
              font-size: 1px;
              line-height: 1px;
              vertical-align: top;
              text-align: left;
              display: inline-block !important;
            "
          >
            <div
              class="asmbeacon"
              id="adspiritflash6704816_scrolldivouter"
              style="
                position: relative;
                font-size: 1px;
                line-height: 1px;
                width: 1px;
                height: 1px;
                text-align: left;
                display: inline-block !important;
              "
            >
              <div
                class="asmbeacon asmscroller"
                id="adspiritflash6704816_scrolldiv"
                style="
                  position: absolute;
                  font-size: 1px;
                  line-height: 1px;
                  width: 300px;
                  height: 601px;
                  text-align: left;
                  top: 0px;
                  left: 0px;
                "
              >
                <div
                  class="asmbeacon asmb3"
                  id=""
                  style="position: absolute; left: 0px; top: 0px"
                >
                  <img
                    class="asmbeacon"
                    border="0"
                    style="
                      border: 0px white solid !important;
                      width: 1px;
                      height: 1px;
                    "
                    width="1"
                    height="1"
                    alt=""
                    src="https://ads.qualitymedianetwork.de/adview.php?tz=175143643375202275005tzmacro&amp;&amp;pid=5005&amp;kid=1193&amp;wmid=4945&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=1&amp;nvc=1&amp;target1=-"
                  />
                </div>
                <ins
                  class="asm_async_creative"
                  data-asm-cdn="cdn-de.f11-ads.com"
                  data-asm-host="de.f11-ads.com"
                  data-asm-params="pid=7952&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"
                  data-asm-click="https://ads.qualitymedianetwork.de/adclick.php?tz=1751436433500549455339459&amp;pid=5005&amp;kid=1193&amp;wmid=4945&amp;wsid=703&amp;sid=1&amp;ord=1751436433&amp;vlx=9d6&amp;target="
                  data-asm-encode="1"
                  id="p70156x8"
                  style="
                    display: inline-block;
                    width: 300px;
                    height: 600px;
                    text-align: left;
                    text-decoration: none;
                  "
                  data-asm-done="1"
                  data-asm-pushed="1"
                  ><script src="https://de.f11-ads.com/adscript.php?async=p70156x8&amp;wpcn=asm32418431x1751436283986&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;swf=-1&amp;scx=2094&amp;scy=1361&amp;wcx=2094&amp;wcy=1361&amp;dcx=300&amp;vis=0&amp;tz=1751436434792&amp;prdclick_1=https%3A%2F%2Fads.qualitymedianetwork.de%2Fadclick.php%3Ftz%3D1751436433500549455339459%26pid%3D5005%26kid%3D1193%26wmid%3D4945%26wsid%3D703%26sid%3D1%26ord%3D1751436433%26vlx%3D9d6%26target%3D&amp;pid=7952&amp;gdpr=1&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA"></script>
                  <span
                    class=""
                    data-dv-view="79881272294"
                    id="79881272294"
                    style=""
                  >
                  </span>
                  <div
                    class="asmbeacon asmdivouter"
                    id="asmobj_765209"
                    style="
                      z-index: 2500;
                      position: relative;
                      top: 0px;
                      left: 0px;
                      height: 601px;
                      font-size: 1px;
                      line-height: 1px;
                      vertical-align: top;
                      text-align: left;
                      display: block;
                    "
                  >
                    <div
                      class="asmbeacon asmdiv"
                      id="asmobj_765209div"
                      style="
                        z-index: 2500;
                        position: absolute;
                        left: 0px;
                        top: 0px;
                        font-size: 1px;
                        line-height: 1px;
                        vertical-align: top;
                        text-align: left;
                        display: inline-block !important;
                      "
                    >
                      <div
                        class="asmbeacon"
                        id="adspiritflash994023_scrolldivouter"
                        style="
                          position: relative;
                          font-size: 1px;
                          line-height: 1px;
                          width: 1px;
                          height: 1px;
                          text-align: left;
                          display: inline-block !important;
                        "
                      >
                        <div
                          class="asmbeacon asmscroller"
                          id="adspiritflash994023_scrolldiv"
                          style="
                            position: absolute;
                            font-size: 1px;
                            line-height: 1px;
                            width: 300px;
                            height: 601px;
                            text-align: left;
                            top: 0px;
                            left: 0px;
                          "
                        >
                          <div class="" id="adv272294" style="display: none">
                            <img
                              src="https://de.f11-ads.com/adview.php?tz=175143643452107127952tzmacro&amp;&amp;pid=7952&amp;kid=79881&amp;wmid=272294&amp;gdpr_consent=CQTqZzAQTqZzAAfLtBDEBxFgAP_gAEPgAAYgGhwIgAFAAVAA4ACAAEQAJYATgBQACwAGQANAAcwBFAEYAJgATgAtgB-AEAAIoAhABEwCLAEpgJuAnABdQC_AGKANOAi8BGoCbAF5gMbAZQA0MC84BoAFQAOAAgABkADQAJgAfgCEAEWAJSAYoBF4C8wAAA&amp;sid=406&amp;target=&amp;sid2=01675"
                            />
                          </div>
                          <iframe
                            id="adspiritflash994023"
                            width="300"
                            height="601"
                            noresize="noresize"
                            scrolling="no"
                            frameborder="0"
                            marginheight="0"
                            marginwidth="0"
                            src="https://www.ad-production-stage.com/11-1952/cbkkb4tdeb/20250502T132616-96922332/index.html?clicktag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143643479522722945173772%26pid%3D7952%26kid%3D79881%26wmid%3D272294%26wsid%3D1991%26sid%3D406%26ord%3D1751436434%26clex%3D2592000%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D1751436433500549455339459%2526pid%253D5005%2526kid%253D1193%2526wmid%253D4945%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436433%2526vlx%253D9d6%2526target%253D%26nenco%3D1%26vlx%3D9d6%26target%3D&amp;clickTag=https%3A%2F%2Fde.f11-ads.com%2Fadclick.php%3Ftz%3D175143643479522722945173772%26pid%3D7952%26kid%3D79881%26wmid%3D272294%26wsid%3D1991%26sid%3D406%26ord%3D1751436434%26clex%3D2592000%26rdclick_1%3Dhttps%253A%252F%252Fads.qualitymedianetwork.de%252Fadclick.php%253Ftz%253D1751436433500549455339459%2526pid%253D5005%2526kid%253D1193%2526wmid%253D4945%2526wsid%253D703%2526sid%253D1%2526ord%253D1751436433%2526vlx%253D9d6%2526target%253D%26nenco%3D1%26vlx%3D9d6%26target%3D&amp;collapse=asmflash994023_collapse&amp;expand=asmflash994023_expand&amp;mid=3586&amp;lat=52.5196&amp;lon=13.4069&amp;city=Berlin&amp;zip=10178&amp;kampagnen-id=79881&amp;flaechen-id=7952&amp;werbemittel-id=272294&amp;dppid=7952&amp;ref=https%3A%2F%2Ftierchenwelt.de%2Ftiernamen%2Ftiernamen-goetter-koenige.html&amp;refdomain=tierchenwelt.de&amp;svr=de.f11-ads.com&amp;viewtag=&amp;haendlerid=01675"
                            allowtransparency="true"
                            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                            style="
                              background-color: rgb(237, 237, 237);
                              margin: 0px;
                              max-height: calc(0px + 100vh);
                              max-width: 600px;
                              left: 0px;
                              width: 832px;
                              height: 1334px;
                            "
                          ></iframe>
                        </div>
                      </div>
                    </div>
                  </div>
                  <script
                    type="text/javascript"
                    src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
                  ></script
                ></ins>
                <script
                  type="text/javascript"
                  src="https://cdn-de.f11-ads.com/adasync.min.js"
                ></script>
              </div>
            </div>
          </div>
        </div>
        <script
          type="text/javascript"
          src="https://cdn-de.f11-ads.com/banner/asm_pageview.min.js"
        ></script
      ></ins>
    </div>
    <script
      src="https://cdn.f11-ads.com/adasync.min.js"
      async=""
      type="text/javascript"
    ></script>
    <div style="display: none; visibility: hidden">
      <script>
        (function () {
          var a = document.createElement("script");
          a.type = "text/javascript";
          a.src = "https://securepubads.g.doubleclick.net/tag/js/gpt.js";
          a.async = "true";
          document.getElementsByTagName("head")[0].appendChild(a);
        })();
        window.googletag = window.googletag || { cmd: [] };
        googletag.cmd.push(function () {
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_LB",
              [
                [728, 90],
                [970, 250],
              ],
              "div-gpt-ad-1530627637009-37"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_LB_2",
              [[700, 70]],
              "div-gpt-ad-1530627637009-38"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_MPU",
              [
                [300, 250],
                [300, 600],
              ],
              "div-gpt-ad-1530627637009-39"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_MPU_2",
              [
                [300, 250],
                [300, 600],
              ],
              "div-gpt-ad-1530627637009-40"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_MPU_3",
              [
                [300, 250],
                [300, 600],
              ],
              "div-gpt-ad-1530627637009-41"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_MPU_MOB",
              [
                [300, 250],
                [300, 600],
              ],
              "div-gpt-ad-1530627637009-42"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_MPU_MOB_2",
              [300, 250],
              "div-gpt-ad-1530627637009-43"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_MPU_MOB_3",
              [300, 250],
              "div-gpt-ad-1530627637009-44"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684,22396165180/TWDE_STICKY_LB",
              [
                [320, 50],
                [320, 101],
                [320, 100],
              ],
              "div-gpt-ad-1559908591910-0"
            )
            .addService(googletag.pubads());
          googletag
            .defineSlot(
              "/43340684/TWDE_TEST_KB",
              [
                [970, 250],
                [728, 90],
              ],
              "div-gpt-ad-1664894944128-0"
            )
            .addService(googletag.pubads());
          window.googletag.pubads().setRequestNonPersonalizedAds(0);
          googletag.pubads().addEventListener("slotRenderEnded", function (a) {
            document
              .querySelector("#" + a.slot.getSlotElementId())
              .getAttribute("id")
              .indexOf("STICKY_LB") === -1 ||
              a.isEmpty ||
              ((document.querySelector("#sticky_leader_close").styles.display =
                block),
              (document.querySelector("#sticky_leaderboard").styles.paddingTop =
                "4px"),
              (document.querySelector("body").styles.paddingBottom = "90px"));
          });
          googletag.enableServices();
        });
        document.dispatchEvent(new Event("displayAdSlots"));
      </script>
    </div>
    <script type="text/javascript">
      var valuepassback = "";
    </script>
    <iframe
      id="iframe_880555575357"
      name="iframe_880555575357"
      data-dv-frm="880555575357"
      width="0"
      height="0"
      src="about:blank"
      style="display: none"
    ></iframe>
    <script
      src="https://cdn.doubleverify.com/dvtp_src.js#dvp_scripthash=1&amp;t2te=0&amp;seltag=1&amp;adsrv=0&amp;cmp=DV726174&amp;ctx=24835144&amp;sadv=1&amp;ord=1&amp;litm=81156&amp;scrt=279301&amp;splc=7952&amp;adu=1991&amp;unit=300x601&amp;btreg=81156279301&amp;btadsrv=81156279301"
      async=""
    ></script>
    <iframe
      id="iframe_590735699404"
      name="iframe_590735699404"
      data-dv-frm="590735699404"
      width="0"
      height="0"
      src="about:blank"
      style="display: none"
    ></iframe>
    <script
      src="https://cdn.doubleverify.com/dvtp_src.js#dvp_scripthash=1&amp;t2te=0&amp;seltag=1&amp;adsrv=0&amp;cmp=DV726174&amp;ctx=24835144&amp;sadv=1&amp;ord=1&amp;litm=81606&amp;scrt=279274&amp;splc=7953&amp;adu=2099&amp;unit=800x250&amp;btreg=81606279274&amp;btadsrv=81606279274"
      async=""
    ></script>
    <iframe
      id="iframe_607865367588"
      name="iframe_607865367588"
      data-dv-frm="607865367588"
      width="0"
      height="0"
      src="about:blank"
      style="display: none"
    ></iframe>
    <script
      src="https://cdn.doubleverify.com/dvtp_src.js#dvp_scripthash=1&amp;t2te=0&amp;seltag=1&amp;adsrv=0&amp;cmp=DV726174&amp;ctx=24835144&amp;sadv=1&amp;ord=1&amp;litm=81689&amp;scrt=279288&amp;splc=7960&amp;adu=1991&amp;unit=300x601&amp;btreg=81689279288&amp;btadsrv=81689279288"
      async=""
    ></script>
    <iframe
      id="iframe_7869477969"
      name="iframe_7869477969"
      data-dv-frm="7869477969"
      width="0"
      height="0"
      src="about:blank"
      style="display: none"
    ></iframe>
    <script
      src="https://cdn.doubleverify.com/dvtp_src.js#dvp_scripthash=1&amp;t2te=0&amp;seltag=1&amp;adsrv=0&amp;cmp=DV726174&amp;ctx=24835144&amp;sadv=1&amp;ord=1&amp;litm=81099&amp;scrt=277428&amp;splc=7952&amp;adu=1991&amp;unit=300x601&amp;btreg=81099277428&amp;btadsrv=81099277428"
      async=""
    ></script>
    <iframe
      id="iframe_869046826814"
      name="iframe_869046826814"
      data-dv-frm="869046826814"
      width="0"
      height="0"
      src="about:blank"
      style="display: none"
    ></iframe>
    <script
      src="https://cdn.doubleverify.com/dvtp_src.js#dvp_scripthash=1&amp;t2te=0&amp;seltag=1&amp;adsrv=0&amp;cmp=DV726174&amp;ctx=24835144&amp;sadv=1&amp;ord=1&amp;litm=79881&amp;scrt=272294&amp;splc=7952&amp;adu=1991&amp;unit=300x601&amp;btreg=79881272294&amp;btadsrv=79881272294"
      async=""
    ></script>
  </body>
  <iframe
    name="goog_topics_frame"
    src="https://securepubads.g.doubleclick.net/static/topics/topics_frame.html"
    style="display: none"
  ></iframe>
</html>
