"use client";

import Image from "next/image";
import { useState, useEffect } from "react";

// Typdefinition für das Ergebnis der URL-Analyse (jetzt ein generisches Objekt)
type InspectionResult = Record<string, any> | null;

export default function Home() {
  // Zustand zum Speichern von Fensterinformationen der aktuellen Seite
  const [windowInfo, setWindowInfo] = useState({
    width: 0,
    height: 0,
    userAgent: "",
  });

  // Zustand für die URL-Analyse
  const [urlToInspect, setUrlToInspect] = useState("");
  const [inspectionResult, setInspectionResult] = useState<InspectionResult>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // useEffect, um auf das window-Objekt der aktuellen Seite zuzugreifen
  useEffect(() => {
    const updateWindowInfo = () => {
      setWindowInfo({
        width: window.innerWidth,
        height: window.innerHeight,
        userAgent: window.navigator.userAgent,
      });
    };
    updateWindowInfo();
    window.addEventListener("resize", updateWindowInfo);
    return () => window.removeEventListener("resize", updateWindowInfo);
  }, []);

  // Handler für das Absenden des Formulars
  const handleInspectUrl = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setInspectionResult(null);

    try {
      const response = await fetch("/api/inspect-url", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url: urlToInspect }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Ein unbekannter Fehler ist aufgetreten.");
      }

      setInspectionResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-8 row-start-2 items-center sm:items-start w-full max-w-4xl">
        <Image
          className="dark:invert"
          src="/next.svg"
          alt="Next.js logo"
          width={180}
          height={38}
          priority
        />
        <ol className="list-inside list-decimal text-sm text-center sm:text-left font-[family-name:var(--font-geist-mono)]">
          <li className="mb-2">
            Get started by editing{" "}
            <code className="bg-black/[.05] dark:bg-white/[.06] px-1 py-0.5 rounded font-semibold">
              app/page.tsx
            </code>
            .
          </li>
          <li>Save and see your changes instantly.</li>
        </ol>

        {/* Box für die Analyse externer URLs */}
        <div className="w-full p-4 border rounded-lg bg-black/[.05] dark:bg-white/[.06]">
          <h2 className="text-lg font-semibold mb-2">Externe URL analysieren</h2>
          <form onSubmit={handleInspectUrl} className="flex flex-col sm:flex-row gap-2 mb-4">
            <input
              type="url"
              value={urlToInspect}
              onChange={(e) => setUrlToInspect(e.target.value)}
              placeholder="https://www.google.com"
              required
              className="flex-grow p-2 border rounded bg-white dark:bg-black/[.2] dark:border-white/[.145] focus:ring-2 focus:ring-blue-500 outline-none"
            />
            <button
              type="submit"
              disabled={isLoading}
              className="rounded-md bg-blue-600 text-white px-4 py-2 h-10 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isLoading ? "Analysiere..." : "Analysieren"}
            </button>
          </form>

          {error && <p className="text-red-500 text-sm">Fehler: {error}</p>}

          {inspectionResult && (
            <div>
              <h3 className="font-semibold mb-2 mt-4">Ergebnis der Analyse (QMN-Objekt):</h3>
              <pre className="bg-white dark:bg-black p-4 rounded text-xs overflow-auto max-h-[60vh] border dark:border-white/[.145]">
                <code>
                  {JSON.stringify(inspectionResult, null, 2)}
                </code>
              </pre>
            </div>
          )}
        </div>

        {/* Box für lokale Fensterinformationen */}
        <div className="w-full p-4 border rounded-lg bg-black/[.05] dark:bg-white/[.06]">
          <h2 className="text-lg font-semibold mb-2">Fenster-Informationen (Diese Seite)</h2>
          <div className="font-[family-name:var(--font-geist-mono)] text-sm space-y-1">
            <p>Breite: <span className="font-bold">{windowInfo.width}px</span></p>
            <p>Höhe: <span className="font-bold">{windowInfo.height}px</span></p>
            <p>User Agent: <span className="font-bold break-all">{windowInfo.userAgent}</span></p>
          </div>
        </div>

        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <a
            className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5"
            href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Image
              className="dark:invert"
              src="/vercel.svg"
              alt="Vercel logomark"
              width={20}
              height={20}
            />
            Deploy now
          </a>
          <a
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:min-w-44"
            href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            Read our docs
          </a>
        </div>
      </main>
      <footer className="row-start-3 flex gap-6 flex-wrap items-center justify-center">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}
