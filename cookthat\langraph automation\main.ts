import {
  AIMessage,
  BaseMessage,
  SystemMessage,
} from "@langchain/core/messages";
import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { DynamicTool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import  z  from "zod";
import { downloadTikTokVideo } from "../tiktok-download-video.js";
import { ttdl } from 'btch-downloader';
import fs from "fs";
import path from "path";
import axios from "axios";
import { XMLOutputParser } from "@langchain/core/output_parsers";

const systemPrompt =
`
You are an expert culinary assistant. 
You will receive an audio track from a cooking video (e.g., TikTok) and the video description. 
Extract a clean, structured recipe from both the audio narration and the video description, as ingredients and steps may be mentioned in either source. 
If there is insufficient info, make minimal reasonable assumptions but mark unknowns as null.
`;

const IngredientSchema = z.object({
  quantity: z.number().positive(),
  unit: z.string().min(1),
  name: z.string().min(1),
});

const StepSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
});

//TODO improve metadata
const MetadataSchema = z.object({
  audio_helpful: z.boolean().optional(),
  video_type: z.enum(["cinematic", "vlog", "tutorial", "other"]).optional(),
});

const RecipeSchema = z.object({
  title: z.string().min(1).max(200),
  category: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  cooking_time_minutes: z.number().int().positive().nullable().optional(),
  servings: z.number().int().positive().nullable().optional(),
  ingredients: z.array(IngredientSchema).default([]),
  steps: z.array(StepSchema).default([]),
  metadata: MetadataSchema.optional(),
});

interface Input {
  url: string;
  Language?: string;
}

interface VideoInfo {
  videoPath: string;
  videoDescription: string;
}

type Output = z.infer<typeof RecipeSchema>;


const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [new SystemMessage(systemPrompt)],
  }),
  input: Annotation<Input>(),
  videoInfo: Annotation<VideoInfo>(),
  output: Annotation<Output>(),
});

export class Agent {
  model: ChatGoogleGenerativeAI;
  State: typeof AgentState;
  tools: DynamicTool[];

  constructor({
    model,
    State,
    tools,
  }: {
    model: ChatGoogleGenerativeAI;
    State: typeof AgentState;
    tools: DynamicTool[];
  }) {
    this.model = model;
    this.State = State;
    this.tools = tools;
  }

  compile() {
    const toolNode = new ToolNode(this.tools);

    const shouldContinue = (state: typeof AgentState.State) => {
      const lastMessage = state.messages[state.messages.length - 1];

      if (lastMessage && !(lastMessage as AIMessage).tool_calls?.length) {
        return END;
      }

      return "action";
    };

    const agentNode = async (state: typeof AgentState.State) => {
      const messages = state.messages;
      console.log(messages);

      try {
        const response = await this.model.invoke(messages);
        return { messages: [response] };
      } catch (err) {
        console.log(err);
        return { messages: [new AIMessage("error")] };
      }
    };

const fallbackDownloadVideo = async (url: string): Promise<VideoInfo> => {
  let videoPath = "";
  let videoDescription = "";

  console.log("Attempting fallback download method...");
  const btchResult = await ttdl(url);

  if (btchResult && btchResult.video && btchResult.video.length > 0) {
    const videoUrl = btchResult.video[0];
    videoDescription = btchResult.title || "";

    // Download the video from the URL
    const outDir = path.resolve("cookthat", "downloads");
    await fs.promises.mkdir(outDir, { recursive: true });

    const fileName = `tiktok_video_fallback_${Date.now()}.mp4`;
    videoPath = path.join(outDir, fileName);

    const response = await axios.get(videoUrl, { responseType: "stream" });
    await new Promise<void>((resolve, reject) => {
      const writer = fs.createWriteStream(videoPath);
      response.data.pipe(writer);
      writer.on("finish", () => resolve());
      writer.on("error", reject);
    });

    console.log("Fallback video downloaded successfully:", videoPath);
    return { videoPath, videoDescription };
  } else {
    throw new Error("No video URL found in btch-downloader result");
  }
};

    const getContentNode = async (state: typeof AgentState.State) => {
      const url = state.input.url;
      let videoPath = "";
      let videoDescription = "";

      try {
        // Primary method: Use downloadTikTokVideo
        console.log("Attempting to download video using primary method...");
        const result = await downloadTikTokVideo(url);
        videoPath = result.videoPath;
        videoDescription = result.description || "";
        console.log("Video downloaded successfully:", videoPath);
      } catch (primaryError) {
        const primaryErrorMsg = primaryError instanceof Error ? primaryError.message : String(primaryError);
        console.log("Primary download method failed:", primaryErrorMsg);

        try {
          const fallbackResult = await fallbackDownloadVideo(url);
          videoPath = fallbackResult.videoPath;
          videoDescription = fallbackResult.videoDescription;
        } catch (fallbackError) {
          const fallbackErrorMsg = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
          console.error("Both download methods failed:");
          console.error("Primary error:", primaryErrorMsg);
          console.error("Fallback error:", fallbackErrorMsg);
          throw new Error(`Failed to download video: ${primaryErrorMsg}`);
        }
      }

      return { videoPath, videoDescription };
    };

    const PrepareRequestNode = async (state: typeof AgentState.State) => {
      const videoPath = state.videoInfo.videoPath;
      const videoDescription = state.videoInfo.videoDescription;
      
      /*
      TODO 
      Prepare request to Gemini for video like in the gemini-extract-from-video.js file.
      make sure to respest Langchain/Langgraph utilities.

      add the human message into the messages of the state as expected when using langgraph
      */

      return { videoPath, videoDescription };
    };

    const workflow = new StateGraph(this.State)
      .addNode("getContent", getContentNode)
      .addNode("agent", agentNode)
      .addNode("action", toolNode)
      .addEdge(START, "getContent")
      .addEdge("getContent", "agent")
      .addEdge("action", "agent")
      .addConditionalEdges("agent", shouldContinue);

    const agent = workflow.compile();

    return agent;
  }
}
