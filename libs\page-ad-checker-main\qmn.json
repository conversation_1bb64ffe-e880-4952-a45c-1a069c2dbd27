{"displayMode": "all", "desktop_trackingpixel": "2398", "mobile_trackingpixel": "2395", "desktop_billboard": "2396", "desktop_billboard_insertion": "before-html", "desktop_billboard_html_selector": "body.home h4, Body.post-template-default p:nth-of-type(4), body.page-template-default h3:nth-of-type(3)", "desktop_billboard_position": "20", "desktop_billboard_html_exclusion": "", "desktop_brochuread": "2397", "desktop_brochuread_insertion": "before-html", "desktop_brochuread_html_selector": "body.page-template-default h3:last-of-type, Body.post-template-default .wp-block-jetpack-subscriptions + *, .wp-block-pullquote, .entry-content + *", "desktop_brochuread_html_exclusion": "", "desktop_brochuread_position": "80", "mobile_hpa_top": "2394", "mobile_hpa_top_insertion": "before-html", "mobile_hpa_top_html_selector": "body.search article:nth-of-type(2), Body.home h4, Body.post-template-default p:nth-of-type(4), .embed-instagram, #instagram-embed-0, .wp-block-embed.is-type-rich.is-provider-instagram.wp-block-embed-instagram, .instagram-media.instagram-media-rendered, h3:nth-of-type(4), h3, p:nth-of-t", "mobile_hpa_top_html_exclusion": "", "mobile_hpa_top_position": "30", "mobile_hpa_bottom": "2392", "mobile_hpa_bottom_insertion": "before-html", "mobile_hpa_bottom_html_selector": "body.search article:last-of-type, Body.home h4:nth-of-type(2), Body.post-template-default p:nth-of-type(10), hr:has(+.tiled-gallery.type-rectangular), .wp-block-separator.has-alpha-channel-opacity, .wp-block-jetpack-subscriptions__supports-newline.wp-block-jetpack-subscriptions, h3:nth-of-type(13), h3:nth-of-type(7), h3:nth-of-type(4)", "mobile_hpa_bottom_html_exclusion": "", "mobile_hpa_bottom_position": "60", "mobile_hpa_3": "", "mobile_hpa_3_insertion": "after-html", "mobile_hpa_3_html_selector": "", "mobile_hpa_3_html_exclusion": "", "mobile_brochuread": "2393", "mobile_brochuread_insertion": "before-html", "mobile_brochuread_html_selector": "body.search footer, body.home .wp-block-embed.is-type-rich, main h2:last-of-type, .wp-block-pullquote", "mobile_brochuread_html_exclusion": "", "mobile_brochuread_position": "90", "mobile_interstitial": "", "watchbetter_position": "30", "watchbetter_insertion": "before-html", "watchbetter_html_selector": "Body.home p:nth-of-type(4), p:has(+.instagram-gallery-feed), blockquote + *, main .instagram-gallery-list.instagram-gallery-square, h3:nth-of-type(7), h3:nth-of-type(6), h3:nth-of-type(2)", "watchbetter_html_exclusion": "", "mobile_watchbetter_insertion": "before-html", "mobile_watchbetter_html_selector": "Body.home p:nth-of-type(4), p:has(+.instagram-gallery-feed), body.post-template-default .instagram-media, h3:nth-of-type(7), h3:nth-of-type(6), h3:nth-of-type(2)", "mobile_watchbetter_html_exclusion": "", "header_sticky_selector": "header", "footer_selector": "footer", "main_content_selector": "main", "scroll_to_top_selector": "", "sitebar_selector": "aside", "sitebar_right_offset": "0", "is_left_sitebar": "", "sitebar_reposition": "", "display_below_selector": "", "sitebar_max_width": "", "is_sitebar_dynamic_placement": "", "adsLoaded": true}