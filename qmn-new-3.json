{"config": {"displayMode": "development", "excludedPaths": [], "options": {"adLabel": {"enabled": false, "text": "Anzeige", "style": {"color": "#757575", "fontSize": "12px", "textAlign": "left", "padding": "5px 5px 2px 5px"}}}, "adSlots": [{"id": "4845", "type": "tracker", "width": "1", "height": "1", "responsive": "desktop"}, {"id": "4843", "type": "intext", "width": "800", "height": "250", "responsive": "desktop", "lazyload": true, "position": {"htmlSelectors": "#lastnews_pc, p:nth-of-type(3), #news_l .lastnews:nth-of-type(6), #comments, #news_l #lastnews:nth-of-type(6), #produktliste, .letters", "htmlPosition": "before", "htmlExclusionSelectors": "#fcontent, #news_head"}, "css": {}}, {"id": "4852", "type": "tracker", "width": "1", "height": "1", "responsive": "mobile"}, {"id": "4851", "type": "hpa", "width": "300", "height": "600", "responsive": "mobile", "lazyload": false, "position": {"htmlSelectors": "#lastvideos_one, #lastnews_mobile .lastnews:nth-of-type(8)", "htmlPosition": "before"}, "css": {}}, {"id": "4849", "type": "hpa", "width": "300", "height": "600", "responsive": "mobile", "lazyload": false, "position": {"htmlSelectors": "#lastvideos, #news_text p:nth-of-type(3), #content_r, #commentlink, #news_l .lastnews:nth-of-type(15)", "htmlPosition": "before"}, "css": {}}, {"id": "4847", "type": "sitebar", "width": "300", "height": "600", "responsive": "desktop", "lazyload": false, "css": {}, "options": {"header_sticky_selector": "header", "footer_selector": "footer", "main_content_selector": "main", "is_left_sitebar": "0", "sitebar_max_width": "600", "is_sitebar_dynamic_placement": "0"}}, {"id": "4848", "type": "floorad", "width": "300", "height": "600", "responsive": "mobile", "css": {}}], "watchbetter": {"players": [{"playerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responsive": "desktop", "position": {"htmlPosition": "before"}, "css": {}}, {"playerType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responsive": "mobile", "position": {"htmlPosition": "before"}, "css": {}}]}}, "updateConfig": {}, "deviceType": "desktop", "header_sticky_selector": "header", "footer_selector": "footer", "main_content_selector": "main", "is_left_sitebar": "0", "sitebar_max_width": "600", "is_sitebar_dynamic_placement": "0", "adsLoaded": true}