(function () {
    function qmnAd(qmnPID, qmnWidth, qmnHeight, device) {
        if (device == 'mobile' && window.innerWidth > 480
            || device == 'desktop' && window.innerWidth <= 480) {
            console.log("%c QMN ", "background-color: Red; color: white; font-weight: bold; padding:2px; ", "Ad not loaded: "+qmnPID, device);
            document.getElementById('qmn'+qmnPID).remove();
            return;
        }
        console.log("%c QMN ", "background-color: Red; color: white; font-weight: bold; padding:2px; ", "Create Ad: "+qmnPID, device);

        const qmnnewINS = document.createElement("ins");
        qmnnewINS.className ="asm_async_creative";
        qmnnewINS.style.display = "inline-block";
        qmnnewINS.style.width = qmnWidth+"px";
        qmnnewINS.style.height = qmnHeight+"px";
        qmnnewINS.style.textAlign = "left";
        qmnnewINS.style.textDecoration = "none";
        qmnnewINS.setAttribute("data-asm-cdn", "cdn.f11-ads.com");
        qmnnewINS.setAttribute("data-asm-host", "ads.qualitymedianetwork.de");
        qmnnewINS.setAttribute("data-asm-fetch-gdpr", "1");
        qmnnewINS.setAttribute("data-asm-params", "pid="+qmnPID);

        document.getElementById('qmn'+qmnPID).appendChild(qmnnewINS);
    }

    function moveElementByHTMLSelector(element, selector, beforeOrAfter, exclusionSelector) {
        // check if selector is empty
        if (!selector) {
            return;
        }

        // split the selector by comma and iterate over all selectors until one is found
        // otherwise we can not guarantee that we find the element in the correct order of the selectors
        var selectors = selector.split(',').map(s => s.trim());

        var exclusions = exclusionSelector ? document.querySelectorAll(exclusionSelector) : [];

        for (let i = 0; i < selectors.length; i++) {
            // skip for empty selector
            if (!selectors[i]) {
                continue;
            }
            const targetElements = document.querySelectorAll(selectors[i]);
            for (const targetElement of targetElements) {
                // if element is child element in exclusions then continue with next element
                let isExcluded = false;
                for (const exclusion of exclusions) {
                    if (exclusion.contains(targetElement)) {
                        isExcluded = true;
                        break;
                    }
                }
                if (isExcluded) {
                    continue;
                }

                if (targetElement) {
                    if (beforeOrAfter === 'before') {
                        targetElement.parentNode.insertBefore(element, targetElement);
                    } else {
                        targetElement.parentNode.insertBefore(element, targetElement.nextSibling);
                    }
                    return;
                }

            }
        }
    }

    function moveElementByPercent(element, percent) {
        let parentNodeRect = element.parentNode.getBoundingClientRect();
        let parentHeight = parentNodeRect.height + parseInt(window.getComputedStyle(element.parentNode).marginTop, 10) + parseInt(window.getComputedStyle(element.parentNode).marginBottom, 10);

        // calculate the ideal y position
        let idealYPosition = (parentHeight * percent) / 100;

        // iterate over all children of parent and get the y position of the current element
        // if the y position is greater than the idealYPosition, insert the element after the current element
        let y = 0;
        let previousElement = null;
        for (let j = 0; j < element.parentNode.childNodes.length; j++) {
            let child = element.parentNode.childNodes[j];

            if (child.clientHeight) {
                var currentComputedStyle = window.getComputedStyle(child);
                y += child.clientHeight;
                y += parseInt(currentComputedStyle.marginTop, 10);
                y += parseInt(currentComputedStyle.marginBottom, 10);
                if(y > idealYPosition) {
                    // check if the start or the end of the element is closer to the idealYPosition
                    if (previousElement && y - idealYPosition > idealYPosition - (y - child.clientHeight - parseInt(currentComputedStyle.marginTop, 10) - parseInt(currentComputedStyle.marginBottom, 10))) {
                        insertAfter(previousElement, element);
                    } else {
                        insertAfter(child, element);
                    }
                    break;
                }
                previousElement = child;
            }
        }
    }

    function insertAfter(referenceNode, newNode) {
        referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling);
    }

    function contentpassAuthenticated() {
        return new Promise((resolve, reject) => {
            if (typeof window.cp !== 'object' || typeof window.cp.authenticate !== 'function') {
                reject(new Error('Contentpass function is not defined'));
                return;
            }

            window.cp('authenticate', function (error, user) {
                if (error) {
                    reject(error);
                    return;
                }

                if (!user || typeof user.isLoggedIn !== 'function' || typeof user.hasValidSubscription !== 'function') {
                    reject(new Error('Invalid user object'));
                    return;
                }

                if (user.isLoggedIn() && user.hasValidSubscription()) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        });
    }

    function resetPlacements() {
        let qmnDivs = document.querySelectorAll('[id^="qmn"]');
        const qmnDivsArray = Array.from(qmnDivs).filter(element => /^qmn(?:\d+|Watchbetter|WatchbetterShortcode)$/.test(element.id));
        for (let i = 0; i < qmnDivsArray.length; i++) {
            qmnDivsArray[i].innerHTML = '';
            // reset the adsLoaded flag
            window.qmn.adsLoaded = false;
        }
    }

    async function loadQMNAds(tcData, success) {
        if (!success || !tcData.gdprApplies) {
            return;
        }

        if (
            tcData.eventStatus !== "useractioncomplete" &&
            tcData.eventStatus !== "tcloaded"
        ) {
            return;
        }

        // check if the purpose.consents 1 to 10 are given
        for (let i = 1; i <= 10; i++) {
            if (!tcData.purpose.consents[i]) {
                resetPlacements();
                return;
            }
        }

        // check if the vendors (Factor Eleven GmbH, Quality Media Network) are given
        const vendorConsents = [795,835];
        for (let i = 0; i < vendorConsents.length; i++) {
            if (!tcData.vendor.consents[vendorConsents[i]]) {
                resetPlacements();
                return;
            }
        }

        // Query third-party callback.
        // If the global function window.qmnShouldDisplayAds is defined, call it asynchronously.
        // The callback can synchronously return a boolean value or a Promise that resolves to a boolean value.
        if (typeof window.qmnShouldDisplayAds === 'function') {
            const shouldDisplay = await window.qmnShouldDisplayAds();
            if (!shouldDisplay) {
                resetPlacements();
                return;
            }
        }

        // check if the user is authenticated with Contentpass
        if (typeof window.cp === 'object' && typeof window.cp.authenticate === 'function') {
            try {
                const isContentpassUserAuthenticated = await contentpassAuthenticated();
                if (isContentpassUserAuthenticated) {
                    resetPlacements();
                    return;
                }
            } catch (error) {   
                console.error(error);
            }
        }

        window.qmn = window.qmn || {};
        if (window.qmn.adsLoaded) return;
        window.qmn.adsLoaded = true;

        let qmnDivs = document.querySelectorAll('[id^="qmn"]');
        if (window.qmn && window.qmn.displayMode === 'pagetracker') {
            qmnDivs = document.querySelectorAll('[id="qmn' + window.qmn.desktop_trackingpixel + '"], [id="qmn' + window.qmn.mobile_trackingpixel + '"]');
        }

        const qmnDivsArray = Array.from(qmnDivs).filter(element => /^qmn(?:\d+|Watchbetter|WatchbetterShortcode)$/.test(element.id));

        // Remove duplicates
        const seenIds = {};
        const uniqueQmnDivs = qmnDivsArray.filter(el => {
            const id = el.id;
            if (seenIds[id]) {
                // remove the element from the DOM
                el.parentNode.removeChild(el);
                return false;
            } else {
                seenIds[id] = true;
                return true;
            }
        });

        for (let i = 0; i < uniqueQmnDivs.length; i++) {
            let qmnId = uniqueQmnDivs[i].id;
            let qmnBannerId = qmnId.replace('qmn', '');

            // get the count of the children of the parent, but ignore the qmn elements
            let childCount = 0;
            for (let j = 0; j < uniqueQmnDivs[i].parentNode.childNodes.length; j++) {
                let child = uniqueQmnDivs[i].parentNode.childNodes[j];
                if (child.id && child.id.startsWith('qmn')) {
                    continue;
                }
                childCount++;
            }

            // Billboard
            // if parent has more than 1 children, move the position of the billboard
            if (
                childCount > 1
                && parseInt(qmnBannerId, 10) === parseInt(window.qmn.desktop_billboard, 10)
                && window.qmn.desktop_billboard_insertion === 'percentage'
            ) {
                moveElementByPercent(uniqueQmnDivs[i], parseInt(window.qmn.desktop_billboard_position, 10));
            }
            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.desktop_billboard, 10)
                && (window.qmn.desktop_billboard_insertion === 'after-html' || window.qmn.desktop_billboard_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.desktop_billboard_html_selector, window.qmn.desktop_billboard_insertion === 'before-html' ? 'before' : 'after', window.qmn.desktop_billboard_html_exclusion);
            }

            // Prospekt-Ad
            if (
                childCount > 1
                && parseInt(qmnBannerId, 10) === parseInt(window.qmn.desktop_brochuread, 10)
                && (window.qmn.desktop_brochuread_insertion === 'percentage')
            ) {
                moveElementByPercent(uniqueQmnDivs[i], parseInt(window.qmn.desktop_brochuread_position, 10));
            }
            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.desktop_brochuread, 10)
                && (window.qmn.desktop_brochuread_insertion === 'after-html' || window.qmn.desktop_brochuread_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.desktop_brochuread_html_selector, window.qmn.desktop_brochuread_insertion === 'before-html' ? 'before' : 'after', window.qmn.desktop_brochuread_html_exclusion);
            }

            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.desktop_hpa, 10)
                && (window.qmn.desktop_hpa_insertion === 'after-html' || window.qmn.desktop_hpa_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.desktop_hpa_html_selector, window.qmn.desktop_hpa_insertion === 'before-html' ? 'before' : 'after', window.qmn.desktop_hpa_html_exclusion);
            }

            // if parent has more than 5 children, move the banner down
            if (childCount > 8) {
                if (
                    parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_hpa_top, 10)
                    && (window.qmn.mobile_hpa_top_insertion === 'percentage')
                ) {
                    moveElementByPercent(uniqueQmnDivs[i], parseInt(window.qmn.mobile_hpa_top_position, 10));
                }
                if (
                    parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_hpa_bottom, 10)
                    && (window.qmn.mobile_hpa_bottom_insertion === 'percentage')
                ) {
                    moveElementByPercent(uniqueQmnDivs[i], parseInt(window.qmn.mobile_hpa_bottom_position, 10));
                }
                if (
                    parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_brochuread, 10)
                    && (window.qmn.mobile_brochuread_insertion === 'percentage')
                ) {
                    moveElementByPercent(uniqueQmnDivs[i], parseInt(window.qmn.mobile_brochuread_position, 10));
                }
            }
            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_hpa_top, 10)
                && (window.qmn.mobile_hpa_top_insertion === 'after-html' || window.qmn.mobile_hpa_top_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.mobile_hpa_top_html_selector, window.qmn.mobile_hpa_top_insertion === 'before-html' ? 'before' : 'after', window.qmn.mobile_hpa_top_html_exclusion);
            }
            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_hpa_bottom, 10)
                && (window.qmn.mobile_hpa_bottom_insertion === 'after-html' || window.qmn.mobile_hpa_bottom_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.mobile_hpa_bottom_html_selector, window.qmn.mobile_hpa_bottom_insertion === 'before-html' ? 'before' : 'after', window.qmn.mobile_hpa_bottom_html_exclusion);
            }
            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_hpa_3, 10)
                && (window.qmn.mobile_hpa_3_insertion === 'after-html' || window.qmn.mobile_hpa_3_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.mobile_hpa_3_html_selector, window.qmn.mobile_hpa_3_insertion === 'before-html' ? 'before' : 'after', window.qmn.mobile_hpa_3_html_exclusion);
            }
            if (
                parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_brochuread, 10)
                && (window.qmn.mobile_brochuread_insertion === 'after-html' || window.qmn.mobile_brochuread_insertion === 'before-html')
            ) {
                moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.mobile_brochuread_html_selector, window.qmn.mobile_brochuread_insertion === 'before-html' ? 'before' : 'after', window.qmn.mobile_brochuread_html_exclusion);
            }


            if (qmnBannerId === 'Watchbetter' || qmnBannerId === 'WatchbetterShortcode') {
                // Wenn ein Shortcode-Player vorhanden ist und wir den normalen Player verarbeiten, diesen Ã¼berspringen
                if (qmnBannerId === 'Watchbetter' && document.getElementById('qmnWatchbetterShortcode')) {
                    uniqueQmnDivs[i].innerHTML = '';
                    continue;
                }

                // PrÃ¼fe, ob es watchbetter-embed Elemente gibt, die sich auÃŸerhalb unserer Container befinden
                var allWatchbetterEmbeds = document.querySelectorAll('[id="watchbetter-embed"]');
                var embeddedOutsideOurContainers = false;

                for (let i = 0; i < allWatchbetterEmbeds.length; i++) {
                    var container = allWatchbetterEmbeds[i].closest('#qmnWatchbetter, #qmnWatchbetterShortcode');
                    if (!container) {
                        // Es gibt einen watchbetter-embed auÃŸerhalb unserer Container
                        embeddedOutsideOurContainers = true;
                        break;
                    }
                }

                if (embeddedOutsideOurContainers) {
                    // Wenn externe Einbettungen vorhanden sind, initialisieren wir nicht
                    uniqueQmnDivs[i].innerHTML = '';
                    continue;
                }

                // ZÃ¤hle, wie viele watchbetter-embed-Elemente in jedem Containertyp sind
                var shortcodeContainers = document.getElementById('qmnWatchbetterShortcode') ?
                    document.getElementById('qmnWatchbetterShortcode').querySelectorAll('[id="watchbetter-embed"]').length : 0;

                var normalContainers = document.getElementById('qmnWatchbetter') ?
                    document.getElementById('qmnWatchbetter').querySelectorAll('[id="watchbetter-embed"]').length : 0;

                // Duplikate nur in dem aktuellen Container-Typ entfernen
                if ((qmnBannerId === 'WatchbetterShortcode' && shortcodeContainers > 1) ||
                    (qmnBannerId === 'Watchbetter' && normalContainers > 1)) {
                    uniqueQmnDivs[i].innerHTML = '';
                    continue;
                }


                if (qmnBannerId === 'Watchbetter') {
                    // Desktop
                    if (window.innerWidth > 480 && (window.qmn.watchbetter_insertion === 'after-html' || window.qmn.watchbetter_insertion === 'before-html')) {
                        moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.watchbetter_html_selector, window.qmn.watchbetter_insertion === 'before-html' ? 'before' : 'after', window.qmn.watchbetter_html_exclusion);
                    }
                    // Mobile
                    if (window.innerWidth <= 480 && (window.qmn.watchbetter_insertion === 'after-html' || window.qmn.watchbetter_insertion === 'before-html')) {
                        moveElementByHTMLSelector(uniqueQmnDivs[i], window.qmn.mobile_watchbetter_html_selector, window.qmn.mobile_watchbetter_insertion === 'before-html' ? 'before' : 'after', window.qmn.mobile_watchbetter_html_exclusion);
                    }

                    if (window.innerWidth > 480 && window.qmn.watchbetter_insertion === 'percentage') {
                        moveElementByPercent(uniqueQmnDivs[i], parseInt(window.qmn.watchbetter_position, 10));
                    }
                }

                // load the watchbetter script
                const watchbetterScript = document.createElement("script");
                watchbetterScript.src = uniqueQmnDivs[i].dataset.src;
                if (uniqueQmnDivs[i].dataset.cta) {
                    watchbetterScript.dataset.cta = uniqueQmnDivs[i].dataset.cta;
                }
                watchbetterScript.async;
                watchbetterScript.defer;
                watchbetterScript.type = "text/javascript";
                // append the script to the watchbetter container
                uniqueQmnDivs[i].appendChild(watchbetterScript);
                continue;
            }

            // if qmnBannerId is window.qmn.mobile_interstitial then only load the ad if it is the second view in the user session
            if (parseInt(qmnBannerId, 10) === parseInt(window.qmn.mobile_interstitial, 10)) {
                // check user session storage or local storage if it is the second page view
                let pageViews = 0;
                if (window.sessionStorage) {
                    pageViews = window.sessionStorage.getItem('qmn_page_views');
                } else {
                    pageViews = window.localStorage.getItem('qmn_page_views');
                }

                if (!pageViews) {
                    pageViews = 0;

                }
                pageViews = parseInt(pageViews, 10) + 1;

                if (window.sessionStorage) {
                    window.sessionStorage.setItem('qmn_page_views', pageViews);
                }
                if (window.localStorage) {
                    window.localStorage.setItem('qmn_page_views', pageViews);
                }

                // show on the second page view and every third page view, but not on the first third page view
                if (pageViews < 2 || pageViews === 3 || (pageViews >= 3 && pageViews % 3 !== 0)) {
                    continue;
                }
            }

            // lazy load the following ads using the IntersectionObserver API
            const lazyLoadAds = [
                window.qmn.desktop_billboard,
                window.qmn.desktop_brochuread,
                window.qmn.mobile_hpa_top,
                window.qmn.mobile_hpa_bottom,
                window.qmn.mobile_hpa_3,
                window.qmn.mobile_brochuread,
            ];
            if (lazyLoadAds.includes(`${qmnBannerId}`)) {
                const observer = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            qmnAd(qmnBannerId, uniqueQmnDivs[i].dataset.width, uniqueQmnDivs[i].dataset.height, uniqueQmnDivs[i].dataset.responsive);
                            if (typeof (window.asm_async_data) == 'object') {
                                window.asm_async_data.initi();
                            }
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '300px'
                });
                observer.observe(uniqueQmnDivs[i]);
            } else {
                qmnAd(qmnBannerId, uniqueQmnDivs[i].dataset.width, uniqueQmnDivs[i].dataset.height, uniqueQmnDivs[i].dataset.responsive);
            }
        }

        // load the adserver script to initialize adslots
        const qmnnewScript = document.createElement("script");
        qmnnewScript.src = "https://cdn.f11-ads.com/adasync.min.js";
        qmnnewScript.async;
        qmnnewScript.type = "text/javascript";
        qmnnewScript.onerror = function() {
            // remove all ads
            for (let i = 0; i < uniqueQmnDivs.length; i++) {
                // exclude the watchbetter container
                if (uniqueQmnDivs[i].id === 'qmnWatchbetter') {
                    continue;
                }
                uniqueQmnDivs[i].innerHTML = '';
            }
            // reset the adsLoaded flag
            window.qmn.adsLoaded = false;
        };
        document.body.appendChild(qmnnewScript);
    }

    // watch for __tcfapi and then add the event listener
    // https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework/blob/b7d34a11326aeabbf8045b9a99dd111d03e4716a/TCFv2/IAB%20Tech%20Lab%20-%20CMP%20API%20v2.md
    if ( typeof __tcfapi === 'function' ) {
        __tcfapi('addEventListener', 2, loadQMNAds);
    } else {
        // if __tcfapi is not available try again in 3000ms and repeat this process 5 times
        let tries = 0;
        let interval = setInterval(function() {
            if (typeof __tcfapi === 'function') {
                clearInterval(interval);
                __tcfapi('addEventListener', 2, loadQMNAds);
            } else {
                tries++;
                if (tries > 10) {
                    clearInterval(interval);
                    console.log("%c QMN ", "background-color: Red; color: white; font-weight: bold; padding:2px; ", "Consent Management Platform not found");
                }
            }
        }, 1000);
    }
})();