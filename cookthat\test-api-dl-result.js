import <PERSON><PERSON><PERSON> from "@tobyg74/tiktok-api-dl";
import fs from "fs";
import axios from "axios";

const url = "https://www.tiktok.com/@miina.vnr/video/7514988113119284502";

Tiktok.Downloader(url)
  .then(async (result) => {
    console.log(JSON.stringify(result, null, 2));
    /* console.log( JSON.stringify(result.result.video, null, 2)); */
    if (result.status === "success" && result.result) {
      // Download video
/*       if (result.result.video && result.result.video.playAddr && result.result.video.playAddr.length > 1) {
        const videoUrl = result.result.video.playAddr[1];
        const videoFileName = `tiktok_video_${Date.now()}.mp4`;
        const videoFilePath = `./${videoFileName}`; // Save in the current directory

        try {
          const videoResponse = await axios({
            method: 'get',
            url: videoUrl,
            responseType: 'stream'
          });

          videoResponse.data.pipe(fs.createWriteStream(videoFilePath));

          await new Promise((resolve, reject) => {
            videoResponse.data.on('end', () => {
              console.log(`Video downloaded successfully to ${videoFilePath}`);
              resolve();
            });
            videoResponse.data.on('error', (err) => {
              console.error('Error downloading video:', err);
              reject(err);
            });
          });
        } catch (error) {
          console.error("Error fetching the video:", error);
        }
      } else {
        console.log("Video URL not found in the result.");
      } */

      // Download audio
      if (result.result.music && result.result.music.playUrl && result.result.music.playUrl.length > 0) {
        const audioUrl = result.result.music.playUrl[0]; // Assuming the first URL is the primary one
        const audioFileName = `tiktok_audio_${Date.now()}.mp3`; // Assuming MP3 format
        const audioFilePath = `./${audioFileName}`;

        try {
          const audioResponse = await axios({
            method: 'get',
            url: audioUrl,
            responseType: 'stream'
          });

          audioResponse.data.pipe(fs.createWriteStream(audioFilePath));

          await new Promise((resolve, reject) => {
            audioResponse.data.on('end', () => {
              console.log(`Audio downloaded successfully to ${audioFilePath}`);
              resolve();
            });
            audioResponse.data.on('error', (err) => {
              console.error('Error downloading audio:', err);
              reject(err);
            });
          });
        } catch (error) {
          console.error("Error fetching the audio:", error);
        }
      } else {
        console.log("Audio URL not found in the result.");
      }
    } else {
      console.log("No result or status not success.");
    }
  })
  .catch((error) => {
    console.error("Error during TikTok download:", error);
  });