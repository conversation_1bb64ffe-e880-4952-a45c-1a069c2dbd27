import Tik<PERSON> from "@tobyg74/tiktok-api-dl";
import fs from "fs";
import path from "path";
import axios from "axios";

/**
 * Download TikTok full video for a given TikTok URL.
 * Returns { videoPath, description }
 *
 * - Chooses result.result.video.playAddr[1] if available, otherwise first
 * - Saves to ./cookthat/downloads
 */
export async function downloadTikTokVideo(tiktokUrl, opts = {}) {
  if (!tiktokUrl) throw new Error("Missing TikTok URL");
  const outDir = opts.outputDir || path.resolve("cookthat", "downloads");
  await fs.promises.mkdir(outDir, { recursive: true });

  const result = await Tiktok.Downloader(tiktokUrl);
  if (result?.status !== "success" || !result?.result) {
    throw new Error("TikTok API DL did not return success");
  }

  const description = result?.result?.desc ?? "";

  const playAddr = result?.result?.video?.playAddr || [];
  if (!Array.isArray(playAddr) || playAddr.length === 0) {
    throw new Error("No video URL found for this TikTok");
  }
  const videoUrl = playAddr[1] || playAddr[0];

  const fileName = `tiktok_video_${Date.now()}.mp4`;
  const videoPath = path.join(outDir, fileName);

  const response = await axios.get(videoUrl, { responseType: "stream" });
  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(videoPath);
    response.data.pipe(writer);
    writer.on("finish", resolve);
    writer.on("error", reject);
  });

  return { videoPath, description };
}

// Direct run helper
if (import.meta.url === `file://${process.argv[1]}`) {
  const url = process.argv[2];
  if (!url) {
    console.error("Usage: node cookthat/tiktok-download-video.js <tiktokUrl>");
    process.exit(1);
  }
  downloadTikTokVideo(url)
    .then(({ videoPath, description }) => {
      console.log("Video saved:", videoPath);
      if (description) console.log("Description:", description);
    })
    .catch((e) => {
      console.error("Download failed:", e?.message || e);
      process.exit(1);
    });
}

