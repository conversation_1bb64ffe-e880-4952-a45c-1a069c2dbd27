import { OpenAIWhisperAudio } from "@langchain/community/document_loaders/fs/openai_whisper_audio";
import dotenv from 'dotenv';
dotenv.config();

const filePath = "./tiktok_audio_1755086527367.mp3";

console.log(process.env.OPENAI_API_KEY)

if (!process.env.OPENAI_API_KEY) {
  console.log("OPENAI_API_KEY is not set")
}


const loader = new OpenAIWhisperAudio(filePath, {
  clientOptions: {
    apiKey: process.env.OPENAI_API_KEY,
  },
  transcriptionCreateParams: {
    language: "en",
  },
});

const docs = await loader.load();

console.log(docs);