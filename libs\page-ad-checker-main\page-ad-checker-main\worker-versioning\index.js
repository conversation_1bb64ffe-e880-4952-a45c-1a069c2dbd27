// Test change to verify volume mounting is working

const { Worker } = require('bullmq');
const IORedis = require('ioredis');
const puppeteer = require('puppeteer');

const connection = new IORedis({
  host: 'redis',
  port: 6379,
  maxRetriesPerRequest: null,
});

async function getElementId(elementHandle) {
    if (elementHandle) {
        return await elementHandle.evaluate(el => el.id);
    }
    return null;
}

const calculateHeadingDistances = () => {
  // Selektiere alle relevanten Elemente in Erscheinungsreihenfolge
  const elements = Array.from(document.querySelectorAll("h1, h2, h3, h4, h5, h6, p"));

  // Extrahiere Position und Typinformationen
  const getElementDetails = el => {
    const rect = el.getBoundingClientRect();
    const scrollTop = window.scrollY || window.pageYOffset;
  
    return {
      tag: el.tagName,
      text: el.textContent.trim(),
      top: rect.top + scrollTop,
      bottom: rect.bottom + scrollTop,
      isHeading: el.tagName.startsWith('H')
    };
  };
  

  const elementDetails = elements.map(getElementDetails);

  // Sortiere Elemente nach vertikaler Position im Viewport
  elementDetails.sort((a, b) => a.top - b.top);

  const result = [];
  let currentHeading = null;
  let currentSectionElements = [];

  for (let i = 0; i < elementDetails.length; i++) {
    const el = elementDetails[i];

    if (el.isHeading) {
      if (currentHeading) {
        const sectionEnd = currentSectionElements.length > 0
          ? currentSectionElements[currentSectionElements.length - 1].bottom
          : currentHeading.bottom;

        const sectionHeight = sectionEnd - currentHeading.top;
        const distanceToNextHeading = el.top - currentHeading.bottom;

        result.push({
          heading: {
            tag: currentHeading.tag,
            text: currentHeading.text,
            position: currentHeading.top
          },
          elements: currentSectionElements,
          distanceToNextHeading,
          sectionHeight
        });
      }

      currentHeading = el;
      currentSectionElements = [];
    } else {
      currentSectionElements.push({
        tag: el.tag,
        text: el.text,
        position: el.top,
        bottom: el.bottom
      });
    }

    // Letztes Element: finalisiere die letzte Section
    const isLastElement = i === elementDetails.length - 1;
    if (isLastElement && currentHeading) {
      const sectionEnd = currentSectionElements.length > 0
        ? currentSectionElements[currentSectionElements.length - 1].bottom
        : currentHeading.bottom;

      const sectionHeight = sectionEnd - currentHeading.top;

      result.push({
        heading: {
          tag: currentHeading.tag,
          text: currentHeading.text,
          position: currentHeading.top
        },
        elements: currentSectionElements,
        distanceToNextHeading: null,
        sectionHeight
      });
    }
  }

  return result;
};

const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};

const isElementInViewportLogic = (el) => {
    if (!el) return false;
    const rect = el.getBoundingClientRect();
    const vHeight = window.innerHeight || document.documentElement.clientHeight;
    const vWidth = window.innerWidth || document.documentElement.clientWidth;
    
    // Prüft, ob irgendein Teil des Elements im Viewport sichtbar ist.
    const vertInView = rect.top < vHeight && rect.bottom > 0;
    const horInView = rect.left < vWidth && rect.right > 0;
    
    return vertInView && horInView;
};


const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN-Objekt oder qmn.adSlots ist nicht im erwarteten Format.');
    return {};
  }

  return qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type, position } = adSlot;

    // Anzeigen vom Typ 'tracker' und Slots ohne ID/Responsive ignorieren
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Sicherstellen, dass das Array für den Gerätetyp existiert
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Anzeigen-Objekt zur Liste hinzufügen, inklusive Position
    acc[responsive].push({ id, type, position });

    return acc;
  }, {});
};

const checkSelectors = async (page) => {
  const qmnData = await page.evaluate(() => {
    return window.qmn;
  });

  /* console.log( 'QMN Object:' ,JSON.stringify(qmnData, null, 2)) */

  if(!qmnData || !qmnData.config) {
    console.log('⚠️ window.qmn-Objekt oder window.qmn.config nicht auf der Seite gefunden.');
    return null;
  }

  return qmnData.config
}

const viewports = {
  mobile: { width: 375, height: 812, deviceType: 'mobile', name: 'Mobile' },
  tablet: { width: 768, height: 1024, deviceType: 'desktop', name: 'Tablet' },
  desktop: { width: 1920, height: 1080, deviceType: 'desktop', name: 'Desktop HD' },
};

// --- Logik für Layer 1: Placement Check (wird im Browser-Kontext ausgeführt) ---
const checkPlacementLogic = (ads) => {
    const missing = [];
    for (const ad of ads) {
        const adContainer = document.getElementById(`qmn${ad.id}`);
        if (!adContainer) {
            missing.push(ad);
        }
    }
    return missing;
};

// --- Logik für Layer 2: Positioning Check (wird im Browser-Kontext ausgeführt) ---
const checkPositioningLogic = (ads) => {
    const unpositionable = [];
    for (const ad of ads) {
        if (ad.position && ad.position.htmlSelectors) {
            const selector = ad.position.htmlSelectors;
            const isPlaceable = document.querySelectorAll(selector).length > 0;
            if (!isPlaceable) {
                unpositionable.push({
                    ...ad,
                    result: false
                });
            }
        }
    }
    return unpositionable;
};

// --- Logik für Layer 3: Visibility Check (wird im Browser-Kontext ausgeführt) ---
const checkVisibilityLogic = (selector) => {
    const elements = Array.from(document.querySelectorAll(selector));
    const results = [];
    const pageScrollHeight = document.body.scrollHeight;
    const pageScrollWidth = document.body.scrollWidth;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    console.log(  `element 🌳`, JSON.stringify(elements, null, 2))

    for (const el of elements) {

        const rect = el.getBoundingClientRect();
        
        const isRendered = rect.width > 0 && rect.height > 0;

        const scrollTop = window.scrollY || window.pageYOffset;
        const scrollLeft = window.scrollX || window.pageXOffset;
        const isWithinPageBounds = isRendered &&
            (rect.top + scrollTop) >= 0 &&
            (rect.left + scrollLeft) >= 0 &&
            (rect.bottom + scrollTop) <= pageScrollHeight &&
            (rect.right + scrollLeft) <= pageScrollWidth;

      
        console.log("result is within pagebound 🎦",isWithinPageBounds )

        let isVisibleByCss = false;
        if (isRendered) {
            if (typeof el.checkVisibility === 'function') {
                isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
            }
        }
        
        let isOccluded = false;
        if (isVisibleByCss) {
            const samplePoints = [
                { x: rect.left + 1, y: rect.top + 1 },
                { x: rect.left + rect.width / 2, y: rect.top + 1 },
                { x: rect.right - 1, y: rect.top + 1 },
                { x: rect.left + 1, y: rect.top + rect.height / 2 },
                { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 },
                { x: rect.right - 1, y: rect.top + rect.height / 2 },
                { x: rect.left + 1, y: rect.bottom - 1 },
                { x: rect.left + rect.width / 2, y: rect.bottom - 1 },
                { x: rect.right - 1, y: rect.bottom - 1 }
            ];

            console.log( `sample points 🈯:` ,  JSON.stringify(samplePoints, null, 2))

  
            const visibilityResults = samplePoints.map(point => {
                if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                    const elementAtPoint = document.elementFromPoint(point.x, point.y);
                    
                    // Log the found element for debugging purposes
                    if (elementAtPoint) {
                        const tag = elementAtPoint.tagName;
                        const id = elementAtPoint.id;
                        const classes = elementAtPoint.className;
                        console.log(`[Visibility Check] Point (${point.x.toFixed(0)}, ${point.y.toFixed(0)}): Found element: ${tag} ${id ? '#'+id : ''}`);
                    } else {
                        console.log(`[Visibility Check] Point (${point.x.toFixed(0)}, ${point.y.toFixed(0)}): Found no element.`);
                    }

                    // Das Element am Prüfpunkt muss entweder das Zielelement selbst oder ein Kindelement davon sein.
                    return elementAtPoint === el || el.contains(elementAtPoint);
                }


              console.log(" is not within viewport  ❌" )

                return false;
            });

            const visiblePoints = visibilityResults.filter(isVisible => isVisible).length;
            const visibilityPercentage = (visiblePoints / samplePoints.length) * 100;
            
            if (visibilityPercentage < 100) {
                isOccluded = true;
            }
        }

        const isTrulyVisible = isWithinPageBounds && isVisibleByCss && !isOccluded;
        results.push({ id: el.id, isWithinPageBounds, isVisibleByCss, isOccluded, isTrulyVisible });
    }
    return results;
};

/* async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`[${deviceName}] ✅ ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`[${deviceName}] 🔍 Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        // Construct the selector for the ad container based on ad.id
        // Example: #qmn12345
        const adContainerSelector = `#qmn${ad.id}`;
        console.log(`  [${deviceName}] 📦 Prüfe Ad Container: ${adContainerSelector}`);

        let iframeHandle = null;
        let iframeId = null;

        try {
            // Step 1: Wait for the ad container to be present on the page
            const containerHandle = await page.waitForSelector(adContainerSelector, { timeout: 10000 }); // Increased timeout for container
            
            if (!containerHandle) {
                const details = { isTrulyVisible: false, reason: "Ad container element not found (timeout)." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ❌ ${adContainerSelector}: Ad-Container nicht gefunden oder Timeout.`);
                continue;
            }

            // Step 2: Look for an iframe *inside* that specific ad container
            // We expect the iframe's ID to start with "adspiritflash" based on previous examples.
            iframeHandle = await containerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 10000 }); // Wait for iframe inside container

            if (!iframeHandle) {
                const details = { isTrulyVisible: false, reason: "Iframe element not found within specific ad container (timeout)." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ❌ ${adContainerSelector} iframe: Nicht gefunden (Timeout oder Strukturabweichung).`);
                await containerHandle.dispose(); // Clean up container handle
                continue;
            }

            // Step 3: Get the ID of the found iframe
            iframeId = await iframeHandle.evaluate(iframe => iframe.id);

            if (!iframeId) {
                const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`[${deviceName}]   - ⚠️ ${adContainerSelector} iframe: Gefunden, aber es hat keine ID.`);
                await iframeHandle.dispose();
                await containerHandle.dispose();
                continue;
            }

            console.log(`  [${deviceName}] 🆔 Gefundenes Iframe-ID für ${adContainerSelector}: ${iframeId}`);

            const adSelectorForVisibilityCheck = `#${iframeId}`;

            // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
            await iframeHandle.evaluate(el => el.scrollIntoView({ block: 'center', inline: 'center' }));
            // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
            await new Promise(resolve => setTimeout(resolve, 1000));

            // DEBUG: Überprüfen, ob das Element nach dem Scrollen im Viewport ist.
            const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
            console.log(`[${deviceName}]   - 🐛 DEBUG: Element ${adSelectorForVisibilityCheck} im Viewport nach Scroll: ${isElementInViewportForDebug}`);

            // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
            // Note: checkVisibilityLogic takes a selector, which is now the iframeId.
            const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelectorForVisibilityCheck);
            const result = visibilityResults[0]; // Assuming checkVisibilityLogic returns an array with one result for the given selector

            if (!result || !result.isTrulyVisible) {
                const details = result || { isTrulyVisible: false };
                notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
                const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
                console.log(`[${deviceName}]   - ❌ ${adSelectorForVisibilityCheck}: Nicht sichtbar. Grund: ${reason}`);
            } else {
                console.log(`[${deviceName}]   - ✅ ${adSelectorForVisibilityCheck}: Sichtbar.`);
            }

        } catch (error) {
            // Catch specific timeouts or other errors during iframe finding
            const details = { isTrulyVisible: false, reason: `Fehler beim Suchen des Iframes: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   - 🚨 ERROR beim Prüfen von ${adContainerSelector}: ${error.message}`);
        } finally {
            if (iframeHandle) {
                await iframeHandle.dispose(); // Clean up the iframe handle
            }
            // containerHandle would be disposed if it was assigned and caught an error before iframeHandle.
            // If it was successfully used, it will be implicitly cleaned up when page closes or when its scope ends.
            // For explicit cleanup after successful use:
            // if (containerHandle) await containerHandle.dispose();
        }
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] ❌ FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ✅ ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
} */

// --- Funktionen zur Ausführung der Layer-Prüfungen ---

async function runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unplacedAds = await page.evaluate(checkPlacementLogic, adsToCheck);
    if (unplacedAds.length > 0) {
        if (!issues.placement) issues.placement = { desktop: [], mobile: [] };
        issues.placement[responsiveKey] = unplacedAds;
        console.log(`[${deviceName}] FEHLER [Placement]: ${unplacedAds.length} Anzeigen-Container nicht im DOM gefunden:`, unplacedAds.map(ad => ad.id));
    } else {
        console.log(`[${deviceName}] ERFOLG [Placement]: Alle ${adsToCheck.length} Anzeigen-Container gefunden.`);
    }
    return unplacedAds.length;
}

async function runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unpositionableAds = await page.evaluate(checkPositioningLogic, adsToCheck);
    if (unpositionableAds.length > 0) {
        if (!issues.positioning) issues.positioning = { desktop: [], mobile: [] };
        issues.positioning[responsiveKey] = unpositionableAds;
        console.log(`[${deviceName}] FEHLER [Positioning]: ${unpositionableAds.length} Anzeigen nicht positionierbar (Ziel-Selektor nicht gefunden):`, unpositionableAds.map(ad => ({id: ad.id, selector: ad.position.htmlSelectors})));
    } else {
        console.log(`[${deviceName}] ERFOLG [Positioning]: Alle ${adsToCheck.length} Anzeigen sind positionierbar.`);
    }
    return unpositionableAds.length;
}

/* async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`[${deviceName}] Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        const adContainerSelector = `#qmn${ad.id}`;

        console.log ( `ad 🆔` , adContainerSelector )

        const iframeHandle = await page.$(`${adContainerSelector} iframe`);
  


        

        console.log( `iframe 🖼️` , JSON.stringify(iframeHandle, null, 2))

        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found within ad container" };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   -   ${adContainerSelector} iframe: Nicht gefunden.`);
            continue;
        }

        const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
        console.log( `iframe  ID 🖼️` , JSON.stringify(iframeId, null, 2))

        if (!iframeId) {
            const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   -   ${adContainerSelector} iframe: Gefunden, aber es hat keine ID.`);
            await iframeHandle.dispose();
            continue;
        }

        const adSelector = `#${iframeId}`;

        // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
        await iframeHandle.evaluate(el => el.scrollIntoView({ block: 'center', inline: 'center' }));
        // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
        await new Promise(resolve => setTimeout(resolve, 1000));

        // DEBUG: Überprüfen, ob das Element nach dem Scrollen im Viewport ist.
        const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
        console.log(`[${deviceName}]   - DEBUG: Element ${adSelector} im Viewport nach Scroll: ${isElementInViewportForDebug}`);

        // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0]; 

        if (!result || !result.isTrulyVisible) {
            const details = result || { isTrulyVisible: false };
            notVisibleAds.push({ id:ad.id, type: ad.type , visibilityDetails: details });
            const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
            console.log(`[${deviceName}]   - ❌ ${adSelector}: Nicht sichtbar. Grund: ${reason}`);
        } else {
            console.log(`[${deviceName}]   - ✅ ${adSelector}: Sichtbar.`);
        }
        
        await iframeHandle.dispose(); // Bereinige das Handle, um Speicherlecks zu vermeiden.
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
} */

/* async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`[${deviceName}] Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        // --- Primary Selector based on ad.id for the ad container ---
        // This assumes the ad container has an ID like `qmn<ad.id>`
        const adContainerSelector = `#qmn${ad.id}`;
        console.log(`[${deviceName}] ➡️ Prüfe Ad ID: ${ad.id} mit Container-Selektor: ${adContainerSelector}`);

        let iframeHandle = null;
        let parentContainerId = null; // To store the ID of the found parent container

        try {
            // Attempt to find the iframe directly within the expected ad container, waiting for it.
            // This is the most robust approach if the adContainerSelector is accurate.
            const containerHandle = await page.waitForSelector(adContainerSelector, { timeout: 10000 }); // Wait for the ad container itself
            if (containerHandle) {
                parentContainerId = await getElementId(containerHandle); // Get the ID of the found container
                // Now, wait for the iframe *within* that container
                iframeHandle = await containerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 10000 });
            }
        } catch (error) {
            console.log(`[${deviceName}] ⚠️ WARN: Iframe container (${adContainerSelector}) or iframe not found directly. Error: ${error.message}`);
            // If the primary selector fails, consider a more generic fallback.
            // This fallback mimics parts of the previous generic solution,
            // useful if the ad.id mapping isn't 100% consistent or if there are other ad types.
            try {
                // Fallback 1: Look for any <ins> element that starts with 'p' and contains an 'adspiritflash' iframe
                const genericInsElements = await page.$$('ins[id^="p"]');
                for (const ins of genericInsElements) {
                    const foundIframeInIns = await ins.$('iframe[id^="adspiritflash"]');
                    if (foundIframeInIns) {
                        const insId = await getElementId(ins);
                        const potentialIframeId = await getElementId(foundIframeInIns);

                        console.log(`id number ins ${insId} / Iframe Id ${potentialIframeId} `)

                        // Implement logic here to determine if this generic iframe is *the one* for the current ad.
                        // For example, by checking if its ID or content relates to ad.id
                        // For now, let's assume if we find any, it might be relevant, or you'd refine this.
                        // For simplicity, we'll just take the first generic one found if the specific one fails.
                        // A more robust solution might involve checking the iframe's src or content.
                        
                        // For now, if ad.id is '301579' and the iframe has 'adspiritflash6870544' but is in 'asmobj_301579',
                        // the initial direct selector is best. This generic fallback is for when the qmn<ad.id> is not found.
                        // Let's make this fallback smarter by looking for 'asmobj_' if it's typical.

                        const asmobjContainerSelector = `div[id*="asmobj_${ad.id}"]`;
                        const asmobjContainerHandle = await page.$(asmobjContainerSelector);
                        if (asmobjContainerHandle) {
                            parentContainerId = await getElementId(asmobjContainerHandle);
                            iframeHandle = await asmobjContainerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 5000 });
                            if (iframeHandle) {
                                console.log(`[${deviceName}] ✅ Found iframe via fallback (asmobj_ for ad.id ${ad.id}): ${parentContainerId}`);
                                break; // Found it, exit fallback loop
                            }
                        }
                    }
                }
            } catch (fallbackError) {
                console.log(`[${deviceName}] ❌ Fallback search failed for ad.id ${ad.id}. Error: ${fallbackError.message}`);
            }
        }

        // --- End Iframe Discovery Logic ---

        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found within ad container or via fallback." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   -   Ad ID ${ad.id}: Iframe not found.`);
            continue; // Move to the next ad
        }

        const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
        console.log(`[${deviceName}]   -   Iframe ID found for Ad ID ${ad.id}: ${iframeId}`);

        if (!iframeId) {
            const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   -   Ad ID ${ad.id}: Iframe found, but it has no ID.`);
            await iframeHandle.dispose();
            continue; // Move to the next ad
        }

        const adSelector = `#${iframeId}`;

        // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
        await iframeHandle.evaluate(el => el.scrollIntoView({ block: 'center', inline: 'center' }));
        // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
        await new Promise(resolve => setTimeout(resolve, 1000));

        // DEBUG: Überprüfen, ob das Element nach dem Scrollen im Viewport ist.
        const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
        console.log(`[${deviceName}]   - DEBUG: Element ${adSelector} im Viewport nach Scroll: ${isElementInViewportForDebug}`);

        // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0];

        if (!result || !result.isTrulyVisible) {
            const details = result || { isTrulyVisible: false };
            notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
            const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
            console.log(`[${deviceName}]   - ❌ ${adSelector}: Nicht sichtbar. Grund: ${reason}`);
        } else {
            console.log(`[${deviceName}]   - ✅ ${adSelector}: Sichtbar.`);
        }

        await iframeHandle.dispose(); // Bereinige das Handle, um Speicherlecks zu vermeiden.
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
} */

async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Keine Anzeigen für diesen Layer zu prüfen.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`[${deviceName}] Starte individuellen Visibility-Check für ${adsToCheck.length} Anzeigen...`);

    for (const ad of adsToCheck) {
        // --- NEW Iframe Discovery Logic based on your specification ---

        // 1. Define the selector for the parent div (e.g., #qmn123 where ad.id is 123)
        const qmnDivSelector = `div[id^="qmn"][id*="${ad.id}"]`;
        console.log(`[${deviceName}] ➡️ Prüfe Ad ID: ${ad.id}. Suche nach div: ${qmnDivSelector}`);

        let iframeHandle = null;
        let parentContainerId = null; // To store the ID of the div container
        let insContainerId = null;    // To store the ID of the ins container

        try {
            // Find all potential QMN divs on the page that match the ad.id
            const qmnDivHandles = await page.$$(qmnDivSelector);

            if (qmnDivHandles.length === 0) {
                console.log(`[${deviceName}] ⚠️ WARN: No div element matching "${qmnDivSelector}" found for ad.id ${ad.id}.`);
                // Fallback to the 'asmobj_' logic if the qmn div is not found,
                // as that was observed in tierchenwelt.de
                const asmobjContainerSelector = `div[id*="asmobj_${ad.id}"]`;
                const asmobjContainerHandle = await page.$(asmobjContainerSelector);
                if (asmobjContainerHandle) {
                    parentContainerId = await getElementId(asmobjContainerHandle);
                    iframeHandle = await asmobjContainerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 5000 });
                    if (iframeHandle) {
                        console.log(`[${deviceName}] ✅ Found iframe via 'asmobj_' fallback for ad.id ${ad.id}. Parent: ${parentContainerId}`);
                    }
                }

                if (!iframeHandle) { // If still no iframe after fallback
                     const details = { isTrulyVisible: false, reason: `No container div (${qmnDivSelector} or ${asmobjContainerSelector}) found for ad.id.` };
                     notVisibleAds.push({ ...ad, visibilityDetails: details });
                     console.log(`[${deviceName}]   -   Ad ID ${ad.id}: Container div not found.`);
                     continue; // Move to the next ad
                }
            } else {
                // Iterate through found QMN divs
                for (const qmnDiv of qmnDivHandles) {
                    parentContainerId = await getElementId(qmnDiv);
                    console.log(`[${deviceName}]   - Found QMN div: #${parentContainerId}. Searching for ins...`);

                    // 2. Within this div, find all 'ins' elements
                    const insHandles = await qmnDiv.$$('ins');

                    if (insHandles.length === 0) {
                        console.log(`[${deviceName}]     - No 'ins' element found within div #${parentContainerId}.`);
                        // === NEW LOGIC HERE: Log the outerHTML of the div if no ins is found ===
                        const divOuterHTML = await qmnDiv.evaluate(node => node.outerHTML);
                        console.log(`[${deviceName}]     - Full HTML of div #${parentContainerId} (no ins found):`);
                        console.log(divOuterHTML);
                        // =========================================================================
                        continue; // Try next qmnDiv if exists
                    }

                    // 3. Within each 'ins', search for the first 'iframe'
                    for (const ins of insHandles) {
                        insContainerId = await getElementId(ins); // Get ID of the INS element
                        console.log(`[${deviceName}]     - Found ins: #${insContainerId}. Searching for iframe...`);

                        // Find the first iframe within this specific 'ins' element
                        iframeHandle = await ins.$('iframe'); // Using $ to get the first one

                        if (iframeHandle) {
                            console.log(`[${deviceName}] ✅ Found iframe inside div #${parentContainerId} -> ins #${insContainerId} for ad.id ${ad.id}.`);
                            break; // Found the iframe, stop searching within this ad
                        }
                    }

                    if (iframeHandle) {
                        break; // Found iframe for this ad, exit qmnDiv loop
                    }
                }
            }

        } catch (error) {
            console.error(`[${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`);
            const details = { isTrulyVisible: false, reason: `Error during iframe discovery: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            continue; // Move to the next ad
        }

        // --- End NEW Iframe Discovery Logic ---

        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found after hierarchical search." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   -   Ad ID ${ad.id}: Iframe not found after all attempts.`);
            continue; // Move to the next ad
        }

        const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
        console.log(`[${deviceName}]   -   Iframe ID found for Ad ID ${ad.id}: ${iframeId}`);

        if (!iframeId) {
            const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`[${deviceName}]   -   Ad ID ${ad.id}: Iframe found, but it has no ID.`);
            await iframeHandle.dispose();
            continue; // Move to the next ad
        }

        const adSelector = `#${iframeId}`;

        // Scrolle das Element in die Mitte des Viewports, um sicherzustellen, dass es prüfbar ist.
        await iframeHandle.evaluate(el => el.scrollIntoView({ block: 'center', inline: 'center' }));
        // Eine kurze Wartezeit ist entscheidend, damit sich das Rendering nach dem Scrollen stabilisiert.
        await new Promise(resolve => setTimeout(resolve, 1000));

        // DEBUG: Überprüfen, ob das Element nach dem Scrollen im Viewport ist.
        const isElementInViewportForDebug = await iframeHandle.evaluate(isElementInViewportLogic);
        console.log(`[${deviceName}]   - DEBUG: Element ${adSelector} im Viewport nach Scroll: ${isElementInViewportForDebug}`);

        // Bewerte die Sichtbarkeit dieses einzelnen, jetzt zentrierten Elements.
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0];

        if (!result || !result.isTrulyVisible) {
            const details = result || { isTrulyVisible: false };
            notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
            const reason = !result ? 'Prüfung fehlgeschlagen' : (result.isOccluded ? 'Verdeckt' : (!result.isVisibleByCss ? 'CSS-versteckt' : 'Außerhalb der Seite'));
            console.log(`[${deviceName}]   - ❌ ${adSelector}: Nicht sichtbar. Grund: ${reason}`);
        } else {
            console.log(`[${deviceName}]   - ✅ ${adSelector}: Sichtbar.`);
        }

        await iframeHandle.dispose(); // Bereinige das Handle, um Speicherlecks zu vermeiden.
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] FEHLER [Visibility]: ${notVisibleAds.length} von ${adsToCheck.length} Anzeigen nicht wirklich sichtbar.`);
    } else {
        console.log(`[${deviceName}] ERFOLG [Visibility]: Alle ${adsToCheck.length} Anzeigen sind sichtbar.`);
    }
    return notVisibleAds.length;
}


const worker = new Worker(
  'url-jobs',
  async (job) => {
    const { url, layers } = job.data;
    const activeLayers = layers || ['placement', 'positioning', 'visibility'];

    console.log(`📥 Starte Analyse: ${url} mit Layern [${activeLayers.join(', ')}]`);

    const startTime = Date.now();

    const browser = await puppeteer.launch({
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();
    page.on('console', msg => console.log(`BROWSER LOG: ${msg.text()}`));

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });

      const cookieConsentAccepted = await acceptCookieConsent(page);
      if (cookieConsentAccepted !== 1) {
        throw new Error('Cookie-Zustimmung fehlgeschlagen oder nicht gefunden: ' + cookieConsentAccepted);
      }
      console.log('Cookie-Zustimmung akzeptiert, warte auf mögliche Overlays...');
      await new Promise(resolve => setTimeout(resolve, 2000)); 

      const qmnConfig = await checkSelectors(page);
      if (!qmnConfig) {
          throw new Error('window.qmn.config-Objekt konnte nicht abgerufen werden.');
      }
      const adsByResponsive = groupAdsByResponsive(qmnConfig);

      const issues = {};
      let totalIssues = 0;

      for (const [deviceName, config] of Object.entries(viewports)) {
        console.log(`\n--- Teste Gerät: ${config.name} (${config.width}x${config.height}) ---`);

        await page.setViewport({ width: config.width, height: config.height });
        await page.reload({ waitUntil: 'networkidle2' });

        await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
        await new Promise(resolve => setTimeout(resolve, 2000));
        const htmlContent = await page.content();
        console.log(`[${config.name}] HTML Content after reload and scroll:\n${htmlContent}`);

        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Überprüfe ${adsToCheck.length} Anzeigen für Responsive-Typ '${responsiveKey}'.`);

        if (adsToCheck.length === 0) {
            console.log(`[${deviceName}] Keine Anzeigen für den Gerätetyp '${responsiveKey}' zu prüfen.`);
            continue;
        }

        if (activeLayers.includes('placement')) {
            totalIssues += await runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues);
        }
        if (activeLayers.includes('positioning')) {
            totalIssues += await runPositioningCheck(page, adsToCheck, deviceName, responsiveKey, issues);
        }
        if (activeLayers.includes('visibility')) {
            totalIssues += await runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues);
        }
      }

      const endTime = Date.now();
      const durationMs = endTime - startTime;
      
      let finalResult;

      if (totalIssues === 0) {
          finalResult = {
              success: true,
              url,
              checkedLayers: activeLayers,
              timestamp: new Date().toISOString(),
              processingTimeMs: durationMs
          };
      } else {
          const finalIssues = {};
          if (issues.placement) {
              finalIssues.placement = {
                  desktop: issues.placement.desktop || [],
                  mobile: issues.placement.mobile || []
              };
          }
          if (issues.positioning) {
              finalIssues.positioning = {
                  desktop: issues.positioning.desktop || [],
                  mobile: issues.positioning.mobile || []
              };
          }
          if (issues.visibility) {
              finalIssues.visibility = {
                  desktop: issues.visibility.desktop || [],
                  mobile: issues.visibility.mobile || []
              };
          }

          finalResult = {
              success: false,
              url,
              checkedLayers: activeLayers,
              issues: finalIssues,
              timestamp: new Date().toISOString(),
              processingTimeMs: durationMs
          };
      }

      /* console.log("final result 📈 :" ,JSON.stringify(finalResult, null, 2)) */
    
      return finalResult

    } catch (e) {
      console.error(`❌ Fehler bei ${url}:`, e.message);
      return {
          url,
          error: e.message,
          timestamp: new Date().toISOString(),
      }
    } finally {
      await browser.close();
    }
  },
  { connection }
  
);

worker.on('completed', (job) => {
  console.log(`🎉 Job abgeschlossen: ${job.id}`);
});

worker.on('failed', (job, err) => {
  console.error(`❌ Job fehlgeschlagen: ${job.id} - ${err.message}`);
});
