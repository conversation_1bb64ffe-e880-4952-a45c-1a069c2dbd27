<style id="f11-floorAd-%creativeid%">
    :root{
        --backgroundColor-%creativeid%: #dadadae3;
        --buttonColor-%creativeid%: #989898;
        --arrowColor-%creativeid%: #fff;
    }

    .mobileFloorAdiframe-%creativeid%{
        background-color: var(--backgroundColor-%creativeid%);
        width: 300px!important;
        height: 600px!important;
        position: fixed;
        bottom: calc(100px - 600px);
        left: calc(50% - 150px);
        transition: transform 1s;
        line-height: 0px;
        font-size: 0px;
        z-index: 10000001;
    }
    #mobileFloorAdbackground-%creativeid%{
        background-color: var(--backgroundColor-%creativeid%);
        width: 100%;
        height: 600px;
        position: fixed;
        bottom: calc(100px - 600px);
        left: 0px;
        transition: transform 1s;
        z-index: 10000000;
    }
    #mflwrapper-%creativeid%{
        position: fixed;
        bottom: 100px;
        left: 0px;
        z-index: 100000001;
        width: 100%;
        height: 32px;
        transition: transform 1s;
    }
    #mobileFloorAd-%creativeid%{
        background-color: var(--backgroundColor-%creativeid%);
        width: 100%;
        height: 600px;
        position: fixed;
        bottom: calc(100px - 600px);
        left: 0px;
        transition: transform 1s;
        -webkit-box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.53);
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.53);
    }
    .mflopener-%creativeid%{
        width: 52px;
        height: 30px;
        position: relative;
        line-height: 1.5;
        left: calc(50% - 26px);
        top: -12px;
        transition: transform 1s;
        border-radius: 4px;
        -webkit-box-shadow: 0px 0px 6px 0px rgb(129 129 129 / 53%);
        box-shadow: 0px 0px 6px 0px rgb(129 129 129 / 53%);
        margin: initial;
    }

    #mflup-%creativeid%{
        background-color: var(--buttonColor-%creativeid%);
        display: block;
    }
    #mfldown-%creativeid%{
        background-color: var(--buttonColor-%creativeid%);
        display: none;
    }


    @-moz-keyframes mflbounce {0%, 20%, 50%, 80%, 100% {
        -moz-transform: translateY(0) rotate(90deg);
        transform: translateY(0) rotate(90deg);
    }40% {
         -moz-transform: translateY(-7px) rotate(90deg);
         transform: translateY(-7px) rotate(90deg);
     }60% {
          -moz-transform: translateY(-2px) rotate(90deg);
          transform: translateY(-2px) rotate(90deg);
      }
    }
    @-webkit-keyframes mflbounce {0%, 20%, 50%, 80%, 100% {
        -webkit-transform: translateY(0) rotate(90deg);
        transform: translateY(0) rotate(90deg);
    }40% {
         -webkit-transform: translateY(-7px) rotate(90deg);
         transform: translateY(-7px) rotate(90deg);
     }60% {
          -webkit-transform: translateY(-2px) rotate(90deg);
          transform: translateY(-2px) rotate(90deg);
      }
    }
    @keyframes mflbounce {0%, 20%, 50%, 80%, 100% {
        -moz-transform: translateY(0) rotate(90deg);
        -ms-transform: translateY(0) rotate(90deg);
        -webkit-transform: translateY(0) rotate(90deg);
        transform: translateY(0) rotate(90deg);
    }40% {
         -moz-transform: translateY(-7px) rotate(90deg);
         -ms-transform: translateY(-7px) rotate(90deg);
         -webkit-transform: translateY(-7px) rotate(90deg);
         transform: translateY(-7px) rotate(90deg);
     }60% {
          -moz-transform: translateY(-2px) rotate(90deg);
          -ms-transform: translateY(-2px) rotate(90deg);
          -webkit-transform: translateY(-2px) rotate(90deg);
          transform: translateY(-2px) rotate(90deg);
      }
    }

    .mflarrow-%creativeid% {
        position: absolute;
        left: 50%;
        margin-left: -5px;
        width: 20px;
        height: 20px;
        color: var(--arrowColor-%creativeid%);
        font-family: Arial, Helvetica, sans-serif;
        font-size: 20px;
        font-weight: bold;
        -webkit-transform:rotate(90deg);
        -moz-transform:rotate(90deg);
        -o-transform: rotate(90deg);
        -ms-transform:rotate(90deg);
        transform: rotate(90deg);
    /* writing-mode: vertical-rl;*/
    /* writing-mode:tb-rl; */

    }

    .mflarrowup-%creativeid% {
        bottom: 8px;
    }

    .mflarrowup2-%creativeid% {
        bottom: 14px;
    }

    .mflarrowdown-%creativeid% {
        bottom: 3px;
    }
    .mflarrowdown2-%creativeid% {
        bottom: -3px;
    }

    .mflbounce-%creativeid% {
        -moz-animation: mflbounce 2s infinite;
        -webkit-animation: mflbounce 2s infinite;
        animation: mflbounce 2s infinite;
        animation-iteration-count: 2;
    }

    #mflclose-%creativeid%{
        display: block;
        width: 12px;
        height: 30px;
        color: #323232;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 18px;
        font-weight:100;
    /* background-color: #EEEEEE; */
        position: absolute;
        top: 8px;
        left: 12px;
        text-align: center;
        line-height: 1.9;
    /* -webkit-box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.53);
    box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.53); */
    }

    #mflLogo-%creativeid%{
        height: 14px;
        width: 38px;
        display: none;
        position: absolute;
        top: 8px;
        right: 12px;
        line-height: 1.7;
        font-size: 18px;
    }

    #mflbuttons-%creativeid%{
        z-index: 1000;
    }
    .trf-ad-bottom-sticky-label{
        display: none!important;
    }

    #mflwerbung-%creativeid%{
        position: absolute;
        top: 8px;
        left: 12px;
        font-size: 12px;
        color: black;
        display: none;
        line-height: 30.6px;
    }

    .mflbefore{
        display: none;
    }

    .mfldesign-%creativeid%{
        position: absolute;
        bottom: -1px;
        display: flex;
        z-index: -1;
        width: 100%;
    }

    .floorad-mobile-%creativeid% {
        transition: opacity 0.2s;
    }
</style>
<script id="mflanker-%creativeid%">
    var mfaActive = true;
    var f11_screen = 0;
    var fadeOutSize = 550 - 50; // + 80 = 630px
    // var topOffset = 0;
    // //if (/^\/news\/[^/]+-\d+\/seite-\d+$/i.test(window.location.pathname)) {
    //     // get element .slideshow-pagination.bottom and get the height
    //     var pagination = document.querySelector('.slideshow-pagination.bottom');
    //     if (pagination) {
    //         topOffset = pagination.offsetHeight;
    //
    //         // overwrite css
    //         var style = document.createElement('style');
    //         style.innerHTML = '.mobileFloorAdiframe-%creativeid%, #mobileFloorAdbackground-%creativeid%, #mobileFloorAd-%creativeid% { bottom: calc(100px - 600px + ' + topOffset + 'px) !important; }';
    //         style.innerHTML += '#mflwrapper-%creativeid% { bottom: calc(100px + ' + topOffset + 'px) !important; }';
    //         document.head.appendChild(style);
    //
    //         // fadeOutSize -= topOffset;
    //     }
    // //}

    var mainDiv%creativeid% = document.querySelector("#mflwrapper-%creativeid%").parentElement.parentElement;
    mainDiv%creativeid%.classList.add("floorad-mobile-%creativeid%");

    function getCookieValue%creativeid%(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }

    const waitForElement = function (selector, callback) {
        // check if the element already exists
        if (mainDiv%creativeid%.querySelector(selector)) {
            setTimeout(callback, 100);
            return;
        }
        // create a new MutationObserver
        const observer = new MutationObserver((mutationsList, observer) => {
            // check if the element now exists
            if (mainDiv%creativeid%.querySelector(selector)) {
                setTimeout(callback, 100);
                observer.disconnect();
            }
        });
        // start observing changes to the mainDiv's children and subtree
        observer.observe(mainDiv%creativeid%, { childList: true, subtree: true });
    };

    // watch interstitial container for 20 times with 200ms delay
    // if interstitial container appears, hide floor ad
    let counter%creativeid% = 0;
    const interval%creativeid% = setInterval(() => {
        const interstitialContainer = document.querySelector('[class^="interstitial-mobile"] > div');
        if (interstitialContainer) {
            // check if interstitial is visible with IntersectionObserver
            const observer = new IntersectionObserver(entries => {
                const hide = entries[0]?.isIntersecting;
                mainDiv%creativeid%.style.opacity = hide ? '0' : '1';
                mainDiv%creativeid%.style.pointerEvents = hide ? 'none' : 'all';
            }, { threshold: [0.1] });
            observer.observe(interstitialContainer);
            clearInterval(interval%creativeid%);
        }

        if (counter%creativeid% >= 20) {
            clearInterval(interval%creativeid%);
        }

        counter%creativeid%++;
    }, 200);

    // check for qmnFloorAdClosed cookie to hide the floor ad
    var qmnFloorAdClosed = getCookieValue%creativeid%('qmnFloorAdClosed');
    var qmnFloorAd = getCookieValue%creativeid%('qmnFloorAd');
    if(qmnFloorAdClosed == "true" || qmnFloorAd == "0"){
        mfaActive = false;
    }

    // check available height. there must be at least 600px to show the ad
    if(window.screen.availHeight <= 600){
        mfaActive = false;
    }

    if(mfaActive) {
        parent.postMessage({key: "QMN-MOBILE-FLOORAD-INIT"}, "*");

        waitForElement('ins.asm_async_creative_lazyload', () => {
            const adIns = mainDiv%creativeid%.querySelector('ins.asm_async_creative_lazyload');
            if (adIns) {
                adIns.classList.add('asm_async_creative');
                adIns.classList.remove('asm_async_creative_lazyload');
                window.asm_async_data.initi();
            }
        });

        // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "aktiv");
        // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "V1"+ Math.floor(Math.random() * 10));
        // document.querySelector("#mflanker").closest("ins").id ="mflwrapper";  //INS Tag finden
        // document.querySelector("#mflanker").closest("iframe").classList.add("mobileFloorAdiframe");//iFrame finden
        //Ermittlung Seitenhöhe
        if(window.screen.availHeight <= window.innerHeight && window.screen.availHeight>300){
            f11_screen = window.screen.availHeight
        }else if(window.innerHeight <= window.outerHeight && window.innerHeight>300){
            f11_screen = window.innerHeight
        }else if(window.outerHeight<=300){
            f11_screen = window.outerHeight
        }else{
            f11_screen = 630
        };
        // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "f11_screen: "+f11_screen);
        if(f11_screen<=630){
            delteSmalScreen = 630 - f11_screen + 50;
            fadeOutSize = 550 - delteSmalScreen;
        }
        // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "fadeOutSize: "+fadeOutSize);

        // Parent iframe manipulieren
        if(window != top.window){
            if(document.querySelector('[id^="asm_indapif"]') != null){
                document.querySelector('[id^="asm_indapif"]').parentNode.classList.add("mobileFloorAdiframe-%creativeid%");
                // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "parentNode.classList");
            }else{
                window.frameElement.classList.add("mobileFloorAdiframe-%creativeid%");
                // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "window.frameElement");
            }
        }else{
            document.querySelector("#mflwrapper-%creativeid%").parentElement.closest("ins").classList.add("mobileFloorAdiframe-%creativeid%");
            // console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "parentElement.closest");
        }
        document.querySelector("#mflwrapper-%creativeid%").parentElement.parentElement.style.width="300px";
        var mflAdIframe = parent.document.querySelector(".mobileFloorAdiframe-%creativeid%");
        mflAdIframe.setAttribute('scrolling', 'no');

        // Button in Seite einbinden.
        var divmflwrapper = document.createElement('div');
        divmflwrapper.id = 'mflwrapper-%creativeid%';
        var divmflwrapperpos = document.createElement('div');
        divmflwrapperpos.style.position = 'relative';
        var divmflbuttons = document.createElement('div');
        divmflbuttons.id = 'mflbuttons-%creativeid%';
        var divmflup = document.createElement('div');
        divmflup.id = 'mflup-%creativeid%';
        divmflup.className = "mflopener-%creativeid%";
        var divmflarrowup = document.createElement('div');
        divmflarrowup.classList.add("mflarrow-%creativeid%", "mflarrowup-%creativeid%", "mflbounce-%creativeid%");
        var spanmflup = document.createElement('span');
        spanmflup.innerHTML="&#9001;";
        divmflarrowup.appendChild(spanmflup);
        divmflup.appendChild(divmflarrowup);
        var divmflarrowup2 = document.createElement('div');
        divmflarrowup2.classList.add("mflarrow-%creativeid%", "mflarrowup-%creativeid%", "mflarrowup2-%creativeid%", "mflbounce-%creativeid%");
        var spanmflup2 = document.createElement('span');
        spanmflup2.innerHTML="&#9001;";
        divmflarrowup2.appendChild(spanmflup2);
        divmflup.appendChild(divmflarrowup2);
        divmflbuttons.appendChild(divmflup);

        var divDesign = document.createElement('div');
        divDesign.classList.add("mfldesign-%creativeid%");
        divDesign.innerHTML=`<svg width="100%" height="100%" viewBox="0 0 414 38" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M170.789 3.67877C173.691 1.31512 177.313 0 181.056 0H232.944C236.687 0 240.309 1.31512 243.211 3.67877C245.859 5.8349 249.307 8.6076 252.49 11.0571C261.509 18 274 18 274 18H394C405.046 18 414 26.9543 414 38H0C0 26.9543 8.95432 18 20 18H140C140 18 152.491 18 161.511 11.0571C164.693 8.6076 168.141 5.8349 170.789 3.67877Z" fill="var(--backgroundColor-%creativeid%)"/></svg>`;
        divmflwrapper.appendChild(divDesign);

        var divmfldown = document.createElement('div');
        divmfldown.id = 'mfldown-%creativeid%';
        divmfldown.className = "mflopener-%creativeid%";
        var divmflarrowdown = document.createElement('div');
        divmflarrowdown.classList.add("mflarrow-%creativeid%", "mflarrowdown-%creativeid%");
        var spanmfldown = document.createElement('span');
        spanmfldown.innerHTML="&#9002;";
        divmflarrowdown.appendChild(spanmfldown);
        divmfldown.appendChild(divmflarrowdown);
        var divmflarrowdown2 = document.createElement('div');
        divmflarrowdown2.classList.add("mflarrow-%creativeid%","mflarrowdown-%creativeid%","mflarrowdown2-%creativeid%");
        var spanmfldown2 = document.createElement('span');
        spanmfldown2.innerHTML="&#9002;";
        divmflarrowdown2.appendChild(spanmfldown2);
        divmfldown.appendChild(divmflarrowdown2);
        divmflbuttons.appendChild(divmfldown);
        var divmflclose = document.createElement('div');
        divmflclose.id = 'mflclose-%creativeid%';
        divmflclose.innerHTML=`<svg width="15" height="15" fill="none" xmlns="http://www.w3.org/2000/svg" style="scale:0.7; vertical-align: initial"><path fill-rule="evenodd" clip-rule="evenodd" d="M.305.305a1.042 1.042 0 0 1 1.473 0L7.292 5.82 12.805.305a1.042 1.042 0 1 1 1.473 1.473L8.765 7.292l5.513 5.513a1.042 1.042 0 1 1-1.473 1.473L7.292 8.765l-5.514 5.513a1.042 1.042 0 0 1-1.473-1.473L5.82 7.292.305 1.778a1.042 1.042 0 0 1 0-1.473z" fill="#505050"/></svg>`;
        divmflbuttons.appendChild(divmflclose);

        var divmWerbung = document.createElement('div');
        divmWerbung.id = 'mflwerbung-%creativeid%';
        divmWerbung.innerHTML="-w-";
        divmflbuttons.appendChild(divmWerbung);

        var divmflLogo = document.createElement('a');
        divmflLogo.id = 'mflLogo-%creativeid%';
        divmflLogo.href="https://www.qualitymedianetwork.de";
        divmflLogo.target="_blank";
        divmflLogo.innerHTML=`<svg width="100%" height="100%" viewBox="0 0 275 100" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M70.234 73.534C71.579 74.579 73 76 73 76l11.5 11.5-.568-.105c-7.717-1.394-13.527-1.36-17.432.105l-.28.104c-5.945 2.19-10.588 2.654-17.076 2.353a40 40 0 1 1 41.16-47.387c.97 5.133.945 10.16-.05 15-.882 4.293-2.526 8.439-4.917 12.383-.11.183-.223.365-.337.547l-9.5-9s-.085-.026-.156-.138a.511.511 0 0 1-.045-.092c-.077-.204-.087-.582.201-1.27 1.993-4.765 2.565-9.796 1.594-14.933A26.556 26.556 0 1 0 64.615 72.8c.877-.523 2.235-.707 3.486-.402.*************.608.192.277.109.543.245.791.41a13.988 13.988 0 0 1 .734.533z" fill="#006AFF"/><path d="m106 13 37.5 44L181 13.077V86h-12V45.5l-25.5 30-25.5-30V86h-12V13zM200 13l52 47.5V13h12v73l-52-46.5V86h-12V13z" fill="#006AFF"/></svg>`;
        divmflbuttons.appendChild(divmflLogo);

        divmflwrapperpos.appendChild(divmflbuttons);
        divmflwrapper.appendChild(divmflwrapperpos);
        let mflsearchinsert = parent.document.querySelector('.mobileFloorAdiframe-%creativeid%');
        let mflparentDiv = mflsearchinsert.parentNode;
        mflparentDiv.insertBefore(divmflwrapper, mflsearchinsert);
        var divmflbackground = document.createElement('div');
        divmflbackground.id = 'mobileFloorAdbackground-%creativeid%';
        mflparentDiv.insertBefore(divmflbackground, mflsearchinsert);

        function mfl_up(){
            parent.document.getElementById("mflwrapper-%creativeid%").style.transform = 'translate(0px, -'+fadeOutSize+'px)';
            parent.document.getElementById("mobileFloorAdbackground-%creativeid%").style.transform = 'translate(0px, -'+fadeOutSize+'px)';
            mflAdIframe.style.transform = 'translate(0px, -'+fadeOutSize+'px)';
            parent.document.getElementById("mflclose-%creativeid%").style.display = 'none';
            parent.document.getElementById("mflup-%creativeid%").style.display = "none";
            parent.document.getElementById("mfldown-%creativeid%").style.display = "block";
            parent.document.getElementById("mflLogo-%creativeid%").style.display = "block";
            parent.document.getElementById("mflwerbung-%creativeid%").style.display = "block";
            parent.postMessage({key: "QMN-MOBILE-FLOORAD-EXPAND", size: fadeOutSize+80}, "*");
        }
        function mfl_down(){
            parent.document.getElementById("mflwrapper-%creativeid%").style.transform = 'translate(0px, 0px)';
            parent.document.getElementById("mobileFloorAdbackground-%creativeid%").style.transform = 'translate(0px, 0px)';
            mflAdIframe.style.transform = 'translate(0px, 0px)';
            parent.document.getElementById("mflclose-%creativeid%").style.display = 'block';
            parent.document.getElementById("mflup-%creativeid%").style.display = "block";
            parent.document.getElementById("mfldown-%creativeid%").style.display = "none";
            parent.document.getElementById("mflLogo-%creativeid%").style.display = "none";
            parent.document.getElementById("mflwerbung-%creativeid%").style.display = "none";
            parent.postMessage({key: "QMN-MOBILE-FLOORAD-COLLAPSE", size: 80}, "*");
        }
        function mfl_close(){
            parent.document.getElementById("mflwrapper-%creativeid%").style.display = "none";
            parent.document.getElementById("mobileFloorAdbackground-%creativeid%").style.display = "none";
            mflAdIframe.style.display = "none";

            // set cookie for 1 day to hide the floor ad for the next visits
            // var date = new Date();
            // date.setTime(date.getTime() + (1 * 24 * 60 * 60 * 1000));
            // var expires = "expires="+date.toUTCString();
            // document.cookie = "qmnFloorAdClosed=true; expires=" + expires + "; path=/";

            parent.postMessage({key: "QMN-MOBILE-FLOORAD-CLOSE"}, "*");
        }
        //Click Event auf Buttons setzten.
        parent.document.getElementById("mflup-%creativeid%").addEventListener("click", mfl_up);
        parent.document.getElementById("mfldown-%creativeid%").addEventListener("click", mfl_down);
        parent.document.getElementById("mflclose-%creativeid%").addEventListener("click", mfl_close);
        // var baumcheck = document.querySelector(".mobileFloorAdiframe-%creativeid%");
        // var baumcount = 0;
        // while (baumcheck.nodeName == "DIV"  && baumcount < 5 || baumcheck.nodeName == "INS" && baumcount < 6){
        //     if(baumcheck.nodeName == "DIV"){
        //         // baumcheck.style.zIndex = "100000 !important";
        //         baumcheck.style.setProperty("z-index", "100000", 'important');
        //         baumcheck.style.minHeight =  "0px";
        //         baumcheck.style.height =  "0px";
        //     }
        //     baumcheck = baumcheck.parentNode;
        //     baumcount++;
        // }
        //  document.getElementById("mflwrapper").parentElement.parentElement.classList.remove("trf-declaration-container"); // entfernen Anzeigenkennzeichnung

        var reloadFloorAdTimer%creativeid% = null;
        var reloadCount%creativeid% = 0;
        var reloadFloorAd%creativeid% = function(id){
            // reload the floor ad only 2 times
            if (reloadCount%creativeid% >= 2) {
                return;
            }

            // start timer to reload the floor ad after 500ms. if this function is called again, the timer will be reset
            if (reloadFloorAdTimer%creativeid%) {
                clearTimeout(reloadFloorAdTimer%creativeid%);
            }

            reloadFloorAdTimer%creativeid% = setTimeout(function() {
                reloadCount%creativeid%++;
                if (window.asm_async_data && window.asm_async_data.refreshSlot) {
                    console.log('reload floor ad');
                    window.asm_async_data.refreshSlot(id);
                }
            }, 500);
        }

        // add observer to check in mainDiv%creativeid% if an ins-element is added and has an iframe otherwise reload the floor ad
        // var observer%creativeid% = new MutationObserver(function(mutations) {
        //     mutations.forEach(function(mutation) {
        //         if (mutation.addedNodes.length > 0) {
        //             for (var i = 0; i < mutation.addedNodes.length; i++) {
        //                 var ins = mainDiv%creativeid%.querySelector('ins > ins');
        //                 // check if ins has data-attributes data-asm-done and data-asm-pushed which means the ad is loaded
        //                 if (ins && ins.hasAttribute('data-asm-done') && ins.hasAttribute('data-asm-pushed')) {
        //                     var insIframes = ins.querySelector('iframe[id^="adspiritflash"]');
        //                     if (!insIframes) {
        //                         reloadFloorAd%creativeid%(ins.id);
        //                     } else {
        //                         // stop timer if iframe is found
        //                         if (reloadFloorAdTimer%creativeid%) {
        //                             clearTimeout(reloadFloorAdTimer%creativeid%);
        //                         }
        //                         // stop observing if iframe element was found
        //                         observer%creativeid%.disconnect();
        //                     }
        //                 }
        //             }
        //         }
        //     });
        // });
        // observer%creativeid%.observe(mainDiv%creativeid%, { childList: true, attributes: true, subtree: true });
    }else{
        console.log("%c F11 FloorAd ", "background-color: BlueViolet; color: white; font-weight: bold; padding:2px; ", "deaktiviert");
        mainDiv%creativeid%.style.display = 'none';
    }

    const hideOnSections = [];
    const percentVisible = 10;
    let windowHeight = window.innerHeight || document.documentElement.clientHeight;

    if (window.qmn && window.qmn.mobile_brochuread) {
        const prospectAd = document.querySelector(`#qmn${window.qmn.mobile_brochuread}`);
        if (prospectAd) hideOnSections.push(prospectAd);
    }

    const isInView = array => array.find(el => {
        if (!el || !document.body.contains(el)) return false;
        const rect = el.getBoundingClientRect();

        // check if element is hidden
        if (rect.top === 0 && rect.left === 0 && rect.right === 0 && rect.bottom === 0) return false;

        return !(
            Math.floor(100 - (((rect.top >= 0 ? 0 : rect.top) / +-rect.height) * 100)) < percentVisible ||
            Math.floor(100 - ((rect.bottom - windowHeight) / rect.height) * 100) < percentVisible
        );
    });

    const hideFloorAd = () => {
        windowHeight = window.innerHeight || document.documentElement.clientHeight;
        // if available height is less than 600
        const hide = windowHeight < 600;

        //const hide = isInView(hideOnSections);
        mainDiv%creativeid%.style.opacity = hide ? '0' : '1';
        mainDiv%creativeid%.style.pointerEvents = hide ? 'none' : 'all';
    }

    // document.addEventListener('scroll', hideFloorAd);
    window.addEventListener('resize', hideFloorAd);
    // hideFloorAd();

</script>
<span id="mflwrapper-%creativeid%" class="mflbefore">
    <!-- // Werbefläche -->
</span>

