// Helper function to get element ID
async function getElementId(elementHandle) {
    if (elementHandle) {
        return await elementHandle.evaluate(el => el.id);
    }
    return null;
}

// Auto scroll to bottom function
const autoScrollToBottom = async (page) => {
    await page.evaluate(async () => {
        await new Promise(resolve => {
            let lastScrollHeight = 0;
            let scrollAttempts = 0;
            const maxScrollAttempts = 5;

            const scroll = () => {
                const currentScrollHeight = document.body.scrollHeight;
                window.scrollTo({ top: currentScrollHeight, behavior: 'smooth' });

                if (currentScrollHeight === lastScrollHeight) {
                    scrollAttempts++;
                } else {
                    scrollAttempts = 0;
                }

                lastScrollHeight = currentScrollHeight;

                if (scrollAttempts >= maxScrollAttempts) {
                    resolve();
                } else {
                    setTimeout(scroll, 200);
                }
            };
            scroll();
        });
    });
};

// Accept cookie consent function
const acceptCookieConsent = async (page) => {
  try {
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};

// Group ads by responsive type
const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN object or qmn.adSlots is not in the expected format.');
    return {};
  }

  const acc = qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    acc[responsive].push({ id, type });

    return acc;
  }, {});

  if (qmn.watchbetter) {
    acc.mobile.push({ id: 'watchbetter-embed', type: 'watchbetter' });
    acc.desktop.push({ id: 'watchbetter-embed', type: 'watchbetter' });   
  }

  return acc;
};

// Check selectors function
const checkSelectors = async (page) => {
  const qmnData = await page.evaluate(() => {
    return window.qmn;
  });

  if(!qmnData || !qmnData.config) {
    console.log('⚠️ window.qmn object or window.qmn.config not found on the page.');
    return null;
  }

  return qmnData.config;
};

// Placement check logic (executed in browser context)
const checkPlacementLogic = (ads) => {
    const missing = [];
    for (const ad of ads) {
        let adContainer;
        if (ad.type === 'watchbetter') {
            adContainer = document.getElementById(ad.id);
        } else {
            adContainer = document.getElementById(`qmn${ad.id}`);
        }
        if (!adContainer) {
            missing.push(ad);
        }
    }
    return missing;
};

// Visibility check logic (executed in browser context)
const checkVisibilityLogic = (selector) => {
    const el = document.querySelector(selector);
    if (!el) {
        return [{
            id: selector,
            isWithinPageBounds: false,
            isVisibleByCss: false,
            isOccluded: false,
            isPartiallyOutOfViewport: true,
            visibilityPercentage: 0,
            occludingElements: [],
            reason: 'Element not found'
        }];
    }

    const pageScrollHeight = document.body.scrollHeight;
    const pageScrollWidth = document.body.scrollWidth;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    const qmnContainer = el.closest('div[id^="qmn"], div[id="watchbetter-embed"]');
    const parentOfQmnContainer = qmnContainer ? qmnContainer.parentElement : null;

    const rect = el.getBoundingClientRect();
    
    const isRendered = rect.width > 0 && rect.height > 0;

    const scrollTop = window.scrollY || window.pageYOffset;
    const scrollLeft = window.scrollX || window.pageXOffset;
    const isWithinPageBounds = isRendered &&
        (rect.top + scrollTop) >= 0 &&
        (rect.left + scrollLeft) >= 0 &&
        (rect.bottom + scrollTop) <= pageScrollHeight &&
        (rect.right + scrollLeft) <= pageScrollWidth;

    let isVisibleByCss = false;
    if (isRendered) {
        if (typeof el.checkVisibility === 'function') {
            isVisibleByCss = el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
        }
    }
    
    let visiblePoints = 0;
    const occludingElementsRaw = [];
    let pointsOutsideViewport = 0;

    const samplePoints = [
        { x: rect.left + 1, y: rect.top + 1 },
        { x: rect.left + rect.width / 2, y: rect.top + 1 },
        { x: rect.right - 1, y: rect.top + 1 },
        { x: rect.left + 1, y: rect.top + rect.height / 2 },
        { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 },
        { x: rect.right - 1, y: rect.top + rect.height / 2 },
        { x: rect.left + 1, y: rect.bottom - 1 },
        { x: rect.left + rect.width / 2, y: rect.bottom - 1 },
        { x: rect.right - 1, y: rect.bottom - 1 }
    ];

    if (isVisibleByCss && isWithinPageBounds) {
        samplePoints.forEach(point => {
            if (point.x >= 0 && point.x < viewportWidth && point.y >= 0 && point.y < viewportHeight) {
                const elementAtPoint = document.elementFromPoint(point.x, point.y);
                
                if (!elementAtPoint) {
                    return;
                }

                if (el.contains(elementAtPoint) || (qmnContainer && elementAtPoint === qmnContainer) || (parentOfQmnContainer && elementAtPoint === parentOfQmnContainer)) {
                    visiblePoints++;
                } else {
                    occludingElementsRaw.push({
                        tag: elementAtPoint.tagName,
                        id: elementAtPoint.id || null,
                        className: elementAtPoint.className || null
                    });
                }
            } else {
                pointsOutsideViewport++;
            }
        });
    }

    const visibilityPercentage = Math.round((visiblePoints / samplePoints.length) * 100);
    
    // Deduplicate occluding elements
    const occludingElements = [];
    if (occludingElementsRaw.length > 0) {
        const seenOccluders = new Set();
        for (const o of occludingElementsRaw) {
            const key = `${o.tag}${o.id ? '#' + o.id : ''}${o.className ? '.' + String(o.className).split(' ').join('.') : ''}`;
            if (!seenOccluders.has(key)) {
                seenOccluders.add(key);
                occludingElements.push(o);
            }
        }
    }

    const isOccluded = occludingElements.length > 0;
    const isPartiallyOutOfViewport = pointsOutsideViewport > 0;

    const finalResult = {
        id: el.id,
        isWithinPageBounds,
        isVisibleByCss,
        isOccluded,
        isPartiallyOutOfViewport,
        visibilityPercentage,
        occludingElements
    };

    return [finalResult];
};

// Run placement check
async function runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    const unplacedAds = await page.evaluate(checkPlacementLogic, adsToCheck);
    if (unplacedAds.length > 0) {
        if (!issues.placement) issues.placement = { desktop: [], tablet: [], mobile: [] };
        issues.placement[responsiveKey] = unplacedAds;
        console.log(`[${deviceName}] ERROR [Placement]: ${unplacedAds.length} Display containers not found in the DOM:`, unplacedAds.map(ad => ad.id));
    } else {
        console.log(`[${deviceName}] SUCCESS [Placement]: All ${adsToCheck.length} display containers found.`);
    }
    return unplacedAds.length;
}

// Run visibility check
async function runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues) {
    if (adsToCheck.length === 0) {
        console.log(`### ➡️ [${deviceName}] SUCCESS [Visibility]: No ads to check for this layer.`);
        return 0;
    }

    const notVisibleAds = [];
    console.log(`### ➡️ [${deviceName}] Starting individual visibility check for ${adsToCheck.length} ads...`);

    for (const ad of adsToCheck) {
        let iframeHandle = null;
        let parentContainerHandle = null;
        let parentContainerId = null;
        let insContainerId = null;

        try {
            if (ad.type === 'watchbetter') {
                console.log(`### ➡️  [${deviceName}] ➡️ Checking ad ID: ${ad.id} (watchbetter). Searching for container: #${ad.id}`);
                const watchbetterContainerHandle = await page.$(`#${ad.id}`);
                if (watchbetterContainerHandle) {
                    parentContainerId = await getElementId(watchbetterContainerHandle);
                    iframeHandle = await watchbetterContainerHandle.$('iframe');
                    if (iframeHandle) {
                        parentContainerHandle = watchbetterContainerHandle;
                        console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside #${parentContainerId} for watchbetter ad.`);
                    }
                }
            } else {
                const qmnDivSelector = `div[id^="qmn"][id*="${ad.id}"]`;
                console.log(`### ➡️  [${deviceName}] ➡️ Checking ad ID: ${ad.id}. Searching for div: ${qmnDivSelector}`);

                const qmnDivHandles = await page.$$(qmnDivSelector);
                
                if (qmnDivHandles.length === 0) {
                    console.log(`### ➡️  [${deviceName}] ⚠️ WARN: No div element matching "${qmnDivSelector}" found for ad.id ${ad.id}.`);
                    const asmobjContainerSelector = `div[id*="asmobj_${ad.id}"]`;
                    const asmobjContainerHandle = await page.$(asmobjContainerSelector);
                    if (asmobjContainerHandle) {
                        parentContainerId = await getElementId(asmobjContainerHandle);
                        iframeHandle = await asmobjContainerHandle.waitForSelector('iframe[id^="adspiritflash"]', { timeout: 5000 });
                        if (iframeHandle) {
                            parentContainerHandle = asmobjContainerHandle;
                            console.log(`### ➡️ [${deviceName}] ✅ Found iframe via 'asmobj_' fallback for ad.id ${ad.id}. Parent: ${parentContainerId}`);
                        }
                    }

                    if (!iframeHandle) {
                        const details = { isTrulyVisible: false, reason: `No container div (${qmnDivSelector} or fallback) found for ad.id.` };
                        notVisibleAds.push({ ...ad, visibilityDetails: details });
                        console.log(`### ➡️ [${deviceName}]   -   Ad ID ${ad.id}: Container div not found.`);
                        continue;
                    }
                } else {
                    for (const qmnDiv of qmnDivHandles) {
                        parentContainerId = await getElementId(qmnDiv);
                        console.log(`### ➡️ [${deviceName}]   - Found QMN div: #${parentContainerId}. Searching for ins...`);

                        const insHandles = await qmnDiv.$$('ins');

                        if (insHandles.length === 0) {
                            console.log(`### ➡️ [${deviceName}]     - No 'ins' element found within div #${parentContainerId}.`);
                            continue;
                        }

                        for (const ins of insHandles) {
                            insContainerId = await getElementId(ins);
                            console.log(`### ➡️ [${deviceName}]     - Found ins: #${insContainerId}. Searching for iframe...`);
                            iframeHandle = await ins.$('iframe');

                            if (iframeHandle) {
                                parentContainerHandle = qmnDiv;
                                console.log(`### ➡️ [${deviceName}] ✅ Found iframe inside div #${parentContainerId} -> ins #${insContainerId} for ad.id ${ad.id}.`);
                                break;
                            }
                        }

                        if (iframeHandle) {
                            break;
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`### ➡️ [${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`);
            const details = { isTrulyVisible: false, reason: `Error during iframe discovery: ${error.message}` };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            continue;
        }

        if (!iframeHandle) {
            const details = { isTrulyVisible: false, reason: "Iframe element not found after hierarchical search." };
            notVisibleAds.push({ ...ad, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe not found after all attempts.`);
            if (parentContainerHandle) await parentContainerHandle.dispose();
            continue;
        }

        let adSelector;
        if (ad.type === 'watchbetter') {
            adSelector = `#${parentContainerId} > iframe`;
            console.log(`### ➡️ [${deviceName}] -  Using selector for watchbetter ad: ${adSelector}`);
        } else {
            const iframeId = await iframeHandle.evaluate(iframe => iframe.id);
            console.log(`### ➡️ [${deviceName}] -  Iframe ID found for Ad ID ${ad.id}: ${iframeId}`);

            if (!iframeId) {
                const details = { isTrulyVisible: false, reason: "Iframe element found, but it has no ID, so it cannot be checked for visibility." };
                notVisibleAds.push({ ...ad, visibilityDetails: details });
                console.log(`### ➡️ [${deviceName}]  -  Ad ID ${ad.id}: Iframe found, but it has no ID.`);
                if (parentContainerHandle) await parentContainerHandle.dispose();
                await iframeHandle.dispose();
                continue;
            }
            adSelector = `#${iframeId}`;
        }

        // Ad-Type-Specific Visibility Logic
        console.log(`### ➡️ [${deviceName}]   - Applying visibility logic for ad type: ${ad.type}`);

        let floorAdButtonHandle = null;

        switch (ad.type) {
            case 'floorad':
                console.log(`### ➡️ [${deviceName}]  Specific logic for 'floorad' is applied.`);
                const floorAdButtonSelector = 'div[id^="mflbuttons-"]';
                floorAdButtonHandle = await page.$(floorAdButtonSelector);

                if (floorAdButtonHandle) {
                    console.log(`### ➡️ [${deviceName}]  Floorad button found. Click to open the ad.`);
                    await floorAdButtonHandle.click();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } else {
                    console.log(`### ➡️ [${deviceName}]  WARN: Floorad button (${floorAdButtonSelector}) not found.`);
                }
                break;

            case 'sitebar':
                console.log(`### ➡️ [${deviceName}]  Specific logic for 'sitebar' is applied.`);
                if (!parentContainerHandle) {
                    console.log(`### ➡️ [${deviceName}]  WARN: Parent container handle for sitebar ad ${ad.id} not found. Scroll logic skipped.`);
                    break;
                }

                let isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                    return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                });
                
                console.log(`### ➡️ [${deviceName}]  Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`);

                if (!isContainerVisibleByCss) {
                    console.log(`### ➡️ [${deviceName}]  Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`);
                    
                    const maxScrollAttempts = 50;
                    let attempts = 0;

                    const scrollPosition = await page.evaluate(() => ({
                        scrollTop: window.scrollY,
                        scrollHeight: document.body.scrollHeight,
                        clientHeight: document.documentElement.clientHeight
                    }));
                    
                    const isCloseToBottom = (scrollPosition.scrollTop + scrollPosition.clientHeight) >= (scrollPosition.scrollHeight - 100);
                    const scrollDirection = isCloseToBottom ? 1 : -1;
                    const scrollAmount = 120;

                    console.log(`### ➡️ [${deviceName}]   - Scroll ${scrollDirection === 1 ? 'DOWN' : 'UP'}, to find the sitebar container.`);

                    while (!isContainerVisibleByCss && attempts < maxScrollAttempts) {
                        await page.evaluate((y) => { window.scrollBy(0, y); }, scrollDirection * scrollAmount);
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        isContainerVisibleByCss = await parentContainerHandle.evaluate(el => {
                            if (typeof el.checkVisibility !== 'function') return false;
                            return el.checkVisibility({ checkOpacity: true, checkVisibilityCSS: true });
                        });
                        attempts++;

                        const newScrollTop = await page.evaluate(() => window.scrollY);
                        if ((scrollDirection === -1 && newScrollTop === 0) || 
                            (scrollDirection === 1 && (await page.evaluate(() => (window.innerHeight + window.scrollY) >= document.body.scrollHeight)))) {
                            console.log(`### ➡️ [${deviceName}]   - page ${scrollDirection === 1 ? 'end' : 'start'} Reached. Stop the scroll search.`);
                            break;
                        }
                    }

                    if (isContainerVisibleByCss) {
                        console.log(`### ➡️ [${deviceName}]   - Sitebar container is now visible after ${attempts} scroll attempts via CSS.`);
                    } else {
                        console.log(`### ➡️ [${deviceName}]   - WARN: Could not make the sitebar container visible after ${attempts} attempts via CSS.`);
                    }
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;

            case 'intext':
            case 'prospekt':
            case 'hpa':
            default:
                console.log(`### ➡️ [${deviceName}]   - Standard logic (scrollIntoView) is applied.`);
                await iframeHandle.evaluate(el => {
                    const rect = el.getBoundingClientRect();
                    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                    const targetY = rect.top + window.scrollY - (viewportHeight / 2) + (rect.height / 2);
                    window.scrollTo({ top: targetY });
                });
                await new Promise(resolve => setTimeout(resolve, 1000));
                break;
        }

        // Evaluate the visibility of this single element
        const visibilityResults = await page.evaluate(checkVisibilityLogic, adSelector);
        const result = visibilityResults[0];

        if (!result || result.visibilityPercentage < 100) {
            let reason = 'Others';
            
            if (result.reason === 'Element not found') {
                reason = 'Element not found';
            } else if (!result.isVisibleByCss) {
                reason = 'Hidden by CSS';
            } else if (result.isOccluded) {
                reason = 'Covered';
            } else if (result.isPartiallyOutOfViewport) {
                reason = 'Not in viewport';
            } else if (!result.isWithinPageBounds) {
                reason = 'Not within page bounds';
            }

            const details = {
                visibilityPercentage: result.visibilityPercentage,
                reason: reason,
                isVisibleByCss: result.isVisibleByCss,
                occludingElements: result.occludingElements
            };

            notVisibleAds.push({ id: ad.id, type: ad.type, visibilityDetails: details });
            console.log(`### ➡️ [${deviceName}]   - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`);
        } else {
            console.log(`### ➡️ [${deviceName}]   - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`);
        }

        // Close the Floorad ad if it was opened
        if (floorAdButtonHandle) {
            console.log(`### ➡️ [${deviceName}]  Close Floorad ad by clicking again.`);
            try {
                await floorAdButtonHandle.click();
                await new Promise(resolve => setTimeout(resolve, 2000));
            } catch (e) {
                console.log(`### ➡️ [${deviceName}]  WARN: Floorad button could not be clicked again: ${e.message}`);
            } finally {
                await floorAdButtonHandle.dispose();
            }
        }

        if (parentContainerHandle) {
            await parentContainerHandle.dispose();
        }
        await iframeHandle.dispose();
    }

    if (notVisibleAds.length > 0) {
        if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
        issues.visibility[responsiveKey] = notVisibleAds;
        console.log(`[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`);
    } else {
        console.log(`[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`);
    }
    return notVisibleAds.length;
}

// Main function to run checks for a device
export async function runChecksForDevice(browser, url, deviceName, config, activeLayers, consentState) {
    const page = await browser.newPage();
    page.on('console', msg => console.log(`[${config.name} BROWSER LOG]: ${msg.text()}`));

    try {
        console.log(`\n--- Test device: ${config.name} (${config.width}x${config.height}) ---`);
        await page.setViewport({ width: config.width, height: config.height });
        await page.goto(url, { waitUntil: 'networkidle2' });

        if (!consentState.handled) {
            console.log(`[${config.name}] First device check: Trying to accept cookie consent...`);
            const consentResult = await acceptCookieConsent(page);
            if (consentResult === 1) {
                console.log(`[${config.name}] Cookie consent accepted successfully.`);
                consentState.handled = true;
            } else {
                console.warn(`[${config.name}] Cookie consent could not be accepted (Code: ${consentResult}). Test is continuing.`);
            }
        }

        console.log(`[${config.name}] Wait for TCF API confirmation... ⏳ `);
        try {
            await page.evaluate(() => {
                return new Promise((resolve, reject) => {
                    const checkTcfApi = (retries = 20) => {
                        if (typeof window.__tcfapi === 'function') {
                            window.__tcfapi('getTCData', 2, (tcData, success) => {
                                if (success) {
                                    console.log('TCF API success:', tcData);
                                    resolve();
                                } else if (retries > 0) {
                                    setTimeout(() => checkTcfApi(retries - 1), 500);
                                } else {
                                    reject(new Error('TCF API did not return success within the timeout.'));
                                }
                            });
                        } else if (retries > 0) {
                            setTimeout(() => checkTcfApi(retries - 1), 500);
                        } else {
                            reject(new Error('__tcfapi function not found on page within the timeout. ❌ '));
                        }
                    };
                    checkTcfApi();
                });
            });
            console.log(`[${config.name}] TCF API confirmation received successfully. ✅ `);
        } catch (e) {
            console.warn(`[${config.name}] Error waiting for TCF API: ${e.message} ❌ `);
        }

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Lazy load to ensure all content has been loaded
        await autoScrollToBottom(page);

        // Extract the ads from the QMN object
        const qmnConfig = await checkSelectors(page);
        if (!qmnConfig) {
            throw new Error(`[${config.name}] Could not retrieve window.qmn.config object.`);
        }

        // Group the ads by responsive type (desktop/mobile)
        const adsByResponsive = groupAdsByResponsive(qmnConfig);
        const responsiveKey = config.deviceType;
        const adsToCheck = adsByResponsive[responsiveKey] || [];
        console.log(`[${deviceName}] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`);

        // Run the checks for each layer and count the issues
        const issues = {};
        let issueCount = 0;

        if (adsToCheck.length > 0) {
            if (activeLayers.includes('placement')) {
                issueCount += await runPlacementCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
            if (activeLayers.includes('visibility')) {
                issueCount += await runVisibilityCheck(page, adsToCheck, deviceName, responsiveKey, issues);
            }
        } else {
            console.log(`[${deviceName}] No ads to check for device type '${responsiveKey}'.`);
        }

        return { deviceName, responsiveKey, issues, issueCount, error: null };

    } catch (e) {
        console.error(`❌ Error with device ${deviceName}:`, e.message);
        return { deviceName, error: e.message, issues: {}, issueCount: 1 };
    } finally {
        await page.close();
    }
}
