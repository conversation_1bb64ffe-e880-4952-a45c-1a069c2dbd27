String.prototype.replaceAll = function (search, replacement) {
  var target = this;
  return target.split(search).join(replacement);
};
function asm_async_obj() {
  this.pageViewID =
    "asm" + Math.round(Math.random() * 99999999) + "x" + new Date().getTime();
  this.wallpaperIndex = -1;
  this.win = window;
  this.doc = this.win.document;
  this.inswrapper = "";
  this.scrollAttached = false;
  this.scrollIntoObjects = new Array();
  this.settings = [];
  this.inDebugMode = false;
  this.log = function (s, evt) {
    if (!this.inDebugMode) {
      return;
    }
    if (evt) {
      console.log(
        "%cf11-ads Async%c" + s,
        "color:white; background-color:#a0c85f; border-left:5px solid black; padding:3px; margin-right:10px;",
        "",
        evt
      );
    } else {
      console.log(
        "%cf11-ads Async%c" + s,
        "color:white; background-color:#a0c85f; border-left:5px solid black; padding:3px; margin-right:10px;",
        ""
      );
    }
  };
  this.writeCookie = function (n, w, e) {
    var a = new Date(),
      d;
    a = new Date(a.getTime() + e * 86400 * 1e3);
    d = location.hostname.toLowerCase();
    if (d.substr(0, 4) == "www.") {
      d = d.substr(4, 999);
      d = "." + d;
    } else if (d.split(".").length - 1 == 1) {
      d = "." + d;
    }
    document.cookie =
      n + "=" + w + ";expires=" + a.toUTCString() + ";path=/;domain=" + d;
  };
  this.readCookie = function (n) {
    var a = document.cookie;
    var res = "";
    var dl = 0,
      i;
    while (a != "" && dl < 100) {
      dl++;
      while (a.substr(0, 1) == " ") {
        a = a.substr(1, a.length);
      }
      var cn = a.substring(0, a.indexOf("="));
      if (a.indexOf(";") != -1) {
        var v = a.substring(a.indexOf("=") + 1, a.indexOf(";"));
      } else {
        var v = a.substr(a.indexOf("=") + 1, a.length);
      }
      if (n == cn) {
        res = v;
      }
      i = a.indexOf(";") + 1;
      if (i == 0) {
        i = a.length;
      }
      a = a.substring(i, a.length);
    }
    return res;
  };
  this.hasCookie = function (n) {
    var a = document.cookie;
    var res = false;
    var dl = 0,
      i;
    while (a != "" && dl < 100) {
      dl++;
      while (a.substr(0, 1) == " ") {
        a = a.substr(1, a.length);
      }
      var cn = a.substring(0, a.indexOf("="));
      if (a.indexOf(";") != -1) {
        var v = a.substring(a.indexOf("=") + 1, a.indexOf(";"));
      } else {
        var v = a.substr(a.indexOf("=") + 1, a.length);
      }
      if (n == cn) {
        res = true;
        break;
      }
      i = a.indexOf(";") + 1;
      if (i == 0) {
        i = a.length;
      }
      a = a.substring(i, a.length);
    }
    return res;
  };
  this.fndwin = function (w, o) {
    try {
      var ifs = w.document.getElementsByTagName("iframe");
      for (var i = 0; i < ifs.length; i++) {
        try {
          if (ifs[i].contentWindow == o) {
            return ifs[i];
          }
        } catch (e) {}
      }
    } catch (e) {}
    return null;
  };
  this.checkFlash = function () {
    var major = -1;
    var activeXDetectRules = [
      {
        name: "ShockwaveFlash.ShockwaveFlash.7",
        version: function (obj) {
          return getActiveXVersion(obj);
        },
      },
      {
        name: "ShockwaveFlash.ShockwaveFlash.6",
        version: function (obj) {
          var version = "6,0,21";
          try {
            obj.AllowScriptAccess = "always";
            version = getActiveXVersion(obj);
          } catch (err) {}
          return version;
        },
      },
      {
        name: "ShockwaveFlash.ShockwaveFlash",
        version: function (obj) {
          return getActiveXVersion(obj);
        },
      },
    ];
    var getActiveXVersion = function (activeXObj) {
      var version = -1;
      try {
        version = activeXObj.GetVariable("$version");
      } catch (err) {}
      return version;
    };
    var getActiveXObject = function (name) {
      var obj = -1;
      try {
        obj = new ActiveXObject(name);
      } catch (err) {
        obj = { activeXError: true };
      }
      return obj;
    };
    var parseActiveXVersion = function (str) {
      var versionArray = str.split(",");
      return { major: parseInt(versionArray[0].split(" ")[1], 10) };
    };
    var parseStandardVersion = function (str) {
      var descParts = str.split(/ +/);
      var majorMinor = descParts[2].split(/\./);
      return { major: parseInt(majorMinor[0], 10) };
    };
    var parseRevisionStrToInt = function (str) {
      return parseInt(str.replace(/[a-zA-Z]/g, ""), 10) || self.revision;
    };
    if (navigator.plugins && navigator.plugins.length > 0) {
      var type = "application/x-shockwave-flash";
      var mimeTypes = navigator.mimeTypes;
      if (
        mimeTypes &&
        mimeTypes[type] &&
        mimeTypes[type].enabledPlugin &&
        mimeTypes[type].enabledPlugin.description
      ) {
        var version = mimeTypes[type].enabledPlugin.description;
        var versionObj = parseStandardVersion(version);
        major = versionObj.major;
      }
    } else if (
      navigator.appVersion.indexOf("Mac") == -1 &&
      this.win.execScript
    ) {
      var version = -1;
      for (var i = 0; i < activeXDetectRules.length && version == -1; i++) {
        var obj = getActiveXObject(activeXDetectRules[i].name);
        if (!obj.activeXError) {
          version = activeXDetectRules[i].version(obj);
          if (version != -1) {
            var versionObj = parseActiveXVersion(version);
            major = versionObj.major;
          }
        }
      }
    }
    return major;
  };
  this.swf = this.checkFlash();
  this.checkRef = function () {
    var asmref = location.toString();
    var asmdl = 0;
    var asmwin = self;
    var asmgt = false;
    if (self != top) {
      try {
        var asmtl = top.location.toString();
        asmref = asmtl;
        asmgt = true;
      } catch (e) {}
      if (!asmgt) {
        if (typeof location.ancestorOrigins == "object") {
          asmref =
            location.ancestorOrigins[location.ancestorOrigins.length - 1];
          asmgt = true;
        } else {
          while (asmwin && asmwin != top && asmdl < 100 && asmwin.parent) {
            var asmcw = asmwin;
            asmwin = asmwin.parent;
            if (asmwin.location) {
              try {
                asmref = asmwin.location.toString();
                asmgt = true;
              } catch (e) {
                if (asmcw != top) {
                  if (asmcw.document) {
                    asmref =
                      typeof asmcw.document.referrer == "string" &&
                      asmcw.document.referrer != ""
                        ? asmcw.document.referrer
                        : asmref;
                    asmgt = true;
                  } else {
                    asmref =
                      typeof asmcw.referrer == "string" && asmcw.referrer != ""
                        ? asmcw.referrer
                        : asmref;
                    asmgt = true;
                  }
                }
                break;
              }
            }
            asmdl++;
          }
        }
      }
    }
    if (
      self == top ||
      asmref == undefined ||
      asmref == "" ||
      asmref == "-" ||
      asmref == "undefined" ||
      typeof asmref == "undefined" ||
      !asmgt
    ) {
      asmref =
        self == top
          ? location.href
          : typeof document.referrer == "string"
          ? document.referrer
          : "";
    }
    return asmref;
  };
  this.ref = this.checkRef();
  this.asm_gp = function (e, w, h) {
    var o = this.doc.getElementById(e);
    if (!o || o == null) {
      return [0, 0, 0, 0, false];
    }
    var l = 0,
      t = 0;
    if (typeof w == "undefined" || w == null || isNaN(w)) {
      w = o.offsetWidth;
    }
    if (typeof h == "undefined" || h == null || isNaN(h)) {
      h = o.offsetHeight;
    }
    if (typeof w == "undefined" || w == null || isNaN(w) || w < 1) {
      w = 1;
    }
    if (typeof h == "undefined" || h == null || isNaN(h) || h < 1) {
      h = 1;
    }
    var z = 0;
    while (o.offsetParent !== null) {
      z++;
      if (z > 300) {
        break;
      }
      l += o.offsetLeft;
      t += o.offsetTop;
      o = o.offsetParent;
    }
    var u = [l, t, w, h, true];
    return u;
  };
  this.asm_ds = function (e) {
    var w = 0,
      h = 0,
      sx = 0,
      sy = 0;
    if (
      e.document.documentElement &&
      e.document.documentElement.clientHeight &&
      typeof e.document.documentElement.clientHeight == "number"
    ) {
      h = e.document.documentElement.clientHeight;
    } else if (
      e.document.body &&
      e.document.body.clientHeight &&
      typeof e.document.body.clientHeight == "number"
    ) {
      h = e.document.body.clientHeight;
    } else if (e.innerHeight && typeof e.innerHeight == "number") {
      h = e.innerHeight;
    }
    if (
      e.document.documentElement &&
      e.document.documentElement.clientWidth &&
      typeof e.document.documentElement.clientWidth == "number"
    ) {
      w = e.document.documentElement.clientWidth;
    } else if (
      e.document.body &&
      e.document.body.clientWidth &&
      typeof e.document.body.clientWidth == "number"
    ) {
      w = e.document.body.clientWidth;
    } else if (e.innerWidth && typeof e.innerWidth == "number") {
      w = e.innerWidth;
    }
    if (
      e.document.documentElement &&
      e.document.documentElement.scrollLeft &&
      typeof e.document.documentElement.scrollLeft == "number"
    ) {
      sx = e.document.documentElement.scrollLeft;
    } else if (e.pageXOffset && typeof e.pageXOffset == "number") {
      sx = e.pageXOffset;
    }
    if (
      e.document.documentElement &&
      e.document.documentElement.scrollTop &&
      typeof e.document.documentElement.scrollTop == "number"
    ) {
      sy = e.document.documentElement.scrollTop;
    } else if (e.pageYOffset && typeof e.pageYOffset == "number") {
      sy = e.pageYOffset;
    }
    if (
      e.document.documentElement &&
      e.document.documentElement.clientLeft &&
      typeof e.document.documentElement.clientLeft == "number"
    ) {
      sx -= e.document.documentElement.clientLeft;
    }
    if (
      e.document.documentElement &&
      e.document.documentElement.clientTop &&
      typeof e.document.documentElement.clientTop == "number"
    ) {
      sy -= e.document.documentElement.clientTop;
    }
    return [w, h, sx, sy];
  };
  this.checkVisibility = function (id) {
    var e = this.doc.getElementById(id);
    if (!e) {
      return -1;
    }
    var w = parseInt(e.style.width),
      h = parseInt(e.style.height),
      r = this.asm_gp(id, w, h),
      a,
      b,
      u;
    if (r[4] == false) {
      return -1;
    }
    w = r[2];
    h = r[3];
    var i = this.asm_ds(this.win);
    if (i[0] == 0 || i[1] == 0) {
      return -1;
    }
    if (i[0] < w + 100 && i[1] < h + 100) {
      return -1;
    }
    var x1 = r[0];
    var y1 = r[1];
    var x2 = x1 + w;
    var y2 = y1 + h;
    x1 = x1 > i[2] ? x1 : i[2];
    y1 = y1 > i[3] ? y1 : i[3];
    x2 = x2 < i[2] + i[0] ? x2 : i[2] + i[0];
    y2 = y2 < i[3] + i[1] ? y2 : i[3] + i[1];
    if (x1 >= x2 || y1 > y2) {
      u = 0;
    } else {
      var nw = x2 - x1;
      var nh = y2 - y1;
      a = w * h;
      b = nw * nh;
      if (a <= 0 || b <= 0) {
        u = 0;
      } else {
        u = Math.round((b / a) * 100);
      }
    }
    if (u >= 0 && u < 25) {
      return 0;
    } else if (u >= 25 && u < 50) {
      return 1;
    } else if (u >= 50 && u < 75) {
      return 2;
    } else if (u >= 75 && u < 100) {
      return 3;
    } else if (u >= 100) {
      return 4;
    } else {
      return -1;
    }
    return u;
  };
  this.checkInView = function (id) {
    var e = this.doc.getElementById(id);
    var w = parseInt(e.style.width);
    var h = parseInt(e.style.height);
    var r = this.asm_gp(id, w, h);
    if (r[4] == false) {
      return false;
    }
    w = r[2];
    h = r[3];
    var i = this.asm_ds(this.win);
    if (i[0] == 0 || i[1] == 0) {
      return false;
    }
    var pgx1 = i[2];
    var pgx2 = i[0] + i[2];
    var pgy1 = i[3];
    var pgy2 = i[1] + i[3];
    var dgx1 = r[0];
    var dgx2 = r[0] + r[2];
    var dgy1 = r[1];
    var dgy2 = r[1] + r[3];
    return dgx1 < pgx2 && dgx2 > pgx1 && dgy1 < pgy2 && dgy2 > pgy1;
  };
  this.hasAttribute = function (node, attr) {
    if (node.hasAttribute) {
      return node.hasAttribute(attr);
    } else {
      return typeof node[attr] !== "undefined";
    }
  };
  this.getElementsByClassName = function (node, classname) {
    var a = [];
    var re = new RegExp("(^| )" + classname + "( |$)");
    var els = node.getElementsByTagName("*");
    for (var i = 0, j = els.length; i < j; i++) {
      if (re.test(els[i].className)) {
        a.push(els[i]);
      }
    }
    return a;
  };
  this.addScrollObject = function (id, url) {
    if (!this.scrollAttached) {
      this.scrollAttached = true;
      if (this.win.addEventListener) {
        this.win.addEventListener(
          "resize",
          window.asm_async_data.handleScroll,
          false
        );
        this.win.addEventListener(
          "scroll",
          window.asm_async_data.handleScroll,
          false
        );
        this.win.addEventListener(
          "load",
          window.asm_async_data.handleScroll,
          false
        );
      } else if (this.win.attachEvent) {
        this.win.attachEvent("onresize", window.asm_async_data.handleScroll);
        this.win.attachEvent("onscroll", window.asm_async_data.handleScroll);
        this.win.attachEvent("onload", window.asm_async_data.handleScroll);
      }
    }
    var isInList = false;
    for (var j = 0; j < this.scrollIntoObjects.length; j++) {
      if (this.scrollIntoObjects[j].id == id) {
        isInList = true;
        break;
      }
    }
    if (!isInList) {
      this.scrollIntoObjects[this.scrollIntoObjects.length] = {
        id: id,
        url: url,
        done: false,
      };
    }
    window.asm_async_data.handleScroll();
  };
  this.scrollTimer = null;
  this.scrollTimer2 = null;
  this.scrollWaiting = false;
  this.handleScroll = function () {
    if (window.asm_async_data.scrollWaiting) {
      window.asm_async_data.handleScroll2();
      return;
    }
    if (window.asm_async_data.scrollTimer) {
      window.clearTimeout(window.asm_async_data.scrollTimer);
    }
    if (window.asm_async_data.scrollTimer2) {
      window.clearTimeout(window.asm_async_data.scrollTimer2);
    }
    window.asm_async_data.scrollTimer = window.setTimeout(
      "window.asm_async_data.handleScroll2()",
      50
    );
    window.asm_async_data.scrollTimer2 = window.setTimeout(
      "window.asm_async_data.scrollWaiting = true",
      100
    );
  };
  this.handleScroll2 = function () {
    window.asm_async_data.scrollWaiting = false;
    for (var j = 0; j < window.asm_async_data.scrollIntoObjects.length; j++) {
      if (
        !window.asm_async_data.scrollIntoObjects[j].done &&
        window.asm_async_data.checkInView(
          window.asm_async_data.scrollIntoObjects[j].id
        )
      ) {
        window.asm_async_data.scrollIntoObjects[j].done = true;
        var s = document.createElement("script");
        s.src = window.asm_async_data.scrollIntoObjects[j].url;
        document
          .getElementById(window.asm_async_data.scrollIntoObjects[j].id)
          .appendChild(s);
      }
    }
  };
  this.initi = function () {
    if (
      typeof window.inDapIF == "boolean" &&
      window.inDapIF == true &&
      self != top
    ) {
      this.log("Loading in iFrame, try to get out");
      var e;
      try {
        var asm_iframe = this.fndwin(this.win.parent, window);
        if (asm_iframe != null) {
          this.win = this.win.parent;
          this.doc = this.win.document;
          var asm_ins = this.doc.createElement("ins");
          asm_ins.id = "asm_indapif_" + Math.round(Math.random() * 999999);
          this.inswrapper = asm_ins.id;
          var xy = this.asm_ds(window);
          asm_ins.style.width = xy[0] + "px";
          asm_ins.style.height = xy[1] + "px";
          asm_ins.style.display = "inline-block";
          asm_ins.style.textAlign = "left";
          asm_iframe.parentNode.insertBefore(asm_ins, asm_iframe.nextSibling);
          asm_iframe.style.display = "none";
        }
      } catch (e) {
        this.log("Loading in iFrame, try to get out failed", e);
      }
    }
    if (document.getElementsByClassName) {
      var t = document.getElementsByClassName("asm_container");
    } else {
      var t = this.getElementsByClassName(document, "asm_container");
    }
    for (var i = 0; i < t.length; i++) {
      if (
        t[i] !== null &&
        this.hasAttribute(t[i], "data-asm-host") &&
        this.hasAttribute(t[i], "data-asm-params") &&
        !this.hasAttribute(t[i], "data-asm-done")
      ) {
        var id = "p" + Math.round(Math.random() * 99999) + "x" + i;
        if (!this.hasAttribute(t[i], "id") || t[i].getAttribute("id") == "") {
          t[i].setAttribute("id", id);
        } else {
          id = t[i].getAttribute("id");
        }
        var h = t[i].getAttribute("data-asm-host");
        var p = t[i].getAttribute("data-asm-params");
        var plist = {};
        p.split("&").forEach(function (part) {
          var pn = part.substr(0, part.indexOf("="));
          var pv = part.substr(part.indexOf("=") + 1, 999999);
          plist[pn] = pv;
        });
        if (this.hasAttribute(t[i], "data-asm-set-timeout")) {
          this.settings.timeout = t[i].getAttribute("data-asm-set-timeout");
        }
        t[i].setAttribute("data-asm-done", 1);
        var fetchgdpr = false;
        if (this.hasGDPRFeedback()) {
          if (
            "gdpr_consent" in plist &&
            (plist["gdpr_consent"].substr(0, 1) == "B" ||
              plist["gdpr_consent"].substr(0, 1) == "C")
          ) {
          } else {
            p = this.removeExistingGDPRParamsFromURL(p);
            p +=
              "&gdpr=" +
              window.asm_gdpr +
              "&gdpr_consent=" +
              encodeURIComponent(window.asm_gdpr_consent);
          }
        } else if (
          this.hasAttribute(t[i], "data-asm-fetch-gdpr") ||
          "__tcfapi" in window
        ) {
          fetchgdpr = true;
        }
        var url =
          "https://" +
          h +
          "/adcontainer.php?async=" +
          id +
          "&wpcn=" +
          this.pageViewID +
          "&ref=" +
          encodeURIComponent(this.ref) +
          "&swf=" +
          this.swf +
          "&scx=" +
          screen.width +
          "&scy=" +
          screen.height +
          "&wcx=" +
          ("innerWidth" in window ? window.innerWidth : page.clientWidth) +
          "&wcy=" +
          ("innerHeight" in window ? window.innerHeight : page.clientHeight) +
          "&tz=" +
          new Date().getTime() +
          "&" +
          p;
        if (t[i].hasAttribute("data-asm-pushed")) {
          t[i].removeAttribute("data-asm-pushed");
        }
        if (fetchgdpr) {
          this.log("Wait for GDPR for container " + id, url);
          this.pushScriptGDPR(id, url);
        } else {
          this.log("Add container " + id, url);
          this.pushScript(id, url);
        }
      }
    }
    if (document.getElementsByClassName) {
      var t = document.getElementsByClassName("asm_async_creative");
    } else {
      var t = this.getElementsByClassName(document, "asm_async_creative");
    }
    for (var i = 0; i < t.length; i++) {
      if (
        t[i] !== null &&
        this.hasAttribute(t[i], "data-asm-host") &&
        this.hasAttribute(t[i], "data-asm-params") &&
        !this.hasAttribute(t[i], "data-asm-done")
      ) {
        var id = "p" + Math.round(Math.random() * 99999) + "x" + i;
        if (!this.hasAttribute(t[i], "id") || t[i].getAttribute("id") == "") {
          t[i].setAttribute("id", id);
        } else {
          id = t[i].getAttribute("id");
        }
        var h = t[i].getAttribute("data-asm-host");
        var cdn = this.hasAttribute(t[i], "data-asm-cdn")
          ? t[i].getAttribute("data-asm-cdn")
          : h;
        var p = t[i].getAttribute("data-asm-params");
        var plist = {};
        p.split("&").forEach(function (part) {
          var pn = part.substr(0, part.indexOf("="));
          var pv = part.substr(part.indexOf("=") + 1, 999999);
          plist[pn] = pv;
        });
        var r = "";
        var rc = 0;
        if (this.hasAttribute(t[i], "data-asm-click")) {
          r = t[i].getAttribute("data-asm-click");
        }
        if (this.hasAttribute(t[i], "data-asm-encode")) {
          rc = t[i].getAttribute("data-asm-encode");
        }
        if (this.hasAttribute(t[i], "data-asm-set-timeout")) {
          this.settings.timeout = t[i].getAttribute("data-asm-set-timeout");
        }
        if (this.inswrapper != "") {
          var js = this.copyObject(
            t[i],
            this.doc.getElementById(this.inswrapper)
          );
          var s = this.doc.createElement("script");
          s.src = "https://" + cdn + "/adasync.min.js";
          this.doc.getElementById(this.inswrapper).appendChild(s);
          this.fireJS(js);
        } else {
          t[i].setAttribute("data-asm-done", 1);
          var dcx = "offsetWidth" in t[i] ? t[i].offsetWidth : 0;
          if (dcx < 1) {
            if (
              "offsetParent" in t[i] &&
              typeof t[i].offsetParent == "object" &&
              t[i].offsetParent != null &&
              "offsetWidth" in t[i].offsetParent
            ) {
              dcx = t[i].offsetParent.offsetWidth;
            }
          }
          var iswp = false;
          if (this.hasAttribute(t[i], "data-asm-wallpaper")) {
            iswp = true;
            if (this.wallpaperIndex === -1) {
              this.wallpaperIndex = 1;
            } else {
              this.wallpaperIndex = 0;
            }
          }
          var fetchgdpr = false;
          if (this.hasGDPRFeedback()) {
            if (
              "gdpr_consent" in plist &&
              (plist["gdpr_consent"].substr(0, 1) == "B" ||
                plist["gdpr_consent"].substr(0, 1) == "C")
            ) {
            } else {
              p = this.removeExistingGDPRParamsFromURL(p);
              p +=
                "&gdpr=" +
                window.asm_gdpr +
                "&gdpr_consent=" +
                encodeURIComponent(window.asm_gdpr_consent);
            }
          } else if (
            this.hasAttribute(t[i], "data-asm-fetch-gdpr") ||
            "__tcfapi" in window
          ) {
            fetchgdpr = true;
          }
          var url =
            "https://" +
            h +
            "/ad" +
            (this.hasAttribute(t[i], "data-asm-responsive")
              ? "responsive"
              : "") +
            "script.php?async=" +
            id +
            "&wpcn=" +
            this.pageViewID +
            (iswp && this.wallpaperIndex === 0 ? "&wppo=0" : "") +
            (iswp && this.wallpaperIndex === 1 ? "&wppo=1" : "") +
            "&ref=" +
            encodeURIComponent(this.ref) +
            "&swf=" +
            this.swf +
            "&scx=" +
            screen.width +
            "&scy=" +
            screen.height +
            "&wcx=" +
            ("innerWidth" in window ? window.innerWidth : page.clientWidth) +
            "&wcy=" +
            ("innerHeight" in window ? window.innerHeight : page.clientHeight) +
            "&dcx=" +
            dcx +
            "&vis=" +
            this.checkVisibility(id) +
            "&tz=" +
            new Date().getTime() +
            (r != "" ? "&prdclick_" + rc + "=" + encodeURIComponent(r) : "") +
            "&" +
            p;
          if (t[i].hasAttribute("data-asm-pushed")) {
            t[i].removeAttribute("data-asm-pushed");
          }
          if (fetchgdpr) {
            this.log("Wait for GDPR for placement " + id, url);
            if (iswp && this.wallpaperIndex === 0) {
              window.setTimeout(
                'window.asm_async_data.pushScriptGDPR("' +
                  id +
                  '", "' +
                  url +
                  '");',
                50
              );
            } else {
              this.pushScriptGDPR(id, url);
            }
          } else {
            this.log("Add placement " + id, url);
            if (iswp && this.wallpaperIndex === 0) {
              window.setTimeout(
                'window.asm_async_data.pushScript("' +
                  id +
                  '", "' +
                  url +
                  '");',
                50
              );
            } else {
              this.pushScript(id, url);
            }
          }
        }
      }
    }
  };
  this.pushScript = function (id, url) {
    this.log("Loading script" + id, url);
    var t = this.doc.getElementById(id);
    if (!t) {
      return false;
    }
    if (t.hasAttribute("data-asm-pushed")) {
      return;
    }
    t.setAttribute("data-asm-pushed", 1);
    var scrollintoview = this.hasAttribute(t, "data-asm-set-timeout")
      ? t.getAttribute("data-asm-scrollintoview")
      : false;
    if (
      scrollintoview == "1" ||
      scrollintoview == "true" ||
      scrollintoview == true
    ) {
      if (!this.checkInView(id)) {
        this.addScrollObject(id, url);
        return;
      }
    }
    var s = this.doc.createElement("script");
    s.src = url;
    if (t.nodeName.toUpperCase() == "SCRIPT") {
      t.parentElement.appendChild(s);
    } else {
      t.appendChild(s);
    }
  };
  this.pushScriptGDPR = function (id, url) {
    var that = this;
    this.fetchGDPRData(function () {
      if ("asm_gdpr_feedback" in window && window.asm_gdpr_feedback) {
        url = that.removeExistingGDPRParamsFromURL(url);
        url +=
          "&gdpr=" +
          window.asm_gdpr +
          "&gdpr_consent=" +
          encodeURIComponent(window.asm_gdpr_consent);
      }
      window.asm_async_data.pushScript(id, url);
    }, id);
  };
  this.findCMPFrame = function (framename) {
    var dl = 0;
    var f = window;
    var cmp_frame = null;
    while (!cmp_frame && dl < 100) {
      dl++;
      try {
        if (f.frames[framename]) {
          cmp_frame = f;
        }
      } catch (e) {}
      if (f === window.top) {
        break;
      }
      f = f.parent;
    }
    return cmp_frame;
  };
  this.callcmp = function (c, cbnotify) {
    var callit = true;
    if (
      typeof window.__tcfapi === "function" ||
      typeof window.__tcfapi === "object"
    ) {
      window.__tcfapi("addEventListener", 2, c);
      window.asm_async_data.eventRegistered = true;
      callit = false;
    }
    if (callit) {
      var dl = 0;
      var f = window;
      window.tcfapi_frame = this.findCMPFrame("__tcfapiLocator");
      if (!("cmp_callbacks" in window)) {
        window.cmp_callbacks = {};
      }
      if (window.tcfapi_frame !== null) {
        window.__tcfapi = function (cmd, version, callback, arg) {
          if (!window.tcfapi_frame) {
            callback({ msg: "CMP not found" }, false);
            return;
          }
          var callId = Math.random() + "";
          var msg = {
            __tcfapiCall: {
              command: cmd,
              parameter: arg,
              version: version,
              callId: callId,
            },
          };
          if (!("cmp_callbacks" in window)) {
            window.cmp_callbacks = {};
          }
          window.cmp_callbacks[callId] = callback;
          window.tcfapi_frame.postMessage(msg, "*");
        };
        window.addEventListener(
          "message",
          function (event) {
            var msgIsString = typeof event.data === "string";
            try {
              var json = msgIsString ? JSON.parse(event.data) : event.data;
            } catch (e) {
              var json = null;
            }
            if (
              typeof json === "object" &&
              json !== null &&
              "__tcfapiReturn" in json
            ) {
              var i = json.__tcfapiReturn;
              if (
                i !== null &&
                "callId" in i &&
                "returnValue" in i &&
                "success" in i &&
                "cmp_callbacks" in window &&
                typeof window.cmp_callbacks[i.callId] === "function"
              ) {
                window.cmp_callbacks[i.callId](i.returnValue, i.success);
                delete window.cmp_callbacks[i.callId];
              }
            }
          },
          false
        );
        window.__tcfapi("addEventListener", 2, c);
        window.asm_async_data.eventRegistered = true;
      }
      if (window.tcfapi_frame === null) {
        if (typeof cbnotify === "function") {
          cbnotify();
        }
      }
    }
  };
  this.copyObject = function (o, target) {
    var js = "";
    if (
      o.nodeType == 1 &&
      (o.nodeName.toLowerCase() == "div" ||
        o.nodeName.toLowerCase() == "span" ||
        o.nodeName.toLowerCase() == "ins")
    ) {
      var o2 = this.doc.createElement(o.nodeName.toLowerCase());
      o2.className = o.className;
      var oid = o.id;
      o.id += "random" + Math.random() * 999999999;
      if ("style" in o) {
        for (var i in o.style) {
          try {
            o2.style[i] = o.style[i];
          } catch (err) {}
        }
      }
      if (o.attributes) {
        for (var i = 0; i < o.attributes.length; i++) {
          if (
            o.attributes[i].nodeName.substr(0, 5).toLowerCase() == "data-" ||
            o.attributes[i].nodeName.substr(0, 2).toLowerCase() == "on"
          ) {
            try {
              o2.setAttribute(o.attributes[i].nodeName, o.attributes[i].value);
            } catch (err) {
              this.log(err);
            }
          }
        }
      }
      o2.id = oid;
      var dl = 0;
      while (o.firstChild && dl < 100) {
        dl++;
        js += "\n" + this.copyObject(o.firstChild, o2);
      }
      o.parentNode.removeChild(o);
      target.appendChild(o2);
    } else if (o.nodeType == 1 && o.nodeName.toLowerCase() == "script") {
      if (o.src && o.src != "") {
        var src = o.src;
        var sid = typeof o.id == "string" ? o.id : "";
        o.parentNode.removeChild(o);
        var ns = this.writeScript(target, src, false);
        if (sid != "") {
          ns.id = sid;
        }
      } else {
        var c = o.innerHTML.trim();
        o.parentNode.removeChild(o);
        if (c.substr(0, 4) == "\x3c!--") {
          c = "//" + c.substr(4, c.length);
        }
        if (c.substr(0, 9) == "<![CDATA[") {
          c = c.substr(9, c.length);
        }
        if (c.substr(c.length - 3, 3) == "--\x3e") {
          c = c.substr(0, c.length - 3);
        }
        if (c.substr(c.length - 3, 3) == "]]>") {
          c = c.substr(0, c.length - 3);
        }
        js += "\n" + c.trim();
      }
    } else {
      target.appendChild(o);
    }
    return js;
  };
  this.write = function (id, s) {
    if (s == "") {
      return;
    }
    var o = this.doc.getElementById(id);
    if (o.nodeName.toUpperCase() == "SCRIPT") {
      o = o.parentElement;
    }
    var e = this.doc.createElement("div");
    e.innerHTML = s;
    e.style.display = "none";
    e.className = "asm_insert_div";
    var dl = 0;
    var js = "";
    while (e.firstChild && dl < 100) {
      dl++;
      if (e.firstChild.nodeType == 1) {
        js += "\n" + this.copyObject(e.firstChild, o);
      } else {
        o.appendChild(e.firstChild);
      }
    }
    this.fireJS(js);
  };
  this.fireJS = function (js) {
    if (typeof js == "string" && js.trim() !== "") {
      try {
        with (window) {
          eval(js.trim());
        }
        if (this) {
          for (nx in this) {
            if (typeof window[nx] === "undefined") {
              window[nx] = this[nx];
            }
          }
        }
      } catch (err) {
        this.log("Captured JavaScript Error: " + err);
      }
    }
  };
  this.writeScript = function (id, url, callback) {
    return this.writeScript_base(id, url, callback, false);
  };
  this.writeScript_base = function (id, url, callback, async) {
    var s, r, t;
    r = false;
    s = this.doc.createElement("script");
    s.type = "text/javascript";
    s.src = url;
    s.readySet = false;
    s.idx = id;
    s.cbk = callback;
    if (async) {
      s["async"] = true;
    }
    s.onreadystatechange = function () {
      if (
        !this.readySet &&
        (!this.readyState || this.readyState == "complete")
      ) {
        this.readySet = true;
        if (this.cbk) {
          this.cbk();
        }
      }
    };
    s.onload = function () {
      if (!this.readySet) {
        this.readySet = true;
        if (this.cbk) {
          this.cbk();
        }
      }
    };
    if (typeof id == "object" && id.appendChild) {
      id.appendChild(s);
    } else {
      this.doc.getElementById(id).appendChild(s);
    }
    return s;
  };
  this.writeFlash = function (id, o) {
    var asm_s = "";
    var swf = this.checkFlash();
    if (swf > 0) {
      if (
        typeof o.overrideclk == "string" &&
        o.overrideclk.substr(0, 4) == "http"
      ) {
        asm_s +=
          '<div style="width:' +
          o.w +
          'px; border:0px solid white; margin:0px; padding:0px; position:relative;display: inline;">';
      }
      asm_s +=
        '<span id="asmspan_' +
        o.rnd +
        '" class="asm_flash" style="font-size:1px; ' +
        (typeof o.style == "string" ? o.style : "") +
        '"><' +
        "object";
      asm_s +=
        ' data="' +
        o.swf +
        '" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"  codebase="https://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=' +
        swf +
        ',0,0,0"';
      asm_s +=
        ' id="adspiritflash' +
        o.rnd +
        '" width="' +
        o.w +
        '" height="' +
        o.h +
        '"  type="application/x-shockwave-flash">';
      asm_s += " <" + 'param name="movie" value="' + o.swf + '">';
      o.flashvars += "&id=adspiritflash" + o.rnd;
      o.flashvars += "&player_id=adspiritflash" + o.rnd;
      asm_s += " <" + 'param name="flashvars" value="' + o.flashvars + '"> ';
      asm_s += " <" + 'param name="quality" value="autohigh"> ';
      asm_s += " <" + 'param name="transparency" value="yes"> ';
      asm_s += " <" + 'param name="allowFullScreen" value="true"> ';
      asm_s += " <" + 'param name="allowScriptAccess" value="always"> ';
      asm_s += " <" + 'param name="allowNetworking" value="all"> ';
      if (o.flashvars.indexOf("wmode=opaque") != -1) {
        asm_s += " <" + 'param name="wmode" value="opaque"> ';
      } else if (o.flashvars.indexOf("wmode=window") != -1) {
        asm_s += " <" + 'param name="wmode" value="window"> ';
      } else {
        asm_s += " <" + 'param name="wmode" value="transparent"> ';
      }
      asm_s += " <" + 'param name="swLiveConnect" value="true"> ';
      asm_s +=
        " <" + 'embed src="' + o.swf + '" flashvars="' + o.flashvars + '"';
      asm_s +=
        ' allowNetworking="all" allowFullScreen="true" allowScriptAccess="always" name="adspiritflash' +
        o.rnd +
        '"';
      asm_s +=
        '  transperency="yes" swLiveConnect="true" width="' +
        o.w +
        '" height="' +
        o.h +
        '"';
      asm_s += ' quality="autohigh" type="application/x-shockwave-flash" ';
      asm_s +=
        ' pluginspage="https://www.macromedia.com/shockwave/download/index.cgi?P1_Prod_Version=ShockwaveFlash" ';
      if (o.flashvars.indexOf("wmode=opaque") != -1) {
        asm_s += ' wmode="opaque" ';
      } else if (o.flashvars.indexOf("wmode=window") != -1) {
        asm_s += ' wmode="window" ';
      } else {
        asm_s += ' wmode="transparent" ';
      }
      asm_s += ' menu="false">';
      asm_s += " </" + "embed>";
      asm_s += "</" + "object></span>";
      if (
        typeof o.overrideclk == "string" &&
        o.overrideclk.substr(0, 4) == "http"
      ) {
        asm_s +=
          '<div style="width:' +
          o.w +
          "px; height:" +
          o.h +
          'px; border:0px solid white; margin:0px; padding:0px; position:absolute; left:0px; top:0px; z-index:9999; display: inline"><a href="' +
          o.href +
          '" target="_blank"><img src="' +
          o.overrideclk +
          '" width="' +
          o.w +
          '" height="' +
          o.h +
          '" style="width:' +
          o.w +
          "px; height:" +
          o.h +
          'px;" border="0" alt=""></a></div></div>';
      }
      this.write(id, asm_s);
    } else {
      if (o.alturl != "") {
        this.write(
          id,
          '<a target="_blank" href="' +
            o.href +
            '"><img src="' +
            o.alturl +
            '" width="' +
            o.w +
            '" height="' +
            o.h +
            '" border="0" alt="' +
            o.alttext +
            '" style="width:' +
            o.w +
            "px; height:" +
            o.h +
            'px;"></a>'
        );
      }
    }
    if (o.trackown != "") {
      this.write(
        id,
        '<div style="position:absolute; left:0px; top:0px;"><img src="' +
          o.trackown +
          '" border="0" height="1" width="1"></div>'
      );
    }
    if (typeof o.swf_onafterwrite == "string" && o.swf_onafterwrite != "") {
      var asm_tmp_x_asm_swf_onafterwrite = o.swf_onafterwrite;
      o.swf_onafterwrite = "";
      eval(asm_tmp_x_asm_swf_onafterwrite + "();");
    }
  };
  this.pretargetings = new Object();
  this.startPretargeting = function (id, type, url, count, x, y, name) {
    this.log("Pretargeting started for " + id);
    var idx, ido, prefix, preid;
    idx = id.split("|");
    ido = id;
    prefix = "";
    preid = "";
    if (idx[2]) {
      id = idx[0];
      prefix = idx[1];
      preid = idx[2];
    }
    this.pretargetings[id] = {
      type: type,
      url: url,
      count: count,
      x: x,
      y: y,
      name: name,
      timer: window.setTimeout(
        "window.asm_async_data.pretargetingDone2('" + ido + "');",
        window.asm_async_data.getTimeout(1e3)
      ),
      data: "",
      active: true,
    };
  };
  this.getTimeout = function (alternative) {
    return "timeout" in window.asm_async_data.settings
      ? parseInt(window.asm_async_data.settings.timeout)
      : alternative;
  };
  this.single_pretargetings = new Object();
  this.addSingleCallPretargeting = function (id, callback) {
    this.log("Pretargeting started for single " + id);
    var idx, ido, prefix, preid;
    idx = id.split("|");
    ido = id;
    prefix = "";
    preid = "";
    if (idx[2]) {
      id = idx[0];
      prefix = idx[1];
      preid = idx[2];
    }
    if (!this.single_pretargetings[prefix + "x" + preid]) {
      this.single_pretargetings[prefix + "x" + preid] = { data: "", data2: "" };
      callback();
    } else {
      this.addPretargeting(
        ido,
        this.single_pretargetings[prefix + "x" + preid].data,
        this.single_pretargetings[prefix + "x" + preid].data2
      );
    }
  };
  this.addPretargeting = function (id, data, data2) {
    this.log("Pretargeting added for " + id);
    var idx, ido, prefix, preid;
    idx = id.split("|");
    ido = id;
    prefix = "";
    preid = "";
    if (idx[2]) {
      id = idx[0];
      prefix = idx[1];
      preid = idx[2];
    }
    this.single_pretargetings[prefix + "x" + preid] = {
      data: data,
      data2: data2,
    };
    if (this.pretargetings[id]) {
      if (data == "block") {
        this.pretargetings[id].active = false;
      }
      this.pretargetings[id].data +=
        "&ptv" + prefix + "x" + preid + "=" + encodeURIComponent(data);
      if (typeof data2 !== "undefined" && data2 !== null) {
        this.pretargetings[id].data += "&" + data2 + "&";
      }
      this.pretargetings[id].count--;
      if (this.pretargetings[id].count <= 0) {
        this.pretargetingDone(ido);
      }
    }
  };
  this.pretargetingDone2 = function (id) {
    this.log("Pretargeting TIMEOUT for " + id);
    this.pretargetingDone(id);
  };
  this.pretargetingDone = function (id) {
    this.log("Pretargeting DONE for " + id);
    var idx, ido, prefix, preid;
    idx = id.split("|");
    ido = id;
    prefix = "";
    preid = "";
    if (idx[2]) {
      id = idx[0];
      prefix = idx[1];
      preid = idx[2];
    }
    if (this.pretargetings[id]) {
      if (this.pretargetings[id].active) {
        this.pretargetings[id].active = false;
        var url =
          this.pretargetings[id].url + "&ptv=1&" + this.pretargetings[id].data;
        if (url.indexOf("'") != -1) {
          url = url.replaceAll("'", "'");
          eval("url = '" + url + "'");
        }
        if (this.pretargetings[id].type == 0) {
          this.writeScript(id, url, false);
        } else {
          this.write(
            id,
            '<iframe class="asm_paramiframe" id="asm_altifr_' +
              this.pretargetings[id].name +
              '" width="' +
              this.pretargetings[id].x +
              '" height="' +
              this.pretargetings[id].y +
              '" noresize="noresize" scrolling="no" frameborder="0" style="width:' +
              this.pretargetings[id].x +
              "px; height:" +
              this.pretargetings[id].y +
              'px; border:0px white solid !important;" marginheight="0" marginwidth="0" src="' +
              url +
              '" ALLOWTRANSPARENCY="true"></iframe>'
          );
        }
      }
    }
  };
  this.hasGDPRFeedback = function () {
    return (
      window.asm_gdpr == 0 ||
      (window.asm_gdpr_feedback &&
        window.asm_gdpr_consent != "" &&
        (window.asm_gdpr_status == "tcloaded" ||
          window.asm_gdpr_status == "useractioncomplete"))
    );
  };
  this.gdprCallbacks = [];
  this.checkInterval = 0;
  this.checkIntervalActive = false;
  this.eventRegistered = false;
  this.fetchGDPRData = function (cbnotify) {
    if (this.hasGDPRFeedback()) {
      if (typeof cbnotify == "function") {
        cbnotify();
      }
      return;
    }
    this.log(
      "Fetch GDPR data (Feedback: " +
        window.asm_gdpr_feedback +
        " / GDPR: " +
        window.asm_gdpr +
        " / Status: " +
        window.asm_gdpr_status +
        " / TCString: " +
        window.asm_gdpr_consent +
        ")"
    );
    if (window.asm_async_data.hasCookie("euconsent-v2")) {
      window.asm_gdpr = 1;
      window.asm_gdpr_consent =
        window.asm_async_data.readCookie("euconsent-v2");
      this.log("Found GDPR info in cookie euconsent-v2");
      if (window.asm_gdpr_consent != "") {
        window.asm_gdpr_feedback = true;
        if (typeof cbnotify == "function") {
          cbnotify();
        }
        cbnotify = null;
      }
    }
    if (typeof cbnotify === "function") {
      this.gdprCallbacks.push(cbnotify);
      if (!this.checkIntervalActive) {
        this.checkIntervalActive = true;
        this.checkInterval = window.setInterval(
          window.asm_async_data.checkGDPRFeedback,
          50
        );
        window.setTimeout(
          window.asm_async_data.cancelGDPRCheck,
          window.asm_async_data.getTimeout(1e3)
        );
      }
    }
    if (window.asm_async_data.eventRegistered) {
      return;
    }
    window.asm_async_data.callcmp(function (val, success) {
      if (
        success &&
        val !== null &&
        "tcString" in val &&
        "eventStatus" in val &&
        (val["eventStatus"] == "tcloaded" ||
          val["eventStatus"] == "useractioncomplete")
      ) {
        window.asm_async_data.log("CMP feedback", val);
        window.asm_gdpr_feedback = true;
        window.asm_gdpr =
          typeof val["gdprApplies"] === "boolean"
            ? val["gdprApplies"]
              ? 1
              : 0
            : typeof val["gdprApplies"] === "number"
            ? val["gdprApplies"] === 1
              ? 1
              : 0
            : 1;
        window.asm_gdpr_status =
          typeof val["eventStatus"] === "string" ? val["eventStatus"] : "";
        if ("tcString" in val) {
          window.asm_gdpr_consent =
            typeof val["tcString"] === "string" ? val["tcString"] : "";
        }
        window.asm_async_data.checkGDPRFeedback();
      }
    }, cbnotify);
  };
  this.checkGDPRFeedback = function () {
    if (window.asm_async_data.hasGDPRFeedback()) {
      window.asm_async_data.log("GDPR feedback present, canceling wait");
      window.asm_async_data.cancelGDPRCheck2();
    }
  };
  this.cancelGDPRCheck = function () {
    window.asm_async_data.log("GDPR wait timeout");
    window.asm_async_data.cancelGDPRCheck2();
  };
  this.cancelGDPRCheck2 = function () {
    window.clearInterval(window.asm_async_data.checkInterval);
    window.asm_async_data.checkIntervalActive = false;
    window.asm_async_data.callGDPRCallbacks();
  };
  this.callGDPRCallbacks = function () {
    for (var i = 0; i < this.gdprCallbacks.length; i++) {
      this.gdprCallbacks[i]();
    }
    this.gdprCallbacks = [];
  };
  this.setRefresh = function (id, time, newparams) {
    window.setTimeout(
      "window.asm_async_data.refreshSlot('" + id + "','" + newparams + "')",
      time * 1e3
    );
  };
  this.refreshSlot = function (id, newparams) {
    if (typeof newparams == "undefined" || newparams === null) {
      newparams = "";
    }
    var d = document.getElementById(id);
    if (d) {
      this.log("Refresh slot " + id);
      d.innerHTML = "";
      newparams += "&tz=" + new Date().getTime();
      d.removeAttribute("data-asm-done");
      newparams = newparams
        .substr(newparams.indexOf("?") + 1, 99999)
        .split("&");
      var oldparams = d.getAttribute("data-asm-params").split("&");
      for (var i = 0; i < newparams.length; i++) {
        var addparam = true;
        var pn = newparams[i].substr(0, newparams[i].indexOf("="));
        for (var j = 0; j < oldparams.length; j++) {
          if (oldparams[j].substr(0, oldparams[j].indexOf("=")) == pn) {
            oldparams[j] = newparams[i];
            addparam = false;
            break;
          }
        }
        if (addparam) {
          oldparams.push(newparams[i]);
        }
      }
      d.setAttribute("data-asm-params", oldparams.join("&"));
    }
    window.asm_async_data.initi();
  };
  this.removeExistingGDPRParamsFromURL = function (url) {
    url = url.replace(/(&gdpr_consent|&gdpr)=[^&]*/g, "");
    return url;
  };
}
if (typeof String.prototype.trim !== "function") {
  String.prototype.trim = function () {
    return this.replace(/^\s+|\s+$/g, "");
  };
}
if (typeof window.asm_async_data != "object") {
  window.asm_async_data = new asm_async_obj();
}
window.asm_async_data.initi();
if (!("asm_gdpr" in window)) {
  window.asm_gdpr_feedback = false;
  window.asm_gdpr = 1;
  window.asm_gdpr_consent = "";
  window.asm_gdpr_status = "";
}
window.asm_async_data.fetchGDPRData(null);
asm_findAdspritIFrames = function (scope) {
  var allFrames = scope.querySelectorAll("iframe"),
    outFrames = [];
  for (var frame, i = allFrames.length; i--; ) {
    frame = allFrames[i];
    try {
      var found = frame.contentWindow.document.querySelectorAll(
        'iframe[id^="adspirit"]'
      );
      if (found.length) outFrames.push(found[0]);
    } catch (e) {}
  }
  return outFrames;
};
asm_findIFrames = function () {
  var scopes = [window.document],
    i,
    iframes = [],
    tmp;
  tmp = window;
  while (tmp != tmp.parent) {
    try {
      scopes.push(tmp.parent.document);
    } catch (e) {}
    tmp = tmp.parent;
  }
  for (i = scopes.length; i--; ) {
    try {
      tmp = asm_findAdspritIFrames(scopes[i]);
      for (var j = tmp.length; j--; ) {
        if (iframes.indexOf(tmp[j]) === -1) iframes.push(tmp[j]);
      }
    } catch (e) {}
  }
  return iframes;
};
window.addEventListener("message", ({ data }) => {
  var iframes = asm_findIFrames();
  for (var i = iframes.length; i--; ) {
    try {
      iframes[i].contentWindow.postMessage(data, "*");
    } catch (e) {}
  }
});
