const puppeteer = require('puppeteer');

// Hardcoded test data to simulate jobs from the queue
const testJobs = [
  { data: { url: 'https://helenashandarbeit.com/2024/11/13/einfachen-mohairpullover-stricken/' } },
  { data: { url: 'https://helenashandarbeit.com/2022/02/07/esstisch-aus-alten-holzbohlen-selber-bauen/' } },
  // Add more test URLs as needed
];

const calculateHeadingDistances = () => {
  // Selektiere alle relevanten Elemente in Erscheinungsreihenfolge
  const elements = Array.from(document.querySelectorAll("h1, h2, h3, h4, h5, h6, p"));

  // Extrahiere Position und Typinformationen
  const getElementDetails = el => {
    const rect = el.getBoundingClientRect();
    const scrollTop = window.scrollY || window.pageYOffset;
  
    return {
      tag: el.tagName,
      text: el.textContent.trim(),
      top: rect.top + scrollTop,
      bottom: rect.bottom + scrollTop,
      isHeading: el.tagName.startsWith('H')
    };
  };
  

  const elementDetails = elements.map(getElementDetails);

  // Sortiere Elemente nach vertikaler Position im Viewport
  elementDetails.sort((a, b) => a.top - b.top);

  const result = [];
  let currentHeading = null;
  let currentSectionElements = [];

  for (let i = 0; i < elementDetails.length; i++) {
    const el = elementDetails[i];

    if (el.isHeading) {
      if (currentHeading) {
        const sectionEnd = currentSectionElements.length > 0
          ? currentSectionElements[currentSectionElements.length - 1].bottom
          : currentHeading.bottom;

        const sectionHeight = sectionEnd - currentHeading.top;
        const distanceToNextHeading = el.top - currentHeading.bottom;

        result.push({
          heading: {
            tag: currentHeading.tag,
            text: currentHeading.text,
            position: currentHeading.top
          },
          elements: currentSectionElements,
          distanceToNextHeading,
          sectionHeight
        });
      }

      currentHeading = el;
      currentSectionElements = [];
    } else {
      currentSectionElements.push({
        tag: el.tag,
        text: el.text,
        position: el.top,
        bottom: el.bottom
      });
    }

    // Letztes Element: finalisiere die letzte Section
    const isLastElement = i === elementDetails.length - 1;
    if (isLastElement && currentHeading) {
      const sectionEnd = currentSectionElements.length > 0
        ? currentSectionElements[currentSectionElements.length - 1].bottom
        : currentHeading.bottom;

      const sectionHeight = sectionEnd - currentHeading.top;

      result.push({
        heading: {
          tag: currentHeading.tag,
          text: currentHeading.text,
          position: currentHeading.top
        },
        elements: currentSectionElements,
        distanceToNextHeading: null,
        sectionHeight
      });
    }
  }

  return result;
};

const acceptCookieConsent = async (page) => {
  try {
    // Wait for the container that hosts the shadow root
    await page.waitForSelector('#cmpwrapper', { timeout: 5000 });

    const shadowHost = await page.$('#cmpwrapper');
    const shadowRootHandle = await shadowHost.evaluateHandle(el => el.shadowRoot);
    if(!shadowRootHandle) return -2;

    // Try to find the button and wait for it to be available
    const acceptButtonHandle = await page.waitForFunction(
      (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
      { timeout: 5000 },
      shadowRootHandle
    );

    if (!acceptButtonHandle) {
      console.log('❌ Consent button not found in shadow root');
      return -3;
    }
    const button = acceptButtonHandle.asElement();

    if (button) {
      await button.click();
      console.log('✅ Consent button clicked');
      return 1;
    } else {
      console.log('❌ Consent button not found as element');
      return 0;
    }
  } catch (err) {
    console.error('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};


const groupAdsByResponsive = (qmn) => {
  if (!qmn || !qmn.adSlots || !Array.isArray(qmn.adSlots)) {
    console.log('QMN-Objekt oder qmn.adSlots ist nicht im erwarteten Format.');
    return {};
  }

  const acc = qmn.adSlots.reduce((acc, adSlot) => {
    const { id, responsive, type } = adSlot;

    // Anzeigen vom Typ 'tracker' und Slots ohne ID/Responsive ignorieren
    if (!id || !responsive || type === 'tracker') {
      return acc;
    }

    // Sicherstellen, dass das Array für den Gerätetyp existiert
    if (!acc[responsive]) {
      acc[responsive] = [];
    }

    // Anzeigen-Objekt zur Liste hinzufügen
    acc[responsive].push({ id, type });

    return acc;
  }, {});

  if (qmn.watchbetter) {
    acc.mobile.push({ id: 'watchbetter-embed', type: 'watchbetter' });
    acc.desktop.push({ id: 'watchbetter-embed', type: 'watchbetter' });   
  }

  return acc
};



const checkSelectors = async (page) => {
  const qmnSelectors = await page.evaluate(() => {
    return window.qmn;
  });

  console.log( 'QMN Selectors:' ,JSON.stringify(qmnSelectors, null, 2))

  if(!qmnSelectors) {
    console.log('⚠️ window.qmn-Objekt nicht auf der Seite gefunden.');
    return null;
  }

  return qmnSelectors.config
}

const viewports = {
  mobile: { width: 375, height: 812, deviceType: 'mobile', name: 'Mobile' },
  tablet: { width: 768, height: 1024, deviceType: 'desktop', name: 'Tablet' },
  desktop: { width: 1920, height: 1080, deviceType: 'desktop', name: 'Desktop HD' },
};


async function processJob(job) {
  const url = job.data.url;
  console.log(`📥 Starte Analyse: ${url}`);

  const startTime = Date.now();

  const browser = await puppeteer.launch({
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  const page = await browser.newPage();

  try {
    await page.goto(url, { waitUntil: 'networkidle2' });

    // Accept cookie consent if present
    const cookieConsentAccepted = await acceptCookieConsent(page);
    if (cookieConsentAccepted !== 1) {
      throw new Error('Cookie-Zustimmung fehlgeschlagen oder nicht gefunden: ' + cookieConsentAccepted);
    }
    console.log('Cookie-Zustimmung akzeptiert, warte auf mögliche Overlays...');
    await new Promise(resolve => setTimeout(resolve, 2000)); 

    const qmnSelectors = await checkSelectors(page);
    const adsByResponsive = groupAdsByResponsive(qmnSelectors);

    const missingAds = {
        desktop: new Map(),
        mobile: new Map()
    };
    let allAdsSuccessfullyPlaced = true;

    for (const [deviceName, config] of Object.entries(viewports)) {
      console.log(`\n--- Teste Gerät: ${config.name} (${config.width}x${config.height}) ---`);

      await page.setViewport({ width: config.width, height: config.height });
      await page.reload({ waitUntil: 'networkidle2' });

      // Scroll down to trigger lazy-loaded ads
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await new Promise(resolve => setTimeout(resolve, 2000));

      const responsiveKey = config.deviceType; // 'desktop' or 'mobile'
      const adsToCheck = adsByResponsive[responsiveKey] || [];
      console.log(`[${deviceName}] Überprüfe folgende Anzeigen für Responsive-Typ '${responsiveKey}':`, JSON.stringify(adsToCheck, null, 2));

      if (adsToCheck.length === 0) {
          console.log(`[${deviceName}] Keine Anzeigen für den Gerätetyp '${responsiveKey}' gefunden.`);
          continue;
      }

      const findMissingAds = (ads) => {
          const missing = [];
          for (const ad of ads) {
              const adContainer = document.getElementById(`qmn${ad.id}`);
              if (!adContainer) {
                  missing.push(ad);
              }
          }
          return missing;
      };

      const missingAdsForDevice = await page.evaluate(findMissingAds, adsToCheck);
      
      if (missingAdsForDevice.length > 0) {
          allAdsSuccessfullyPlaced = false;
          console.log(`[${deviceName}] Fehlende Anzeigen:`, missingAdsForDevice);
          missingAdsForDevice.forEach(ad => missingAds[responsiveKey].set(ad.id, ad));
      } else {
          console.log(`[${deviceName}] Alle ${adsToCheck.length} Anzeigen wurden erfolgreich gefunden.`);
      }
    }

    const endTime = Date.now();
    const durationMs = endTime - startTime;
    
    let finalResult;

    if (allAdsSuccessfullyPlaced) {
        finalResult = {
            success: true,
            url,
            timestamp: new Date().toISOString(),
            processingTimeMs: durationMs
        };
    } else {
        finalResult = {
            success: false,
            missingAds: {
                desktop: Array.from(missingAds.desktop.values()),
                mobile: Array.from(missingAds.mobile.values())
            },
            url,
            timestamp: new Date().toISOString(),
            processingTimeMs: durationMs
        };
    }

    console.log("final result 📈 :" ,JSON.stringify(finalResult, null, 2))
  
    return finalResult

  } catch (e) {
    console.error(`❌ Fehler bei ${url}:`, e.message);
    return {
        url,
        error: e.message,
        timestamp: new Date().toISOString(),
    }
  } finally {
    await browser.close();
  }
}

async function runTests() {
  for (const job of testJobs) {
    console.log(`\n--- Processing Test Job for URL: ${job.data.url} ---`);
    const result = await processJob(job);
    console.log(`Result for ${job.data.url}:`, JSON.stringify(result, null, 2));
  }
}

runTests();